import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Alert, Typography, Divider, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { register, selectAuthLoading, selectAuthError } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';
import Logo from '../../components/common/Logo';

const { Title, Text, Paragraph } = Typography;

/**
 * 注册页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const RegisterPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const [form] = Form.useForm();
  
  /**
   * 处理表单提交
   * 
   * @param {Object} values - 表单值
   */
  const handleSubmit = async (values) => {
    try {
      const { confirmPassword, agreement, ...userData } = values;
      await dispatch(register(userData)).unwrap();
      navigate(ROUTES.HOME, { replace: true });
    } catch (err) {
      console.error('Registration failed:', err);
    }
  };
  
  return (
    <div className="register-page" style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
      <div className="register-logo" style={{ textAlign: 'center', marginBottom: 24 }}>
        <Logo size="large" />
        <Title level={2} style={{ marginTop: 16 }}>注册</Title>
        <Text type="secondary">创建您的AI证件照生成器账号</Text>
      </div>
      
      {error && (
        <Alert
          message="注册失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false} style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)' }}>
        <Form
          form={form}
          name="register"
          onFinish={handleSubmit}
          size="large"
          layout="vertical"
          scrollToFirstError
        >
          <Form.Item
            name="name"
            rules={[{ required: true, message: '请输入您的姓名' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="姓名"
              autoComplete="name"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入您的邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="邮箱"
              autoComplete="email"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入您的密码' },
              { min: 8, message: '密码长度至少为8个字符' },
            ]}
            hasFeedback
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="new-password"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            hasFeedback
            rules={[
              { required: true, message: '请确认您的密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="确认密码"
              autoComplete="new-password"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              {
                validator: (_, value) =>
                  value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意用户协议和隐私政策')),
              },
            ]}
          >
            <Checkbox disabled={loading}>
              我已阅读并同意 <Link to="/terms">用户协议</Link> 和 <Link to="/privacy">隐私政策</Link>
            </Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="btn-primary"
            >
              注册
            </Button>
          </Form.Item>
          
          <Divider>
            <Text type="secondary">或者</Text>
          </Divider>
          
          <div style={{ textAlign: 'center' }}>
            <Text>已有账号？</Text>{' '}
            <Link to={ROUTES.LOGIN}>立即登录</Link>
          </div>
        </Form>
      </Card>
      
      <Paragraph type="secondary" style={{ textAlign: 'center', marginTop: 24 }}>
        注册即表示您同意我们的服务条款和隐私政策。
      </Paragraph>
    </div>
  );
};

export default RegisterPage;