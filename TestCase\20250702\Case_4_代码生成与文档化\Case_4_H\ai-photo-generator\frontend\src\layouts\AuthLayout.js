import React, { useEffect } from 'react';
import { Outlet, Link } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Layout, Typography, ConfigProvider, theme as antdTheme } from 'antd';
import styled from 'styled-components';
import { setTheme } from '../store/uiSlice';
import { THEME_MODES, ROUTES } from '../utils/constants';
import ThemeSwitch from '../components/common/ThemeSwitch';

const { Header, Content, Footer } = Layout;
const { Title } = Typography;

/**
 * 样式化组件
 */
const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledHeader = styled(Header)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: ${({ theme }) => theme.colorBgContainer};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
`;

const Logo = styled.img`
  height: 40px;
  margin-right: 12px;
`;

const StyledContent = styled(Content)`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
`;

const ContentWrapper = styled.div`
  width: 100%;
  max-width: 420px;
  padding: 24px;
  background: ${({ theme }) => theme.colorBgContainer};
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
`;

const StyledFooter = styled(Footer)`
  text-align: center;
  background: transparent;
`;

const FooterLinks = styled.div`
  margin-top: 12px;
  
  a {
    margin: 0 8px;
    color: ${({ theme }) => theme.colorTextSecondary};
    
    &:hover {
      color: ${({ theme }) => theme.colorPrimary};
    }
  }
`;

/**
 * 认证布局组件
 * 
 * 用于登录、注册等认证页面的布局
 * 
 * @returns {JSX.Element} 布局组件
 */
const AuthLayout = () => {
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const { currentTheme } = useSelector((state) => state.ui);
  
  // 从localStorage获取主题设置
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme && Object.values(THEME_MODES).includes(savedTheme)) {
      dispatch(setTheme(savedTheme));
    }
  }, [dispatch]);
  
  // 根据当前主题选择antd主题算法
  const themeAlgorithm = currentTheme === THEME_MODES.DARK 
    ? antdTheme.darkAlgorithm 
    : antdTheme.defaultAlgorithm;
  
  return (
    <ConfigProvider
      theme={{
        algorithm: themeAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <StyledLayout>
        <StyledHeader>
          <LogoContainer>
            <Logo src="/logo.png" alt="AI照片生成器" />
            <Title level={4} style={{ margin: 0 }}>AI照片生成器</Title>
          </LogoContainer>
          <ThemeSwitch />
        </StyledHeader>
        
        <StyledContent>
          <ContentWrapper>
            <Outlet />
          </ContentWrapper>
        </StyledContent>
        
        <StyledFooter>
          <Typography.Text>© {new Date().getFullYear()} AI照片生成器 版权所有</Typography.Text>
          <FooterLinks>
            <Link to={ROUTES.PRIVACY}>隐私政策</Link>
            <Link to={ROUTES.TERMS}>服务条款</Link>
            <Link to={ROUTES.HELP}>帮助中心</Link>
          </FooterLinks>
        </StyledFooter>
      </StyledLayout>
    </ConfigProvider>
  );
};

export default AuthLayout;