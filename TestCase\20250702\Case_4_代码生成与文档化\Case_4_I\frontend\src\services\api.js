import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_URL,
  timeout: 30000, // 30秒超时
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      // 可以在这里添加重定向到登录页的逻辑
    }
    return Promise.reject(error);
  }
);

// 用户相关API
export const userApi = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (username, email, password) => api.post('/auth/register', { username, email, password }),
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData) => api.put('/users/profile', userData),
};

// 上传相关API
export const uploadApi = {
  uploadPhoto: (file, spec) => {
    const formData = new FormData();
    formData.append('file', file);
    if (spec) formData.append('spec', spec);
    return api.post('/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadMultiple: (files, spec) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
    if (spec) formData.append('spec', spec);
    return api.post('/upload/multiple', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
};

// 生成相关API
export const generationApi = {
  generateIdPhoto: (params) => api.post('/generate', params),
  getTaskStatus: (taskId) => api.get(`/task/${taskId}`),
  downloadPhoto: (photoId) => api.get(`/download/${photoId}`, { responseType: 'blob' }),
  downloadMultiple: (photoIds) => api.post('/download/batch', { photo_ids: photoIds }, { responseType: 'blob' }),
};

export default api;