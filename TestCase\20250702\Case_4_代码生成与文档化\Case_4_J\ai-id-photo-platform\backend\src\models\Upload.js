const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Upload = sequelize.define('Upload', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'user_id',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  originalFilename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'original_filename'
  },
  filePath: {
    type: DataTypes.STRING(500),
    allowNull: false,
    field: 'file_path'
  },
  fileSize: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'file_size',
    validate: {
      min: 0,
      max: 10485760 // 10MB
    }
  },
  fileType: {
    type: DataTypes.STRING(20),
    allowNull: false,
    field: 'file_type',
    validate: {
      isIn: [['image/jpeg', 'image/jpg', 'image/png']]
    }
  },
  uploadTime: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'upload_time'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '图片元数据，如尺寸、EXIF信息等'
  },
  status: {
    type: DataTypes.ENUM('uploaded', 'processing', 'completed', 'failed', 'deleted'),
    defaultValue: 'uploaded'
  }
}, {
  tableName: 'uploads',
  timestamps: false
});

module.exports = Upload;
