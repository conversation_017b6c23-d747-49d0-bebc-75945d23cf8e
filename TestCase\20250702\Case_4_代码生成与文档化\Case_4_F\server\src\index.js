const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 导入路由
const uploadRoutes = require('./routes/uploadRoutes');
const photoRoutes = require('./routes/photoRoutes');
const userRoutes = require('./routes/userRoutes');
const authRoutes = require('./routes/authRoutes');

// 导入数据库连接
const db = require('./config/database');

// 初始化Express应用
const app = express();

// 基本中间件
app.use(helmet()); // 安全HTTP头
app.use(cors()); // 跨域资源共享
app.use(morgan('dev')); // 日志记录
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码请求体

// 静态文件服务
app.use('/public', express.static(path.join(__dirname, '../public')));

// 路由
app.use('/api/upload', uploadRoutes);
app.use('/api/generate', photoRoutes);
app.use('/api/task', photoRoutes);
app.use('/api/download', photoRoutes);
app.use('/api/user', userRoutes);
app.use('/api/auth', authRoutes);

// 首页路由
app.get('/', (req, res) => {
  res.json({ message: 'AI证件照生成平台API服务' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'production' ? null : err.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: '未找到请求的资源'
  });
});

// 数据库连接测试
db.authenticate()
  .then(() => console.log('数据库连接成功'))
  .catch(err => console.error('数据库连接失败:', err));

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
}); 