import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Divider, message } from 'antd';
import { UserOutlined, LockOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login } from '../store/authSlice';
import { loginUser } from '../services/authService';
import { ROUTES } from '../utils/constants';

const { Title, Paragraph, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  // 获取重定向路径，如果没有则默认为首页
  const from = location.state?.from?.pathname || ROUTES.HOME;

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const response = await loginUser(values.username, values.password);
      
      // 登录成功，更新Redux状态
      dispatch(login({
        user: response.user,
        token: response.token,
      }));
      
      message.success('登录成功！');
      
      // 重定向到之前尝试访问的页面或首页
      navigate(from, { replace: true });
    } catch (error) {
      console.error('登录失败:', error);
      message.error(error.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider) => {
    // 实际项目中，这里会实现社交媒体登录逻辑
    message.info(`${provider}登录功能正在开发中...`);
  };

  return (
    <div className="login-page">
      <Card className="login-card">
        <div className="login-header">
          <Title level={2}>欢迎回来</Title>
          <Paragraph>登录您的账号以继续使用</Paragraph>
        </div>
        
        <Form
          name="login"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名或邮箱' }]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="用户名或邮箱" 
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>
          
          <Form.Item>
            <div className="login-form-options">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <Link to={ROUTES.FORGOT_PASSWORD} className="login-form-forgot">
                忘记密码?
              </Link>
            </div>
          </Form.Item>
          
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              className="login-form-button"
              loading={loading}
              block
            >
              登录
            </Button>
          </Form.Item>
          
          <div className="login-form-register">
            <Text>还没有账号? </Text>
            <Link to={ROUTES.REGISTER}>立即注册</Link>
          </div>
        </Form>
        
        <Divider plain>或使用以下方式登录</Divider>
        
        <div className="social-login">
          <Button 
            icon={<GoogleOutlined />} 
            onClick={() => handleSocialLogin('Google')}
            className="google-login-btn"
          >
            Google登录
          </Button>
          <Button 
            icon={<FacebookOutlined />} 
            onClick={() => handleSocialLogin('Facebook')}
            className="facebook-login-btn"
          >
            Facebook登录
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Login;