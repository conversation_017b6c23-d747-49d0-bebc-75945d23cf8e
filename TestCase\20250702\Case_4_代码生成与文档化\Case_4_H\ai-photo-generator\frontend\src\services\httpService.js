import axios from 'axios';
import { message } from 'antd';
import { STORAGE_KEYS, API_ENDPOINTS, ERROR_MESSAGES } from '../utils/constants';

/**
 * 创建axios实例
 */
const http = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30秒超时
});

/**
 * 请求拦截器
 * 添加认证token到请求头
 */
http.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 处理常见错误，如token过期、网络错误等
 */
http.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // 处理网络错误
    if (!error.response) {
      message.error(ERROR_MESSAGES.NETWORK_ERROR);
      return Promise.reject(error);
    }
    
    // 处理401错误（未授权）
    if (
      error.response.status === 401 &&
      !originalRequest._retry &&
      originalRequest.url !== API_ENDPOINTS.REFRESH_TOKEN &&
      originalRequest.url !== API_ENDPOINTS.LOGIN
    ) {
      originalRequest._retry = true;
      
      try {
        // 尝试刷新token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (!refreshToken) {
          // 如果没有刷新token，清除登录状态并跳转到登录页
          httpService.clearAuth();
          window.location.href = '/login';
          return Promise.reject(error);
        }
        
        const response = await http.post(API_ENDPOINTS.REFRESH_TOKEN, {
          refreshToken,
        });
        
        // 保存新token
        const { token } = response.data;
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        
        // 使用新token重试原请求
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return http(originalRequest);
      } catch (refreshError) {
        // 刷新token失败，清除登录状态并跳转到登录页
        httpService.clearAuth();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    // 处理其他常见错误
    switch (error.response.status) {
      case 400:
        // 处理验证错误
        if (error.response.data.errors) {
          const errorMessages = Object.values(error.response.data.errors).flat();
          errorMessages.forEach(msg => message.error(msg));
        } else {
          message.error(error.response.data.message || ERROR_MESSAGES.VALIDATION_ERROR);
        }
        break;
      case 403:
        message.error(ERROR_MESSAGES.FORBIDDEN);
        break;
      case 404:
        message.error(ERROR_MESSAGES.NOT_FOUND);
        break;
      case 500:
        message.error(ERROR_MESSAGES.SERVER_ERROR);
        break;
      default:
        // 处理其他错误
        message.error(error.response.data.message || '请求失败，请稍后再试');
    }
    
    return Promise.reject(error);
  }
);

/**
 * HTTP服务
 * 提供HTTP请求方法和工具函数
 */
const httpService = {
  /**
   * 发送GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 查询参数
   * @param {Object} config - axios配置
   * @returns {Promise} 响应Promise
   */
  get: (url, params = {}, config = {}) => {
    return http.get(url, { params, ...config });
  },
  
  /**
   * 发送POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - axios配置
   * @returns {Promise} 响应Promise
   */
  post: (url, data = {}, config = {}) => {
    return http.post(url, data, config);
  },
  
  /**
   * 发送PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - axios配置
   * @returns {Promise} 响应Promise
   */
  put: (url, data = {}, config = {}) => {
    return http.put(url, data, config);
  },
  
  /**
   * 发送DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} config - axios配置
   * @returns {Promise} 响应Promise
   */
  delete: (url, config = {}) => {
    return http.delete(url, config);
  },
  
  /**
   * 上传文件
   * @param {string} url - 请求URL
   * @param {FormData} formData - 表单数据
   * @param {Function} onProgress - 进度回调函数
   * @returns {Promise} 响应Promise
   */
  upload: (url, formData, onProgress) => {
    return http.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });
  },
  
  /**
   * 下载文件
   * @param {string} url - 请求URL
   * @param {Object} params - 查询参数
   * @param {string} filename - 保存的文件名
   * @returns {Promise} 响应Promise
   */
  download: async (url, params = {}, filename) => {
    const response = await http.get(url, {
      params,
      responseType: 'blob',
    });
    
    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    
    // 从响应头或参数中获取文件名
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = filename;
    
    if (!downloadFilename && contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch.length === 2) {
        downloadFilename = filenameMatch[1];
      }
    }
    
    if (downloadFilename) {
      link.download = downloadFilename;
    }
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);
    
    return response;
  },
  
  /**
   * 清除认证信息
   */
  clearAuth: () => {
    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
  },
  
  /**
   * 获取完整的API URL
   * @param {string} path - API路径
   * @returns {string} 完整URL
   */
  getApiUrl: (path) => {
    const baseUrl = process.env.REACT_APP_API_URL || '';
    return `${baseUrl}${path}`;
  },
};

export default httpService;