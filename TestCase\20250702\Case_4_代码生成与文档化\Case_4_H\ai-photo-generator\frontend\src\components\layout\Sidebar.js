import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Layout, Menu, Typography, Badge } from 'antd';
import {
  DashboardOutlined,
  PictureOutlined,
  PlusCircleOutlined,
  HeartOutlined,
  CrownOutlined,
  UserOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import { ROUTES } from '../../utils/constants';

const { Sider } = Layout;
const { Title } = Typography;

/**
 * 样式化组件
 */
const StyledSider = styled(Sider)`
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
  
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
  }
  
  @media (max-width: 768px) {
    display: ${({ $mobileVisible }) => ($mobileVisible ? 'block' : 'none')};
  }
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: ${({ $collapsed }) => ($collapsed ? 'center' : 'flex-start')};
  padding: ${({ $collapsed }) => ($collapsed ? '16px 0' : '16px 24px')};
  height: 64px;
  overflow: hidden;
`;

const Logo = styled.img`
  height: 32px;
  margin-right: ${({ $collapsed }) => ($collapsed ? '0' : '12px')};
`;

const MenuContainer = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const UpgradeContainer = styled.div`
  padding: 16px;
  margin: 16px;
  background: ${({ theme }) => theme.colorPrimaryBg};
  border-radius: 8px;
  text-align: center;
  display: ${({ $collapsed }) => ($collapsed ? 'none' : 'block')};
`;

/**
 * 侧边栏组件
 * 
 * 显示在应用的左侧，包含导航菜单和其他侧边栏元素
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.collapsed - 侧边栏是否折叠
 * @returns {JSX.Element} 侧边栏组件
 */
const Sidebar = ({ collapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从Redux获取状态
  const { mobileMenuVisible } = useSelector((state) => state.ui);
  const { userSubscription } = useSelector((state) => state.subscription);
  
  // 本地状态
  const [selectedKeys, setSelectedKeys] = useState([]);
  
  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const mainPath = `/${pathParts[1]}`;
    
    // 特殊处理一些路径
    if (mainPath === '/photo' && pathParts[2] === 'create') {
      setSelectedKeys([ROUTES.CREATE_PHOTO]);
    } else if (mainPath === '/photo' && pathParts[2]) {
      setSelectedKeys([ROUTES.PHOTOS]);
    } else {
      setSelectedKeys([mainPath]);
    }
  }, [location.pathname]);
  
  // 判断是否有高级订阅
  const hasPremiumSubscription = userSubscription && 
    userSubscription.status === 'active' && 
    userSubscription.planType !== 'free';
  
  // 菜单项
  const menuItems = [
    {
      key: ROUTES.DASHBOARD,
      icon: <DashboardOutlined />,
      label: '仪表盘',
      onClick: () => navigate(ROUTES.DASHBOARD),
    },
    {
      key: 'photos',
      icon: <PictureOutlined />,
      label: '我的照片',
      children: [
        {
          key: ROUTES.PHOTOS,
          label: '所有照片',
          onClick: () => navigate(ROUTES.PHOTOS),
        },
        {
          key: ROUTES.CREATE_PHOTO,
          icon: <PlusCircleOutlined />,
          label: '创建新照片',
          onClick: () => navigate(ROUTES.CREATE_PHOTO),
        },
        {
          key: ROUTES.FAVORITES,
          icon: <HeartOutlined />,
          label: '收藏',
          onClick: () => navigate(ROUTES.FAVORITES),
        },
      ],
    },
    {
      key: ROUTES.SUBSCRIPTION,
      icon: hasPremiumSubscription ? <StarOutlined /> : <CrownOutlined />,
      label: (
        <Badge dot={!hasPremiumSubscription}>
          订阅
        </Badge>
      ),
      onClick: () => navigate(ROUTES.SUBSCRIPTION),
    },
    {
      type: 'divider',
    },
    {
      key: ROUTES.PROFILE,
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate(ROUTES.PROFILE),
    },
    {
      key: ROUTES.SETTINGS,
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate(ROUTES.SETTINGS),
    },
    {
      key: ROUTES.HELP,
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      onClick: () => navigate(ROUTES.HELP),
    },
  ];
  
  return (
    <StyledSider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={220}
      theme="light"
      $mobileVisible={mobileMenuVisible}
    >
      <LogoContainer $collapsed={collapsed}>
        <Logo src="/logo.png" alt="AI照片生成器" $collapsed={collapsed} />
        {!collapsed && <Title level={4} style={{ margin: 0 }}>AI照片生成器</Title>}
      </LogoContainer>
      
      <MenuContainer>
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </MenuContainer>
      
      {!hasPremiumSubscription && (
        <UpgradeContainer $collapsed={collapsed}>
          <Typography.Title level={5} style={{ color: '#1890ff', margin: '0 0 8px' }}>
            升级到高级版
          </Typography.Title>
          <Typography.Paragraph style={{ fontSize: '12px', marginBottom: '12px' }}>
            解锁更多高级功能和生成额度
          </Typography.Paragraph>
          <Menu.Item
            key="upgrade"
            icon={<CrownOutlined />}
            onClick={() => navigate(ROUTES.SUBSCRIPTION)}
            style={{
              borderRadius: '4px',
              backgroundColor: '#1890ff',
              color: '#fff',
            }}
          >
            立即升级
          </Menu.Item>
        </UpgradeContainer>
      )}
    </StyledSider>
  );
};

export default Sidebar;