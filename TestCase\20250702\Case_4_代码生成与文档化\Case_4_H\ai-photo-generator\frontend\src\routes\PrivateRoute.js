import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Spin } from 'antd';
import { fetchUserProfile } from '../store/authSlice';
import { ROUTES, STORAGE_KEYS } from '../utils/constants';

/**
 * 私有路由守卫
 * 
 * 用于保护需要认证的路由
 * 如果用户未登录，重定向到登录页面
 * 
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 子组件
 * @returns {JSX.Element} 路由组件
 */
const PrivateRoute = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { isAuthenticated, user, loading } = useSelector((state) => state.auth);
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);

  useEffect(() => {
    // 如果有token但没有用户信息，获取用户信息
    if (token && !user && !loading) {
      dispatch(fetchUserProfile());
    }
  }, [dispatch, token, user, loading]);

  // 如果正在加载用户信息，显示加载中
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 如果没有token或未认证，重定向到登录页面
  if (!token || !isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  // 如果已认证，渲染子组件
  return children;
};

export default PrivateRoute;