import { configureStore } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { combineReducers } from '@reduxjs/toolkit'

import authSlice from './slices/authSlice'
import uploadSlice from './slices/uploadSlice'
import taskSlice from './slices/taskSlice'
import uiSlice from './slices/uiSlice'

// 持久化配置
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'] // 只持久化auth状态
}

// 合并reducers
const rootReducer = combineReducers({
  auth: authSlice,
  upload: uploadSlice,
  task: taskSlice,
  ui: uiSlice
})

// 创建持久化reducer
const persistedReducer = persistReducer(persistConfig, rootReducer)

// 配置store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']
      }
    }),
  devTools: process.env.NODE_ENV !== 'production'
})

// 创建persistor
export const persistor = persistStore(store)

export default store
