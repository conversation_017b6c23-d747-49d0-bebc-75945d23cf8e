/**
 * 路由常量
 * 
 * 定义应用中使用的所有路由路径
 */
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  PHOTOS: '/photos',
  PHOTO_DETAIL: '/photos/:id',
  CREATE_PHOTO: '/photos/create',
  EDIT_PHOTO: '/photos/edit/:id',
  PHOTO_RESULT: '/photos/result',
  FAVORITES: '/favorites',
  SUBSCRIPTION: '/subscription',
  PAYMENT: '/payment',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  HELP: '/help',
  PRIVACY: '/privacy',
  TERMS: '/terms',
  NOT_FOUND: '/404',
};

/**
 * API端点常量
 * 
 * 定义应用中使用的所有API端点
 */
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
  },
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile',
    CHANGE_PASSWORD: '/user/change-password',
    SETTINGS: '/user/settings',
    UPDATE_SETTINGS: '/user/settings',
    AVATAR: '/user/avatar',
  },
  PHOTOS: {
    LIST: '/photos',
    DETAIL: '/photos/:id',
    CREATE: '/photos',
    UPDATE: '/photos/:id',
    DELETE: '/photos/:id',
    GENERATE: '/photos/generate',
    FAVORITES: '/photos/favorites',
    ADD_FAVORITE: '/photos/:id/favorite',
    REMOVE_FAVORITE: '/photos/:id/favorite',
  },
  SUBSCRIPTION: {
    PLANS: '/subscription/plans',
    USER_SUBSCRIPTION: '/subscription/user',
    SUBSCRIBE: '/subscription/subscribe',
    CANCEL: '/subscription/cancel',
    PAYMENT_INTENT: '/subscription/payment-intent',
  },
};

/**
 * 主题模式常量
 * 
 * 定义应用中使用的主题模式
 */
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
};

/**
 * 照片尺寸常量
 * 
 * 定义应用中支持的照片尺寸
 */
export const PHOTO_SIZES = {
  PASSPORT: {
    id: 'passport',
    name: '护照照片',
    width: 35,
    height: 45,
    unit: 'mm',
  },
  ID_CARD: {
    id: 'id_card',
    name: '身份证照片',
    width: 26,
    height: 32,
    unit: 'mm',
  },
  VISA: {
    id: 'visa',
    name: '签证照片',
    width: 51,
    height: 51,
    unit: 'mm',
  },
  DRIVING_LICENSE: {
    id: 'driving_license',
    name: '驾照照片',
    width: 22,
    height: 32,
    unit: 'mm',
  },
  SOCIAL_MEDIA: {
    id: 'social_media',
    name: '社交媒体头像',
    width: 400,
    height: 400,
    unit: 'px',
  },
  CUSTOM: {
    id: 'custom',
    name: '自定义尺寸',
  },
};

/**
 * 背景颜色常量
 * 
 * 定义应用中支持的背景颜色
 */
export const BACKGROUND_COLORS = {
  WHITE: {
    id: 'white',
    name: '白色',
    value: '#FFFFFF',
  },
  BLUE: {
    id: 'blue',
    name: '蓝色',
    value: '#0E71EB',
  },
  RED: {
    id: 'red',
    name: '红色',
    value: '#E74C3C',
  },
  GREEN: {
    id: 'green',
    name: '绿色',
    value: '#2ECC71',
  },
  GRAY: {
    id: 'gray',
    name: '灰色',
    value: '#95A5A6',
  },
  CUSTOM: {
    id: 'custom',
    name: '自定义颜色',
  },
};

/**
 * 订阅类型常量
 * 
 * 定义应用中支持的订阅类型
 */
export const SUBSCRIPTION_TYPES = {
  FREE: 'free',
  BASIC: 'basic',
  PREMIUM: 'premium',
  ENTERPRISE: 'enterprise',
};

/**
 * 照片状态常量
 * 
 * 定义照片的处理状态
 */
export const PHOTO_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
};

/**
 * 本地存储键常量
 * 
 * 定义应用中使用的本地存储键
 */
export const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh_token',
  USER: 'user',
  THEME: 'theme',
  LANGUAGE: 'language',
};

/**
 * 分页常量
 * 
 * 定义应用中使用的分页配置
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 12,
  PAGE_SIZE_OPTIONS: ['12', '24', '36', '48'],
};

/**
 * 文件上传常量
 * 
 * 定义文件上传的配置
 */
export const UPLOAD = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ACCEPTED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MAX_FILES: 1,
};

/**
 * 错误消息常量
 * 
 * 定义应用中使用的错误消息
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络错误，请检查您的网络连接',
  SERVER_ERROR: '服务器错误，请稍后再试',
  UNAUTHORIZED: '未授权，请重新登录',
  FORBIDDEN: '您没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入数据验证失败',
  FILE_TOO_LARGE: '文件大小超过限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
  SUBSCRIPTION_REQUIRED: '需要订阅才能使用此功能',
  QUOTA_EXCEEDED: '已超出当前订阅的使用配额',
};

/**
 * 成功消息常量
 * 
 * 定义应用中使用的成功消息
 */
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功，请检查您的邮箱进行验证',
  PASSWORD_RESET_EMAIL_SENT: '密码重置邮件已发送',
  PASSWORD_RESET_SUCCESS: '密码重置成功',
  PROFILE_UPDATE_SUCCESS: '个人资料更新成功',
  SETTINGS_UPDATE_SUCCESS: '设置更新成功',
  PHOTO_CREATE_SUCCESS: '照片创建成功',
  PHOTO_UPDATE_SUCCESS: '照片更新成功',
  PHOTO_DELETE_SUCCESS: '照片删除成功',
  SUBSCRIPTION_SUCCESS: '订阅成功',
  SUBSCRIPTION_CANCEL_SUCCESS: '订阅取消成功',
};