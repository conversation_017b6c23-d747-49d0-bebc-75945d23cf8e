const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  logger.error({
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Sequelize 验证错误
  if (err.name === 'SequelizeValidationError') {
    const message = err.errors.map(error => error.message).join(', ');
    error = {
      code: 400,
      message: '数据验证失败',
      errors: [message]
    };
  }

  // Sequelize 唯一约束错误
  if (err.name === 'SequelizeUniqueConstraintError') {
    const message = '数据已存在，请检查输入信息';
    error = {
      code: 409,
      message,
      errors: [message]
    };
  }

  // JWT 错误
  if (err.name === 'JsonWebTokenError') {
    error = {
      code: 401,
      message: '无效的访问令牌',
      errors: ['Token无效']
    };
  }

  // JWT 过期错误
  if (err.name === 'TokenExpiredError') {
    error = {
      code: 401,
      message: '访问令牌已过期',
      errors: ['Token已过期']
    };
  }

  // Multer 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = {
      code: 413,
      message: '文件大小超过限制',
      errors: ['文件大小不能超过10MB']
    };
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    error = {
      code: 413,
      message: '文件数量超过限制',
      errors: ['最多只能上传5个文件']
    };
  }

  // 默认错误响应
  res.status(error.code || 500).json({
    code: error.code || 500,
    message: error.message || '服务器内部错误',
    errors: error.errors || ['未知错误'],
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = errorHandler;
