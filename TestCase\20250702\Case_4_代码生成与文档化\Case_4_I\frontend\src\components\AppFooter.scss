.app-footer {
  text-align: center;
  background: #f0f2f5;
  padding: 24px;
  height: auto;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .footer-links {
    margin-bottom: 16px;
    
    a {
      margin: 0 12px;
      color: rgba(0, 0, 0, 0.65);
      
      &:hover {
        color: #1890ff;
      }
    }
  }
  
  .copyright {
    color: rgba(0, 0, 0, 0.45);
  }
}

// 响应式样式
@media (max-width: 768px) {
  .app-footer {
    padding: 16px;
    
    .footer-links {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      
      a {
        margin: 0 8px 8px;
      }
    }
  }
}