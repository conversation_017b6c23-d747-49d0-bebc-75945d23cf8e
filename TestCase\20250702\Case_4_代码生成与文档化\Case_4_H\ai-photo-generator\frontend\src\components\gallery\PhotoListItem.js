import React from 'react';
import { List, Checkbox, Dropdown, Menu, Button, Space, Tooltip, Tag } from 'antd';
import {
  StarOutlined,
  StarFilled,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { formatDate, formatFileSize } from '../../utils/utils';

const PhotoListItem = ({
  photo,
  selected,
  selectMode,
  onClick,
  onToggleFavorite,
  onDelete,
  onEdit,
}) => {
  // 阻止事件冒泡
  const stopPropagation = (e) => {
    e.stopPropagation();
  };

  // 下拉菜单
  const menu = (
    <Menu onClick={stopPropagation}>
      <Menu.Item key="view" icon={<EyeOutlined />} onClick={onClick}>
        查看
      </Menu.Item>
      <Menu.Item key="edit" icon={<EditOutlined />} onClick={onEdit}>
        编辑
      </Menu.Item>
      <Menu.Item key="download" icon={<DownloadOutlined />}>
        下载
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="delete" icon={<DeleteOutlined />} danger onClick={onDelete}>
        删除
      </Menu.Item>
    </Menu>
  );

  return (
    <List.Item
      className={`photo-list-item ${selected ? 'selected' : ''}`}
      onClick={onClick}
    >
      <div className="list-item-content">
        {selectMode && (
          <div className="list-select-checkbox" onClick={stopPropagation}>
            <Checkbox checked={selected} onChange={onClick} />
          </div>
        )}
        
        <div className="list-item-thumbnail">
          <img src={photo.url} alt={photo.name} />
        </div>
        
        <div className="list-item-info">
          <div className="list-item-name">{photo.name}</div>
          <div className="list-item-details">
            <Tag color="blue">{photo.typeName || photo.type}</Tag>
            <span className="list-item-size">{formatFileSize(photo.size)}</span>
            <span className="list-item-dimensions">{photo.width} x {photo.height}</span>
            <span className="list-item-date">{formatDate(photo.createdAt)}</span>
          </div>
        </div>
        
        <div className="list-item-actions" onClick={stopPropagation}>
          <Space>
            <Tooltip title={photo.isFavorite ? '取消收藏' : '收藏'}>
              <Button
                type="text"
                icon={photo.isFavorite ? <StarFilled className="favorite-active" /> : <StarOutlined />}
                onClick={onToggleFavorite}
              />
            </Tooltip>
            
            <Tooltip title="编辑">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={onEdit}
              />
            </Tooltip>
            
            <Dropdown
              overlay={menu}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        </div>
      </div>
    </List.Item>
  );
};

export default PhotoListItem;