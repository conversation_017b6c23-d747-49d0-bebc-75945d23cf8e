const { register, login } = require('../services/userService');
const { body, validationResult } = require('express-validator');

// 用户注册验证规则
const registerValidationRules = [
  body('username')
    .trim()
    .notEmpty().withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 }).withMessage('用户名长度应在3-50个字符之间'),
  
  body('email')
    .trim()
    .notEmpty().withMessage('邮箱不能为空')
    .isEmail().withMessage('请提供有效的邮箱地址'),
  
  body('password')
    .trim()
    .notEmpty().withMessage('密码不能为空')
    .isLength({ min: 6 }).withMessage('密码长度至少为6个字符')
];

// 用户登录验证规则
const loginValidationRules = [
  body('email')
    .trim()
    .notEmpty().withMessage('邮箱不能为空')
    .isEmail().withMessage('请提供有效的邮箱地址'),
  
  body('password')
    .trim()
    .notEmpty().withMessage('密码不能为空')
];

// 验证请求
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (errors.isEmpty()) {
    return next();
  }
  
  const extractedErrors = {};
  errors.array().forEach(err => {
    extractedErrors[err.param] = err.msg;
  });
  
  return res.status(422).json({
    code: 422,
    message: '请求参数验证失败',
    errors: extractedErrors
  });
};

// 用户注册控制器
const registerUser = async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    // 注册用户
    const result = await register(username, email, password);
    
    if (!result.success) {
      return res.status(400).json({
        code: 400,
        message: '注册失败',
        error: result.error
      });
    }
    
    res.status(201).json({
      code: 201,
      message: '注册成功',
      data: {
        user: result.user,
        token: result.token
      }
    });
  } catch (error) {
    console.error('用户注册错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 用户登录控制器
const loginUser = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // 登录用户
    const result = await login(email, password);
    
    if (!result.success) {
      return res.status(401).json({
        code: 401,
        message: '登录失败',
        error: result.error
      });
    }
    
    res.status(200).json({
      code: 200,
      message: '登录成功',
      data: {
        user: result.user,
        token: result.token
      }
    });
  } catch (error) {
    console.error('用户登录错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 注销登录
const logoutUser = async (req, res) => {
  // JWT不需要服务端注销，客户端只需删除令牌即可
  // 这里仅返回一个成功消息
  res.status(200).json({
    code: 200,
    message: '注销成功'
  });
};

module.exports = {
  registerValidationRules,
  loginValidationRules,
  validate,
  registerUser,
  loginUser,
  logoutUser
}; 