.photo-preview {
  margin-bottom: 24px;
  
  .preview-card {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .card-actions {
      display: flex;
      align-items: center;
    }
    
    .empty-result {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }
    
    .select-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .photos-grid {
      .photo-item {
        margin-bottom: 16px;
        
        .photo-card {
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          overflow: hidden;
          position: relative;
          
          .photo-checkbox {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 2;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            padding: 4px;
          }
          
          .photo-image {
            height: 150px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            position: relative;
            
            .preview-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: rgba(0, 0, 0, 0.5);
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              color: white;
              opacity: 0;
              transition: opacity 0.3s;
              
              span {
                margin-top: 4px;
              }
            }
            
            &:hover .preview-overlay {
              opacity: 1;
            }
          }
          
          .quality-score {
            padding: 8px;
            display: flex;
            align-items: center;
            background-color: #f9f9f9;
            
            span {
              font-size: 12px;
              color: rgba(0, 0, 0, 0.65);
            }
            
            .ant-rate {
              font-size: 12px;
              margin: 0 4px;
            }
            
            .score-value {
              font-weight: bold;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .photo-preview {
    .preview-card {
      .card-actions {
        flex-direction: column;
        align-items: flex-start;
        
        button {
          margin-top: 8px;
          margin-right: 0 !important;
        }
      }
      
      .select-actions {
        flex-direction: column;
        align-items: flex-start;
        
        .ant-checkbox-wrapper {
          margin-bottom: 8px;
        }
        
        button {
          width: 100%;
        }
      }
    }
  }
}