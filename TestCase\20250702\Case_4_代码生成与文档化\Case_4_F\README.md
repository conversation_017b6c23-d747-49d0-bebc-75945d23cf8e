# AI证件照生成平台

基于AI技术的在线证件照生成平台，用户只需上传个人照片，即可通过大模型技术自动生成符合标准的证件照。

## 项目特点

- 🖼️ 多种证件照规格支持（1寸、2寸等）
- 🎨 多种背景色选择（红、蓝、白等）
- 🚀 快速生成多个证件照方案
- 💻 简洁易用的用户界面
- 📱 支持PC端和移动端

## 技术栈

- 前端：React.js + Ant Design + Redux
- 后端：Node.js (Express)
- 数据库：MySQL
- 存储：云存储 (阿里云OSS)
- AI模型：支持自定义AI模型接入

## 项目结构

```
ai-photo-generator/
├── client/               # 前端代码
├── server/               # 后端代码
├── docs/                 # 文档
├── scripts/              # 脚本工具
└── docker/               # Docker配置
```

## 快速开始

### 前端开发

```bash
# 进入前端目录
cd client

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 后端开发

```bash
# 进入后端目录
cd server

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 系统架构

![系统架构图](docs/images/architecture.png)

## 接口文档

API文档请参考 [API文档](docs/api.md)

## 数据库设计

数据库设计请参考 [数据库设计文档](docs/database.md)

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交变更 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 提交 Pull Request

## 开源协议

MIT 