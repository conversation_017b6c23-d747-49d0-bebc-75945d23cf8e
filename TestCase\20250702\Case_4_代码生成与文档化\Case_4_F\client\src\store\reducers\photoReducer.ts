// 生成的照片类型
export interface GeneratedPhoto {
  id: string;
  url: string;
  qualityScore: number;
}

// 证件照生成任务状态类型
export interface PhotoState {
  taskId: string | null;
  status: 'idle' | 'processing' | 'completed' | 'failed';
  progress: number;
  generatedPhotos: GeneratedPhoto[];
  selectedPhotoIds: string[];
  error: string | null;
}

// 初始状态
const initialState: PhotoState = {
  taskId: null,
  status: 'idle',
  progress: 0,
  generatedPhotos: [],
  selectedPhotoIds: [],
  error: null
};

// Action 类型
export enum PhotoActionTypes {
  GENERATE_START = 'GENERATE_START',
  GENERATE_PROGRESS = 'GENERATE_PROGRESS',
  GENERATE_SUCCESS = 'GENERATE_SUCCESS',
  GENERATE_FAILURE = 'GENERATE_FAILURE',
  SELECT_PHOTO = 'SELECT_PHOTO',
  DESELECT_PHOTO = 'DESELECT_PHOTO',
  SELECT_ALL_PHOTOS = 'SELECT_ALL_PHOTOS',
  DESELECT_ALL_PHOTOS = 'DESELECT_ALL_PHOTOS',
  RESET_PHOTOS = 'RESET_PHOTOS'
}

interface GenerateStartAction {
  type: PhotoActionTypes.GENERATE_START;
  payload: string; // taskId
}

interface GenerateProgressAction {
  type: PhotoActionTypes.GENERATE_PROGRESS;
  payload: number;
}

interface GenerateSuccessAction {
  type: PhotoActionTypes.GENERATE_SUCCESS;
  payload: {
    generatedPhotos: GeneratedPhoto[];
  };
}

interface GenerateFailureAction {
  type: PhotoActionTypes.GENERATE_FAILURE;
  payload: string;
}

interface SelectPhotoAction {
  type: PhotoActionTypes.SELECT_PHOTO;
  payload: string; // photoId
}

interface DeselectPhotoAction {
  type: PhotoActionTypes.DESELECT_PHOTO;
  payload: string; // photoId
}

interface SelectAllPhotosAction {
  type: PhotoActionTypes.SELECT_ALL_PHOTOS;
}

interface DeselectAllPhotosAction {
  type: PhotoActionTypes.DESELECT_ALL_PHOTOS;
}

interface ResetPhotosAction {
  type: PhotoActionTypes.RESET_PHOTOS;
}

export type PhotoAction =
  | GenerateStartAction
  | GenerateProgressAction
  | GenerateSuccessAction
  | GenerateFailureAction
  | SelectPhotoAction
  | DeselectPhotoAction
  | SelectAllPhotosAction
  | DeselectAllPhotosAction
  | ResetPhotosAction;

// Reducer
const photoReducer = (state = initialState, action: PhotoAction): PhotoState => {
  switch (action.type) {
    case PhotoActionTypes.GENERATE_START:
      return {
        ...state,
        taskId: action.payload,
        status: 'processing',
        progress: 0,
        error: null
      };
    
    case PhotoActionTypes.GENERATE_PROGRESS:
      return {
        ...state,
        progress: action.payload
      };
    
    case PhotoActionTypes.GENERATE_SUCCESS:
      return {
        ...state,
        status: 'completed',
        progress: 100,
        generatedPhotos: action.payload.generatedPhotos
      };
    
    case PhotoActionTypes.GENERATE_FAILURE:
      return {
        ...state,
        status: 'failed',
        error: action.payload
      };
    
    case PhotoActionTypes.SELECT_PHOTO:
      return {
        ...state,
        selectedPhotoIds: [...state.selectedPhotoIds, action.payload]
      };
    
    case PhotoActionTypes.DESELECT_PHOTO:
      return {
        ...state,
        selectedPhotoIds: state.selectedPhotoIds.filter(id => id !== action.payload)
      };
    
    case PhotoActionTypes.SELECT_ALL_PHOTOS:
      return {
        ...state,
        selectedPhotoIds: state.generatedPhotos.map(photo => photo.id)
      };
    
    case PhotoActionTypes.DESELECT_ALL_PHOTOS:
      return {
        ...state,
        selectedPhotoIds: []
      };
    
    case PhotoActionTypes.RESET_PHOTOS:
      return initialState;
    
    default:
      return state;
  }
};

export default photoReducer; 