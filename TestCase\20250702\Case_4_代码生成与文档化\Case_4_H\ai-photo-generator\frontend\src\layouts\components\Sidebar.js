import React, { useState, useEffect } from 'react';
import { Layout, Menu, Badge } from 'antd';
import { 
  HomeOutlined, 
  CameraOutlined, 
  HeartOutlined, 
  CrownOutlined, 
  SettingOutlined, 
  QuestionCircleOutlined,
  PlusOutlined,
  AppstoreOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import styled from 'styled-components';
import { ROUTES, SUBSCRIPTION_TYPES } from '../../utils/constants';

const { Sider } = Layout;

/**
 * 侧边栏样式
 */
const StyledSider = styled(Sider)`
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  @media (max-width: 768px) {
    position: fixed;
    height: 100vh;
    z-index: 999;
    left: 0;
    top: 64px;
  }
`;

/**
 * 菜单容器样式
 */
const MenuContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
`;

/**
 * 订阅信息容器样式
 */
const SubscriptionContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  
  .subscription-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 8px;
  }
  
  .subscription-type {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }
  
  .subscription-info {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 12px;
  }
`;

/**
 * 侧边栏组件
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.collapsed - 侧边栏是否折叠
 */
const Sidebar = ({ collapsed }) => {
  const location = useLocation();
  const user = useSelector(state => state.auth.user);
  const subscription = useSelector(state => state.subscription.userSubscription);
  const [selectedKeys, setSelectedKeys] = useState([]);
  
  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const mainPath = `/${pathParts[1]}`;
    
    setSelectedKeys([mainPath]);
  }, [location.pathname]);
  
  // 菜单项配置
  const menuItems = [
    {
      key: ROUTES.HOME,
      icon: <HomeOutlined />,
      label: <Link to={ROUTES.HOME}>首页</Link>,
    },
    {
      key: ROUTES.DASHBOARD,
      icon: <AppstoreOutlined />,
      label: <Link to={ROUTES.DASHBOARD}>仪表盘</Link>,
    },
    {
      key: ROUTES.PHOTOS,
      icon: <CameraOutlined />,
      label: <Link to={ROUTES.PHOTOS}>我的照片</Link>,
    },
    {
      key: ROUTES.CREATE_PHOTO,
      icon: <PlusOutlined />,
      label: <Link to={ROUTES.CREATE_PHOTO}>创建照片</Link>,
    },
    {
      key: ROUTES.FAVORITES,
      icon: <HeartOutlined />,
      label: <Link to={ROUTES.FAVORITES}>收藏夹</Link>,
    },
    {
      key: ROUTES.SUBSCRIPTION,
      icon: <CrownOutlined />,
      label: <Link to={ROUTES.SUBSCRIPTION}>订阅计划</Link>,
    },
    {
      key: ROUTES.PROFILE,
      icon: <UserOutlined />,
      label: <Link to={ROUTES.PROFILE}>个人资料</Link>,
    },
    {
      key: ROUTES.SETTINGS,
      icon: <SettingOutlined />,
      label: <Link to={ROUTES.SETTINGS}>设置</Link>,
    },
    {
      key: ROUTES.HELP,
      icon: <QuestionCircleOutlined />,
      label: <Link to={ROUTES.HELP}>帮助中心</Link>,
    },
  ];
  
  // 获取订阅类型显示名称
  const getSubscriptionTypeName = (type) => {
    switch (type) {
      case SUBSCRIPTION_TYPES.FREE:
        return '免费版';
      case SUBSCRIPTION_TYPES.BASIC:
        return '基础版';
      case SUBSCRIPTION_TYPES.PREMIUM:
        return '高级版';
      case SUBSCRIPTION_TYPES.ENTERPRISE:
        return '企业版';
      default:
        return '未知';
    }
  };
  
  // 获取订阅剩余天数
  const getRemainingDays = () => {
    if (!subscription || !subscription.expiresAt) return 0;
    
    const expiresAt = new Date(subscription.expiresAt);
    const today = new Date();
    const diffTime = expiresAt - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
  };
  
  return (
    <StyledSider
      width={220}
      collapsible
      collapsed={collapsed}
      breakpoint="lg"
      collapsedWidth={0}
      trigger={null}
    >
      <MenuContainer>
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          items={menuItems}
          style={{ borderRight: 0 }}
        />
      </MenuContainer>
      
      {!collapsed && subscription && (
        <SubscriptionContainer>
          <div className="subscription-title">当前订阅</div>
          <div className="subscription-type">
            {getSubscriptionTypeName(subscription.type)}
            {subscription.type !== SUBSCRIPTION_TYPES.FREE && (
              <Badge 
                count={`剩余${getRemainingDays()}天`} 
                style={{ backgroundColor: '#52c41a', marginLeft: 8 }} 
              />
            )}
          </div>
          <div className="subscription-info">
            {subscription.type === SUBSCRIPTION_TYPES.FREE ? (
              <>每月可生成5张照片</>
            ) : (
              <>已使用 {subscription.usedQuota}/{subscription.totalQuota} 张照片</>
            )}
          </div>
          {subscription.type === SUBSCRIPTION_TYPES.FREE && (
            <Link to={ROUTES.SUBSCRIPTION}>
              <Badge.Ribbon text="升级" color="gold">
                <div style={{ height: 20 }}></div>
              </Badge.Ribbon>
            </Link>
          )}
        </SubscriptionContainer>
      )}
    </StyledSider>
  );
};

export default Sidebar;