const express = require('express');
const router = express.Router();
const {
  getTaskStatus,
  getGeneratedPhotos,
  downloadGeneratedPhoto,
  downloadBatchPhotos,
  regeneratePhoto
} = require('../controllers/generationController');
const { protect } = require('../middleware/auth');

// 获取任务状态
router.get('/status/:taskId', protect, getTaskStatus);

// 获取生成的照片列表
router.get('/photos/:taskId', protect, getGeneratedPhotos);

// 下载单张生成的照片
router.get('/download/:taskId/:photoId', protect, downloadGeneratedPhoto);

// 批量下载生成的照片
router.get('/download-batch/:taskId', protect, downloadBatchPhotos);

// 重新生成照片
router.post('/regenerate/:taskId', protect, regeneratePhoto);

module.exports = router;