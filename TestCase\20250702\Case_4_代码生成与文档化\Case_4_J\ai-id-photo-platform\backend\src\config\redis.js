const redis = require('redis');
require('dotenv').config();

const client = redis.createClient({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0
});

client.on('error', (err) => {
  console.error('Redis连接错误:', err);
});

client.on('connect', () => {
  console.log('Redis连接成功');
});

// 连接Redis
const connectRedis = async () => {
  try {
    await client.connect();
  } catch (error) {
    console.error('Redis连接失败:', error);
  }
};

module.exports = {
  client,
  connectRedis
};
