import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Form, 
  Input, 
  Button, 
  Typography, 
  message, 
  Space, 
  Result, 
  Alert 
} from 'antd';
import { LockOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { resetPassword, verifyResetToken } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;

/**
 * 样式化组件
 */
const FormHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
`;

const FormFooter = styled.div`
  text-align: center;
  margin-top: 24px;
`;

const BackLink = styled(Link)`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  
  .anticon {
    margin-right: 8px;
  }
`;

/**
 * 重置密码页面组件
 * 
 * @returns {JSX.Element} 重置密码页面组件
 */
const ResetPassword = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { token } = useParams();
  
  // 从Redux获取状态
  const { loading } = useSelector((state) => state.auth);
  
  // 本地状态
  const [form] = Form.useForm();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isValidToken, setIsValidToken] = useState(true);
  const [isTokenChecking, setIsTokenChecking] = useState(true);
  
  // 验证重置令牌
  useEffect(() => {
    const checkToken = async () => {
      try {
        await dispatch(verifyResetToken({ token })).unwrap();
        setIsValidToken(true);
      } catch (error) {
        setIsValidToken(false);
      } finally {
        setIsTokenChecking(false);
      }
    };
    
    if (token) {
      checkToken();
    } else {
      setIsValidToken(false);
      setIsTokenChecking(false);
    }
  }, [dispatch, token]);
  
  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      await dispatch(resetPassword({ 
        token, 
        password: values.password 
      })).unwrap();
      
      setIsSubmitted(true);
    } catch (error) {
      message.error(error?.message || '重置密码失败，请稍后再试');
    }
  };
  
  // 如果正在检查令牌，显示加载状态
  if (isTokenChecking) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 0' }}>
        <Title level={3}>正在验证重置链接...</Title>
      </div>
    );
  }
  
  // 如果令牌无效，显示错误信息
  if (!isValidToken) {
    return (
      <Result
        status="error"
        title="无效或已过期的重置链接"
        subTitle="您点击的密码重置链接无效或已过期。请尝试重新请求密码重置。"
        extra={[
          <Button type="primary" key="forgotPassword" onClick={() => navigate(ROUTES.FORGOT_PASSWORD)}>
            重新发送重置链接
          </Button>,
          <Button key="login" onClick={() => navigate(ROUTES.LOGIN)}>
            返回登录
          </Button>,
        ]}
      />
    );
  }
  
  // 如果已提交表单，显示成功信息
  if (isSubmitted) {
    return (
      <Result
        status="success"
        title="密码重置成功"
        subTitle="您的密码已成功重置，现在可以使用新密码登录了。"
        extra={[
          <Button type="primary" key="login" onClick={() => navigate(ROUTES.LOGIN)}>
            立即登录
          </Button>,
        ]}
      />
    );
  }
  
  return (
    <>
      <BackLink to={ROUTES.LOGIN}>
        <ArrowLeftOutlined /> 返回登录
      </BackLink>
      
      <FormHeader>
        <Title level={2}>重置密码</Title>
        <Text type="secondary">
          请输入您的新密码
        </Text>
      </FormHeader>
      
      <Form
        form={form}
        name="resetPassword"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入您的新密码' },
            { min: 6, message: '密码长度不能少于6个字符' }
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />} 
            placeholder="新密码" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认您的新密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />} 
            placeholder="确认新密码" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item>
          <Alert
            message="密码安全提示"
            description="为了您的账户安全，请创建一个强密码，包含大小写字母、数字和特殊字符。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            size="large" 
            block 
            loading={loading}
          >
            重置密码
          </Button>
        </Form.Item>
      </Form>
      
      <FormFooter>
        <Space>
          <Text type="secondary">记起密码了？</Text>
          <Link to={ROUTES.LOGIN}>立即登录</Link>
        </Space>
      </FormFooter>
    </>
  );
};

export default ResetPassword;