import axios from 'axios';
import { message } from 'antd';
import { STORAGE_KEYS, ERROR_MESSAGES } from '../utils/constants';
import store from '../store';
import { logout } from '../store/authSlice';

/**
 * API基础URL
 * 
 * 根据环境变量配置API基础URL
 */
const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

/**
 * 创建axios实例
 */
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 请求拦截器
 * 
 * 在请求发送前添加认证token
 */
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 
 * 处理响应数据和错误
 */
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // 如果是401错误且不是刷新token的请求，尝试刷新token
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      originalRequest.url !== '/auth/refresh-token'
    ) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        
        if (!refreshToken) {
          // 如果没有刷新token，直接登出
          store.dispatch(logout());
          return Promise.reject(error);
        }
        
        // 尝试刷新token
        const response = await axios.post(
          `${BASE_URL}/auth/refresh-token`,
          { refreshToken },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
        
        // 保存新token
        const { token, refreshToken: newRefreshToken } = response.data;
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
        
        // 使用新token重试原请求
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return api(originalRequest);
      } catch (refreshError) {
        // 刷新token失败，登出用户
        store.dispatch(logout());
        return Promise.reject(refreshError);
      }
    }
    
    // 处理常见错误
    if (error.response) {
      // 服务器响应错误
      switch (error.response.status) {
        case 400:
          // 请求错误
          break;
        case 401:
          // 未授权，已在上面处理
          break;
        case 403:
          // 禁止访问
          message.error(ERROR_MESSAGES.FORBIDDEN);
          break;
        case 404:
          // 资源不存在
          message.error(ERROR_MESSAGES.NOT_FOUND);
          break;
        case 500:
          // 服务器错误
          message.error(ERROR_MESSAGES.SERVER_ERROR);
          break;
        default:
          // 其他错误
          break;
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      message.error(ERROR_MESSAGES.NETWORK_ERROR);
    } else {
      // 请求设置时发生错误
      message.error(error.message);
    }
    
    return Promise.reject(error);
  }
);

export default api;