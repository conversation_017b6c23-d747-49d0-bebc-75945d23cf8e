import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Upload, Button, message, Progress, Card, Select } from 'antd';
import { InboxOutlined, UploadOutlined } from '@ant-design/icons';
import { uploadPhoto, uploadMultiplePhotos } from '../services/uploadSlice';
import './PhotoUploader.scss';

const { Dragger } = Upload;
const { Option } = Select;

const PhotoUploader = ({ onUploadSuccess }) => {
  const dispatch = useDispatch();
  const { loading, uploadProgress } = useSelector((state) => state.upload);
  
  const [fileList, setFileList] = useState([]);
  const [selectedSpec, setSelectedSpec] = useState('1inch'); // 默认1寸
  
  // 支持的证件照规格
  const photoSpecs = [
    { value: '1inch', label: '1寸证件照 (25mm × 35mm)' },
    { value: '2inch', label: '2寸证件照 (35mm × 49mm)' },
    { value: 'small2inch', label: '小2寸证件照 (33mm × 48mm)' },
    { value: 'large1inch', label: '大1寸证件照 (33mm × 45mm)' },
    { value: 'custom', label: '自定义尺寸' },
  ];
  
  // 文件上传前的校验
  const beforeUpload = (file) => {
    // 检查文件类型
    const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type);
    if (!isValidType) {
      message.error('只支持JPG、PNG、JPEG格式的图片！');
      return Upload.LIST_IGNORE;
    }
    
    // 检查文件大小
    const isLessThan10M = file.size / 1024 / 1024 < 10;
    if (!isLessThan10M) {
      message.error('图片必须小于10MB！');
      return Upload.LIST_IGNORE;
    }
    
    // 检查文件数量
    if (fileList.length >= 5) {
      message.error('最多只能上传5张照片！');
      return Upload.LIST_IGNORE;
    }
    
    return true;
  };
  
  // 文件状态改变时的回调
  const handleChange = ({ fileList: newFileList }) => {
    setFileList(newFileList.slice(0, 5)); // 限制最多5张
  };
  
  // 自定义上传操作
  const customRequest = ({ file, onSuccess, onError }) => {
    // 这里不做实际上传，只是更新UI状态
    setTimeout(() => {
      onSuccess("ok");
    }, 0);
  };
  
  // 开始生成证件照
  const handleStartGeneration = async () => {
    if (fileList.length === 0) {
      message.error('请先上传照片！');
      return;
    }
    
    try {
      if (fileList.length === 1) {
        // 单张照片上传
        const result = await dispatch(uploadPhoto({
          file: fileList[0].originFileObj,
          spec: selectedSpec
        })).unwrap();
        
        if (onUploadSuccess) {
          onUploadSuccess(result.data.upload_id, selectedSpec);
        }
      } else {
        // 多张照片上传
        const files = fileList.map(file => file.originFileObj);
        const result = await dispatch(uploadMultiplePhotos({
          files,
          spec: selectedSpec
        })).unwrap();
        
        if (onUploadSuccess) {
          onUploadSuccess(result.data.map(item => item.upload_id), selectedSpec);
        }
      }
    } catch (error) {
      message.error(error.message || '上传失败，请重试');
    }
  };
  
  return (
    <div className="photo-uploader">
      <Card title="上传照片" className="upload-card">
        <Dragger
          fileList={fileList}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          customRequest={customRequest}
          multiple
          listType="picture"
          maxCount={5}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽照片到此区域上传</p>
          <p className="ant-upload-hint">
            支持JPG、PNG、JPEG格式，单张照片大小不超过10MB，最多上传5张
          </p>
        </Dragger>
        
        {loading && uploadProgress > 0 && (
          <Progress percent={uploadProgress} status="active" className="upload-progress" />
        )}
        
        <div className="upload-actions">
          <div className="spec-selector">
            <span>证件照规格：</span>
            <Select
              value={selectedSpec}
              onChange={setSelectedSpec}
              style={{ width: 250 }}
            >
              {photoSpecs.map(spec => (
                <Option key={spec.value} value={spec.value}>{spec.label}</Option>
              ))}
            </Select>
          </div>
          
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={handleStartGeneration}
            loading={loading}
            disabled={fileList.length === 0}
          >
            开始生成
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PhotoUploader;