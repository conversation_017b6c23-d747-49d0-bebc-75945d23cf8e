const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 注册新用户
const register = async (username, email, password) => {
  try {
    // 检查用户名是否已存在
    const existingUserByUsername = await User.findOne({ where: { username } });
    if (existingUserByUsername) {
      throw new Error('用户名已被使用');
    }
    
    // 检查邮箱是否已存在
    const existingUserByEmail = await User.findOne({ where: { email } });
    if (existingUserByEmail) {
      throw new Error('邮箱已被注册');
    }
    
    // 创建用户
    const user = await User.create({
      username,
      email,
      password
    });
    
    // 生成JWT令牌
    const token = generateToken(user);
    
    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        credits: user.credits
      },
      token
    };
  } catch (error) {
    console.error('用户注册失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 用户登录
const login = async (email, password) => {
  try {
    // 查找用户
    const user = await User.findOne({ where: { email } });
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 验证密码
    const isPasswordValid = await user.checkPassword(password);
    if (!isPasswordValid) {
      throw new Error('密码错误');
    }
    
    // 生成JWT令牌
    const token = generateToken(user);
    
    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        credits: user.credits
      },
      token
    };
  } catch (error) {
    console.error('用户登录失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 获取用户信息
const getUserInfo = async (userId) => {
  try {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }
    
    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        credits: user.credits
      }
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 更新用户信息
const updateUserInfo = async (userId, userData) => {
  try {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 更新用户数据
    await user.update(userData);
    
    return {
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        credits: user.credits
      }
    };
  } catch (error) {
    console.error('更新用户信息失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 生成JWT令牌
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, username: user.username, email: user.email },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

module.exports = {
  register,
  login,
  getUserInfo,
  updateUserInfo
}; 