# AI证件照生成平台 技术架构

## 架构概览

本项目采用前后端分离的架构，主要分为以下几个部分：

1. **前端**：基于React的单页应用
2. **后端**：基于Node.js和Express的RESTful API服务
3. **数据库**：MongoDB文档数据库
4. **AI处理服务**：负责照片处理和证件照生成
5. **文件存储**：用于存储用户上传的照片和生成的证件照

## 技术栈

### 前端技术栈

- **核心框架**：React 18
- **状态管理**：Redux Toolkit
- **路由管理**：React Router 6
- **UI组件库**：Ant Design 5
- **HTTP客户端**：Axios
- **样式处理**：SCSS
- **构建工具**：Create React App

### 后端技术栈

- **运行环境**：Node.js 14+
- **Web框架**：Express 4
- **数据库**：MongoDB 4.4+
- **ODM**：Mongoose 7
- **认证**：JWT (jsonwebtoken)
- **密码加密**：bcryptjs
- **文件上传**：multer
- **图像处理**：sharp
- **数据验证**：express-validator

## 系统架构图

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|   Web Browser    |<--->|   Nginx Server   |<--->|   Frontend App   |
|                  |     |   (Web Server)   |     |   (React SPA)    |
+------------------+     +------------------+     +------------------+
                                                          |
                                                          |
                                                          v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|   MongoDB        |<--->|   Backend API    |<--->|   AI Processing  |
|   Database       |     |   (Express)      |     |   Service        |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                                |                          |
                                v                          v
                         +------------------+     +------------------+
                         |                  |     |                  |
                         |   File Storage   |<--->|   Image Cache    |
                         |   System         |     |                  |
                         |                  |     |                  |
                         +------------------+     +------------------+
```

## 前端架构

### 目录结构

```
frontend/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── components/         # 可复用组件
│   │   ├── AppHeader/      # 应用头部组件
│   │   ├── AppFooter/      # 应用底部组件
│   │   ├── PhotoUploader/  # 照片上传组件
│   │   └── PhotoPreview/   # 照片预览组件
│   ├── pages/              # 页面组件
│   │   ├── HomePage/       # 首页
│   │   ├── UploadPage/     # 上传页面
│   │   ├── ResultPage/     # 结果页面
│   │   ├── LoginPage/      # 登录页面
│   │   ├── RegisterPage/   # 注册页面
│   │   └── UserProfilePage/# 用户资料页面
│   ├── services/           # Redux服务
│   │   ├── userSlice.js    # 用户状态管理
│   │   ├── uploadSlice.js  # 上传状态管理
│   │   └── generationSlice.js # 生成状态管理
│   ├── utils/              # 工具函数
│   │   └── api.js          # API请求封装
│   ├── App.js              # 应用入口组件
│   ├── App.scss            # 应用样式
│   ├── index.js            # 应用入口文件
│   ├── index.scss          # 全局样式
│   └── store.js            # Redux存储配置
├── package.json            # 项目依赖
└── README.md               # 项目说明
```

### 状态管理

使用Redux Toolkit管理全局状态，主要包括：

1. **userSlice**：管理用户认证状态和用户信息
   - 登录/注册/注销
   - 获取用户资料
   - 更新用户信息

2. **uploadSlice**：管理照片上传状态
   - 上传进度
   - 上传错误处理
   - 上传成功回调

3. **generationSlice**：管理证件照生成状态
   - 生成任务状态
   - 生成结果列表
   - 下载状态

### 路由设计

使用React Router管理前端路由：

- `/` - 首页
- `/upload` - 上传照片页面
- `/result/:taskId` - 生成结果页面
- `/login` - 登录页面
- `/register` - 注册页面
- `/profile` - 用户个人中心
- `/terms` - 用户协议
- `/privacy` - 隐私政策

## 后端架构

### 目录结构

```
backend/
├── config/                 # 配置文件
│   └── db.js               # 数据库配置
├── controllers/            # 控制器
│   ├── userController.js   # 用户控制器
│   ├── uploadController.js # 上传控制器
│   └── generationController.js # 生成控制器
├── middleware/             # 中间件
│   ├── auth.js             # 认证中间件
│   └── upload.js           # 上传中间件
├── models/                 # 数据模型
│   ├── User.js             # 用户模型
│   └── PhotoTask.js        # 照片任务模型
├── routes/                 # 路由
│   ├── userRoutes.js       # 用户路由
│   ├── uploadRoutes.js     # 上传路由
│   └── generationRoutes.js # 生成路由
├── services/               # 服务
│   └── aiService.js        # AI处理服务
├── utils/                  # 工具函数
│   └── errorHandler.js     # 错误处理
├── uploads/                # 上传文件存储
├── .env                    # 环境变量
├── server.js               # 服务器入口
└── package.json            # 项目依赖
```

### API设计

采用RESTful API设计风格：

#### 用户API

- `POST /api/users/register` - 注册用户
- `POST /api/users/login` - 用户登录
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新用户信息
- `PUT /api/users/password` - 更新用户密码

#### 上传API

- `POST /api/upload/single` - 上传单张照片
- `POST /api/upload/batch` - 批量上传照片

#### 生成API

- `GET /api/generation/status/:taskId` - 获取任务状态
- `GET /api/generation/photos/:taskId` - 获取生成的照片列表
- `GET /api/generation/download/:taskId/:photoId` - 下载单张生成的照片
- `GET /api/generation/download-batch/:taskId` - 批量下载生成的照片
- `POST /api/generation/regenerate/:taskId` - 重新生成照片

### 认证机制

使用JWT（JSON Web Token）进行用户认证：

1. 用户登录成功后，服务器生成JWT令牌
2. 客户端存储令牌（localStorage）
3. 后续请求在Authorization头中携带令牌
4. 服务器验证令牌有效性
5. 令牌过期后需要重新登录

### 文件上传处理

使用multer中间件处理文件上传：

1. 验证文件类型（仅允许JPG/JPEG/PNG）
2. 限制文件大小（最大10MB）
3. 为每个用户创建独立的上传目录
4. 使用UUID生成唯一文件名

## 数据库设计

### 用户集合 (Users)

```javascript
const UserSchema = new mongoose.Schema(
  {
    username: String,       // 用户名
    email: String,          // 邮箱
    password: String,       // 密码（加密存储）
    avatar: String,         // 头像URL
    role: String,           // 角色（user/admin）
    remainingCredits: Number, // 剩余积分
    resetPasswordToken: String, // 重置密码令牌
    resetPasswordExpire: Date   // 重置密码令牌过期时间
  },
  { timestamps: true }      // 创建时间和更新时间
);
```

### 照片任务集合 (PhotoTasks)

```javascript
const PhotoTaskSchema = new mongoose.Schema(
  {
    user: ObjectId,         // 关联的用户ID
    originalPhotos: [       // 原始照片数组
      {
        path: String,       // 照片路径
        filename: String     // 文件名
      }
    ],
    generatedPhotos: [      // 生成的照片数组
      {
        path: String,       // 照片路径
        filename: String,    // 文件名
        originalPhotoIndex: Number, // 对应的原始照片索引
        qualityScore: Number // 质量评分
      }
    ],
    specification: String,  // 证件照规格
    status: String,         // 任务状态
    errorMessage: String,   // 错误信息
    processingTime: Number, // 处理时间（秒）
    creditsUsed: Number     // 使用的积分
  },
  { timestamps: true }      // 创建时间和更新时间
);
```

## AI处理服务

### 功能模块

1. **人脸检测**：识别照片中的人脸位置和特征
2. **背景处理**：移除原始背景，替换为标准证件照背景
3. **人像优化**：调整亮度、对比度、肤色等
4. **尺寸裁剪**：根据不同规格要求裁剪照片
5. **质量评估**：对生成结果进行质量评分

### 处理流程

1. 接收原始照片和规格要求
2. 检测人脸位置和特征
3. 分离人像和背景
4. 替换为标准背景
5. 根据规格要求调整尺寸和位置
6. 优化照片质量
7. 生成最终证件照
8. 返回处理结果和质量评分

## 安全措施

1. **认证与授权**
   - JWT认证
   - 基于角色的访问控制
   - 请求频率限制

2. **数据安全**
   - 密码加密存储（bcrypt）
   - HTTPS传输加密
   - 敏感信息过滤

3. **输入验证**
   - 请求数据验证（express-validator）
   - 文件类型和大小验证
   - SQL注入防护

4. **错误处理**
   - 统一错误处理中间件
   - 生产环境错误信息过滤
   - 错误日志记录

## 扩展性设计

1. **模块化架构**
   - 前后端分离
   - 功能模块独立
   - 接口统一规范

2. **水平扩展**
   - 无状态API设计
   - 负载均衡支持
   - 数据库分片准备

3. **服务解耦**
   - AI处理服务独立
   - 文件存储服务独立
   - 消息队列集成准备

## 性能优化

1. **前端优化**
   - 代码分割和懒加载
   - 资源压缩和缓存
   - 图片懒加载

2. **后端优化**
   - 数据库索引优化
   - 请求缓存
   - 异步处理长任务

3. **网络优化**
   - CDN加速静态资源
   - HTTP/2支持
   - 响应压缩