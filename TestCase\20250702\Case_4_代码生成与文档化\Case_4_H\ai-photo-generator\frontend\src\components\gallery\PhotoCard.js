import React from 'react';
import { Card, Checkbox, Dropdown, Menu, Tooltip } from 'antd';
import {
  StarOutlined,
  StarFilled,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  DownloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { formatDate } from '../../utils/utils';

const { Meta } = Card;

const PhotoCard = ({
  photo,
  selected,
  selectMode,
  onClick,
  onToggleFavorite,
  onDelete,
  onEdit,
}) => {
  // 阻止事件冒泡
  const stopPropagation = (e) => {
    e.stopPropagation();
  };

  // 下拉菜单
  const menu = (
    <Menu onClick={stopPropagation}>
      <Menu.Item key="view" icon={<EyeOutlined />} onClick={onClick}>
        查看
      </Menu.Item>
      <Menu.Item key="edit" icon={<EditOutlined />} onClick={onEdit}>
        编辑
      </Menu.Item>
      <Menu.Item key="download" icon={<DownloadOutlined />}>
        下载
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="delete" icon={<DeleteOutlined />} danger onClick={onDelete}>
        删除
      </Menu.Item>
    </Menu>
  );

  return (
    <Card
      hoverable
      className={`photo-card ${selected ? 'selected' : ''}`}
      cover={
        <div className="photo-card-cover" onClick={onClick}>
          <img alt={photo.name} src={photo.url} />
          {selectMode && (
            <div className="select-checkbox" onClick={stopPropagation}>
              <Checkbox checked={selected} onChange={onClick} />
            </div>
          )}
        </div>
      }
      actions={[
        <Tooltip title={photo.isFavorite ? '取消收藏' : '收藏'}>
          {photo.isFavorite ? (
            <StarFilled
              className="favorite-icon active"
              onClick={(e) => {
                stopPropagation(e);
                onToggleFavorite();
              }}
            />
          ) : (
            <StarOutlined
              className="favorite-icon"
              onClick={(e) => {
                stopPropagation(e);
                onToggleFavorite();
              }}
            />
          )}
        </Tooltip>,
        <Tooltip title="编辑">
          <EditOutlined
            key="edit"
            onClick={(e) => {
              stopPropagation(e);
              onEdit();
            }}
          />
        </Tooltip>,
        <Dropdown
          overlay={menu}
          trigger={['click']}
          placement="bottomRight"
          onClick={stopPropagation}
        >
          <MoreOutlined key="more" />
        </Dropdown>,
      ]}
    >
      <Meta
        title={photo.name}
        description={
          <div className="photo-card-info">
            <div className="photo-type">{photo.typeName || photo.type}</div>
            <div className="photo-date">{formatDate(photo.createdAt)}</div>
          </div>
        }
        onClick={onClick}
      />
    </Card>
  );
};

export default PhotoCard;