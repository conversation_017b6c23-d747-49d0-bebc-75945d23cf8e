.app-header {
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: #001529;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .logo {
    float: left;
    margin-right: 30px;
    
    a {
      color: white;
      font-size: 18px;
      font-weight: bold;
      text-decoration: none;
    }
  }

  .main-menu {
    flex: 1;
    border-bottom: none;
  }

  .user-actions {
    display: flex;
    align-items: center;
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 0 8px;
      
      .username {
        margin-left: 8px;
        color: white;
      }
    }
    
    button {
      margin-left: 8px;
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;
    
    .logo {
      margin-right: 15px;
      
      a {
        font-size: 16px;
      }
    }
    
    .user-actions {
      .user-info {
        .username {
          display: none;
        }
      }
    }
  }
}