const express = require('express');
const router = express.Router();
const generateController = require('../controllers/generateController');
const { optionalAuth } = require('../middleware/auth');
const { validate, generateSchema } = require('../utils/validation');

/**
 * @route POST /api/generate
 * @desc 创建证件照生成任务
 * @access Public (可选认证)
 */
router.post('/',
  optionalAuth,
  validate(generateSchema),
  generateController.createGenerationTask
);

/**
 * @route GET /api/generate/task/:taskId
 * @desc 获取生成任务状态
 * @access Public (可选认证)
 */
router.get('/task/:taskId',
  optionalAuth,
  generateController.getTaskStatus
);

/**
 * @route GET /api/generate/user/tasks
 * @desc 获取用户的生成任务列表
 * @access Private
 */
router.get('/user/tasks',
  optionalAuth,
  generateController.getUserTasks
);

module.exports = router;
