import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Select, 
  Radio, 
  Typography, 
  Space, 
  Alert,
  Progress,
  message
} from 'antd'
import { 
  CloudUploadOutlined, 
  PictureOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useDropzone } from 'react-dropzone'

import { uploadSingle } from '@/store/slices/uploadSlice'
import { createGenerationTask } from '@/store/slices/taskSlice'
import uploadService from '@/services/uploadService'
import './index.scss'

const { Title, Paragraph, Text } = Typography
const { Option } = Select

const UploadPage = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  
  const { uploading, uploadProgress, currentUpload, error } = useSelector(state => state.upload)
  const { creating } = useSelector(state => state.task)
  
  const [selectedFile, setSelectedFile] = useState(null)
  const [previewUrl, setPreviewUrl] = useState(null)
  const [spec, setSpec] = useState('1inch')
  const [background, setBackground] = useState('blue')
  const [customSpec, setCustomSpec] = useState({ width: 295, height: 413 })

  // 文件拖拽处理
  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(file => 
        file.errors.map(error => error.message).join(', ')
      )
      message.error(`文件验证失败: ${errors.join('; ')}`)
      return
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0]
      
      // 验证文件
      const validation = uploadService.validateFile(file)
      if (!validation.isValid) {
        message.error(validation.errors.join(', '))
        return
      }

      setSelectedFile(file)
      
      // 生成预览URL
      const url = uploadService.getFilePreviewUrl(file)
      setPreviewUrl(url)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  // 删除选中的文件
  const handleRemoveFile = () => {
    if (previewUrl) {
      uploadService.revokeFilePreviewUrl(previewUrl)
    }
    setSelectedFile(null)
    setPreviewUrl(null)
  }

  // 开始生成
  const handleGenerate = async () => {
    if (!selectedFile) {
      message.error('请先选择要上传的照片')
      return
    }

    try {
      // 上传文件
      const uploadResult = await dispatch(uploadSingle(selectedFile)).unwrap()
      
      // 创建生成任务
      const taskData = {
        uploadId: uploadResult.uploadId,
        spec,
        background,
        count: 5
      }

      if (spec === 'custom') {
        taskData.customSpec = customSpec
      }

      const taskResult = await dispatch(createGenerationTask(taskData)).unwrap()
      
      // 跳转到结果页面
      navigate(`/result/${taskResult.taskId}`)
      
    } catch (error) {
      message.error(error.message || '生成失败，请重试')
    }
  }

  const photoSpecs = [
    { value: '1inch', label: '1寸证件照 (25mm × 35mm)' },
    { value: '2inch', label: '2寸证件照 (35mm × 49mm)' },
    { value: 'small2inch', label: '小2寸证件照 (33mm × 48mm)' },
    { value: 'big1inch', label: '大1寸证件照 (33mm × 45mm)' },
    { value: 'custom', label: '自定义尺寸' }
  ]

  const backgroundColors = [
    { value: 'blue', label: '蓝色', color: '#438EDB' },
    { value: 'red', label: '红色', color: '#FF0000' },
    { value: 'white', label: '白色', color: '#FFFFFF' },
    { value: 'lightblue', label: '浅蓝色', color: '#87CEEB' },
    { value: 'lightgray', label: '浅灰色', color: '#D3D3D3' }
  ]

  return (
    <div className="upload-page">
      <div className="container">
        <Row justify="center">
          <Col xs={24} sm={20} md={16} lg={12}>
            <Card className="upload-card">
              <div className="upload-header">
                <Title level={2}>
                  <PictureOutlined /> 上传照片
                </Title>
                <Paragraph>
                  请上传一张清晰的个人照片，我们将为您生成标准证件照
                </Paragraph>
              </div>

              {error && (
                <Alert
                  message="上传失败"
                  description={error.message || '请重试'}
                  type="error"
                  showIcon
                  closable
                  style={{ marginBottom: 24 }}
                />
              )}

              {/* 文件上传区域 */}
              <div className="upload-section">
                {!selectedFile ? (
                  <div
                    {...getRootProps()}
                    className={`upload-dropzone ${isDragActive ? 'active' : ''}`}
                  >
                    <input {...getInputProps()} />
                    <CloudUploadOutlined className="upload-icon" />
                    <Title level={4}>
                      {isDragActive ? '释放文件到这里' : '拖拽照片到这里，或点击选择'}
                    </Title>
                    <Paragraph>
                      支持 JPG、PNG 格式，文件大小不超过 10MB
                    </Paragraph>
                  </div>
                ) : (
                  <div className="file-preview">
                    <div className="preview-image">
                      <img src={previewUrl} alt="预览" />
                    </div>
                    <div className="file-info">
                      <Text strong>{selectedFile.name}</Text>
                      <br />
                      <Text type="secondary">
                        {uploadService.formatFileSize(selectedFile.size)}
                      </Text>
                    </div>
                    <div className="file-actions">
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        onClick={() => window.open(previewUrl)}
                      >
                        预览
                      </Button>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleRemoveFile}
                      >
                        删除
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* 上传进度 */}
              {uploading && (
                <div className="upload-progress">
                  <Progress percent={uploadProgress} status="active" />
                  <Text>正在上传...</Text>
                </div>
              )}

              {/* 规格选择 */}
              <div className="spec-section">
                <Title level={4}>选择证件照规格</Title>
                <Select
                  value={spec}
                  onChange={setSpec}
                  style={{ width: '100%' }}
                  size="large"
                >
                  {photoSpecs.map(item => (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  ))}
                </Select>

                {spec === 'custom' && (
                  <div className="custom-spec">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Text>宽度 (px):</Text>
                        <input
                          type="number"
                          value={customSpec.width}
                          onChange={(e) => setCustomSpec({
                            ...customSpec,
                            width: parseInt(e.target.value) || 0
                          })}
                          min="100"
                          max="2000"
                        />
                      </Col>
                      <Col span={12}>
                        <Text>高度 (px):</Text>
                        <input
                          type="number"
                          value={customSpec.height}
                          onChange={(e) => setCustomSpec({
                            ...customSpec,
                            height: parseInt(e.target.value) || 0
                          })}
                          min="100"
                          max="2000"
                        />
                      </Col>
                    </Row>
                  </div>
                )}
              </div>

              {/* 背景颜色选择 */}
              <div className="background-section">
                <Title level={4}>选择背景颜色</Title>
                <Radio.Group
                  value={background}
                  onChange={(e) => setBackground(e.target.value)}
                  className="background-options"
                >
                  {backgroundColors.map(item => (
                    <Radio.Button key={item.value} value={item.value}>
                      <div className="color-option">
                        <div 
                          className="color-preview"
                          style={{ backgroundColor: item.color }}
                        />
                        {item.label}
                      </div>
                    </Radio.Button>
                  ))}
                </Radio.Group>
              </div>

              {/* 操作按钮 */}
              <div className="action-section">
                <Space size="large">
                  <Button
                    size="large"
                    onClick={() => navigate('/')}
                  >
                    返回首页
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    loading={uploading || creating}
                    disabled={!selectedFile}
                    onClick={handleGenerate}
                  >
                    开始生成
                  </Button>
                </Space>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default UploadPage
