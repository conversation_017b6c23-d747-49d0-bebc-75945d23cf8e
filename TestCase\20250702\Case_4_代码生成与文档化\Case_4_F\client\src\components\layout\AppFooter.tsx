import React from 'react';
import { Layout, Row, Col, Typography, Space, Divider } from 'antd';
import { Link } from 'react-router-dom';
import './AppFooter.scss';

const { Footer } = Layout;
const { Title, Text } = Typography;

const AppFooter: React.FC = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <Footer className="app-footer">
      <div className="footer-content">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={8}>
            <Title level={4} className="footer-title">AI证件照</Title>
            <Text className="footer-description">
              基于AI技术的证件照生成平台，让您轻松获取专业证件照
            </Text>
          </Col>
          
          <Col xs={24} sm={8}>
            <Title level={4} className="footer-title">快速链接</Title>
            <ul className="footer-links">
              <li>
                <Link to="/">首页</Link>
              </li>
              <li>
                <Link to="/upload">上传照片</Link>
              </li>
              <li>
                <Link to="/pricing">价格方案</Link>
              </li>
              <li>
                <Link to="/about">关于我们</Link>
              </li>
            </ul>
          </Col>
          
          <Col xs={24} sm={8}>
            <Title level={4} className="footer-title">帮助与支持</Title>
            <ul className="footer-links">
              <li>
                <Link to="/help">帮助中心</Link>
              </li>
              <li>
                <Link to="/faq">常见问题</Link>
              </li>
              <li>
                <Link to="/contact">联系我们</Link>
              </li>
              <li>
                <Link to="/feedback">意见反馈</Link>
              </li>
            </ul>
          </Col>
        </Row>
        
        <Divider style={{ margin: '24px 0' }} />
        
        <div className="footer-bottom">
          <Space split={<Divider type="vertical" />}>
            <Link to="/terms">服务条款</Link>
            <Link to="/privacy">隐私政策</Link>
          </Space>
          <div className="copyright">
            &copy; {currentYear} AI证件照生成平台. 保留所有权利.
          </div>
        </div>
      </div>
    </Footer>
  );
};

export default AppFooter; 