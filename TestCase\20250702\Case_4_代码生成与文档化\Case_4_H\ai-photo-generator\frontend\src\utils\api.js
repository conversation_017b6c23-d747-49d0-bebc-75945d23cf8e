import axios from 'axios';

/**
 * 创建axios实例
 */
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

/**
 * 请求拦截器
 * 在请求发送前添加认证token
 */
api.interceptors.request.use(
  (config) => {
    // 从localStorage或sessionStorage获取token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    
    // 如果有token，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 处理常见的响应错误
 */
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 清除token
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      
      // 如果不是登录页面，重定向到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    
    // 处理403禁止访问错误
    if (error.response && error.response.status === 403) {
      console.error('权限不足，无法访问此资源');
    }
    
    // 处理500服务器错误
    if (error.response && error.response.status >= 500) {
      console.error('服务器错误，请稍后再试');
    }
    
    return Promise.reject(error);
  }
);

export default api;