# AI证件照生成平台技术文档

## 1. 系统架构

### 1.1 整体架构

AI证件照生成平台采用前后端分离的架构设计，主要分为以下几个部分：

1. **前端应用**：基于React.js构建的单页面应用，负责用户界面展示和交互
2. **后端API服务**：基于Node.js和Express构建的RESTful API服务，处理业务逻辑
3. **AI模型服务**：负责照片处理和证件照生成的AI模型服务（预留接口）
4. **文件存储服务**：用于存储用户上传的原始照片和生成的证件照
5. **数据库服务**：存储用户信息、任务记录和照片元数据

系统架构图：

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │
│  前端应用   │ <──> │  后端API    │ <──> │  数据库     │
│  (React.js) │      │  (Express)  │      │  (MySQL)    │
│             │      │             │      │             │
└─────────────┘      └──────┬──────┘      └─────────────┘
                            │
                     ┌──────┴──────┐
                     │             │
                     │  文件存储   │
                     │             │
                     └──────┬──────┘
                            │
                     ┌──────┴──────┐
                     │             │
                     │  AI模型服务 │
                     │             │
                     └─────────────┘
```

### 1.2 技术选型

#### 前端技术栈

- **框架**：React.js 18.2.0
- **UI组件库**：Ant Design 5.4.0
- **路由**：React Router 6.10.0
- **HTTP客户端**：Axios 1.3.5
- **样式处理**：SCSS

#### 后端技术栈

- **运行环境**：Node.js
- **Web框架**：Express 4.18.2
- **文件上传**：Multer 1.4.5-lts.1
- **数据库ORM**：Sequelize 6.31.0（预留）
- **数据库**：MySQL（预留）
- **认证**：JWT (jsonwebtoken 9.0.0)
- **密码加密**：bcryptjs 2.4.3
- **数据验证**：express-validator 7.0.1
- **唯一ID生成**：UUID 9.0.0

## 2. 数据库设计

### 2.1 ER图

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│    users    │       │   uploads   │       │ generation_ │
│             │       │             │       │    tasks    │
│ id          │       │ id          │       │ id          │
│ username    │       │ user_id     │──┐    │ upload_id   │──┐
│ email       │       │ file_path   │  │    │ spec        │  │
│ password    │       │ file_size   │  │    │ background  │  │
│ phone       │       │ file_type   │  │    │ status      │  │
│ credits     │       │ upload_time │  │    │ created_at  │  │
│ created_at  │       └──────┬──────┘  │    │ completed_at│  │
│ updated_at  │              │         │    └──────┬──────┘  │
└──────┬──────┘              │         │           │         │
       │                     │         │           │         │
       │                     │         │           │         │
       │                     ▼         │           ▼         │
       │              ┌─────────────┐  │    ┌─────────────┐  │
       └─────────────►│  user_id    │◄─┘    │  task_id    │◄─┘
                      └─────────────┘       └─────────────┘
                                                  │
                                                  │
                                                  ▼
                                           ┌─────────────┐
                                           │ generated_  │
                                           │   photos    │
                                           │ id          │
                                           │ task_id     │
                                           │ file_path   │
                                           │ quality_score│
                                           │ created_at  │
                                           └─────────────┘
```

### 2.2 表结构

#### 用户表 (users)

| 字段名      | 类型         | 描述         | 约束                |
|------------|--------------|-------------|---------------------|
| id         | INT          | 用户ID       | PRIMARY KEY, AUTO_INCREMENT |
| username   | VARCHAR(50)  | 用户名       | UNIQUE, NOT NULL    |
| email      | VARCHAR(100) | 邮箱地址     | UNIQUE, NOT NULL    |
| password   | VARCHAR(255) | 密码哈希     | NOT NULL            |
| phone      | VARCHAR(20)  | 手机号       | NOT NULL            |
| credits    | INT          | 积分/次数    | DEFAULT 0           |
| created_at | TIMESTAMP    | 创建时间     | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP    | 更新时间     | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |

#### 上传记录表 (uploads)

| 字段名           | 类型         | 描述         | 约束                |
|-----------------|--------------|-------------|---------------------|
| id              | VARCHAR(50)  | 上传ID       | PRIMARY KEY         |
| user_id         | INT          | 用户ID       | FOREIGN KEY         |
| original_filename | VARCHAR(255) | 原始文件名   | NOT NULL            |
| file_path       | VARCHAR(500) | 文件路径     | NOT NULL            |
| file_size       | INT          | 文件大小     | NOT NULL            |
| file_type       | VARCHAR(20)  | 文件类型     | NOT NULL            |
| upload_time     | TIMESTAMP    | 上传时间     | DEFAULT CURRENT_TIMESTAMP |

#### 生成任务表 (generation_tasks)

| 字段名       | 类型         | 描述         | 约束                |
|-------------|--------------|-------------|---------------------|
| id          | VARCHAR(50)  | 任务ID       | PRIMARY KEY         |
| upload_id   | VARCHAR(50)  | 上传ID       | FOREIGN KEY         |
| spec        | VARCHAR(20)  | 证件照规格   | NOT NULL            |
| background  | VARCHAR(20)  | 背景颜色     | NOT NULL            |
| status      | ENUM         | 任务状态     | ('pending', 'processing', 'completed', 'failed') |
| created_at  | TIMESTAMP    | 创建时间     | DEFAULT CURRENT_TIMESTAMP |
| completed_at | TIMESTAMP    | 完成时间     | NULL                |

#### 生成照片表 (generated_photos)

| 字段名        | 类型         | 描述         | 约束                |
|--------------|--------------|-------------|---------------------|
| id           | VARCHAR(50)  | 照片ID       | PRIMARY KEY         |
| task_id      | VARCHAR(50)  | 任务ID       | FOREIGN KEY         |
| file_path    | VARCHAR(500) | 文件路径     | NOT NULL            |
| quality_score | DECIMAL(3,2) | 质量评分     | NOT NULL            |
| created_at   | TIMESTAMP    | 创建时间     | DEFAULT CURRENT_TIMESTAMP |

## 3. API设计

### 3.1 认证API

#### 3.1.1 用户注册

- **URL**: `/api/auth/register`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "username": "user123",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "13800138000"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "message": "注册成功",
    "user": {
      "id": 1,
      "username": "user123",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "credits": 10
    }
  }
  ```

#### 3.1.2 用户登录

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **请求体**:
  ```json
  {
    "username": "user123",
    "password": "password123"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "message": "登录成功",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "user123",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "credits": 10
    }
  }
  ```

### 3.2 上传API

#### 3.2.1 上传单张照片

- **URL**: `/api/upload`
- **方法**: `POST`
- **请求头**:
  ```
  Content-Type: multipart/form-data
  x-auth-token: <JWT令牌>
  ```
- **表单数据**:
  - `file`: 照片文件
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "上传成功",
    "data": {
      "upload_id": "unique_upload_id",
      "file_url": "https://example.com/uploads/xxx.jpg",
      "original_filename": "photo.jpg",
      "file_size": 1024000,
      "file_type": "image/jpeg"
    }
  }
  ```

#### 3.2.2 批量上传照片

- **URL**: `/api/upload/batch`
- **方法**: `POST`
- **请求头**:
  ```
  Content-Type: multipart/form-data
  x-auth-token: <JWT令牌>
  ```
- **表单数据**:
  - `files`: 照片文件数组（最多5张）
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "批量上传成功",
    "data": {
      "upload_id": "unique_upload_id",
      "files": [
        {
          "file_url": "https://example.com/uploads/xxx1.jpg",
          "original_filename": "photo1.jpg",
          "file_size": 1024000,
          "file_type": "image/jpeg"
        },
        {
          "file_url": "https://example.com/uploads/xxx2.jpg",
          "original_filename": "photo2.jpg",
          "file_size": 1048576,
          "file_type": "image/jpeg"
        }
      ],
      "total_count": 2
    }
  }
  ```

### 3.3 照片处理API

#### 3.3.1 生成证件照

- **URL**: `/api/photo/generate`
- **方法**: `POST`
- **请求头**:
  ```
  Content-Type: application/json
  x-auth-token: <JWT令牌>
  ```
- **请求体**:
  ```json
  {
    "upload_id": "unique_upload_id",
    "spec": "1inch",
    "background": "blue",
    "count": 5,
    "model_config": {
      "model_name": "default_model",
      "parameters": {
        "quality": "high",
        "style": "formal"
      }
    }
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "生成任务已创建",
    "data": {
      "task_id": "unique_task_id",
      "status": "processing",
      "generated_photos": []
    }
  }
  ```

#### 3.3.2 获取任务状态

- **URL**: `/api/photo/task/:taskId`
- **方法**: `GET`
- **请求头**:
  ```
  x-auth-token: <JWT令牌>
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "data": {
      "task_id": "unique_task_id",
      "status": "completed",
      "progress": 100,
      "spec": "1inch",
      "background": "blue",
      "created_at": "2024-07-01T10:30:00Z",
      "completed_at": "2024-07-01T10:30:30Z",
      "generated_photos": [
        {
          "id": "photo_1",
          "url": "/uploads/generated/photo_1.jpg",
          "quality_score": 0.95
        },
        {
          "id": "photo_2",
          "url": "/uploads/generated/photo_2.jpg",
          "quality_score": 0.92
        }
      ]
    }
  }
  ```

#### 3.3.3 下载照片

- **URL**: `/api/photo/download`
- **方法**: `POST`
- **请求头**:
  ```
  Content-Type: application/json
  x-auth-token: <JWT令牌>
  ```
- **请求体**:
  ```json
  {
    "photo_ids": ["photo_1", "photo_2"]
  }
  ```
- **成功响应** (200):
  ```json
  {
    "code": 200,
    "message": "下载请求成功",
    "data": {
      "download_id": "unique_download_id",
      "photos": [
        {
          "id": "photo_1",
          "url": "/uploads/generated/photo_1.jpg",
          "download_url": "/api/photo/download/photo_1"
        },
        {
          "id": "photo_2",
          "url": "/uploads/generated/photo_2.jpg",
          "download_url": "/api/photo/download/photo_2"
        }
      ],
      "total_count": 2
    }
  }
  ```

### 3.4 用户API

#### 3.4.1 获取用户个人资料

- **URL**: `/api/user/profile`
- **方法**: `GET`
- **请求头**:
  ```
  x-auth-token: <JWT令牌>
  ```
- **成功响应** (200):
  ```json
  {
    "id": 1,
    "username": "user123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "credits": 10,
    "createdAt": "2024-01-15T08:30:00Z",
    "lastLogin": "2024-07-01T14:30:22Z"
  }
  ```

#### 3.4.2 更新用户个人资料

- **URL**: `/api/user/profile`
- **方法**: `PUT`
- **请求头**:
  ```
  Content-Type: application/json
  x-auth-token: <JWT令牌>
  ```
- **请求体**:
  ```json
  {
    "username": "new_username",
    "email": "<EMAIL>",
    "phone": "13900139000"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "message": "个人资料更新成功",
    "user": {
      "id": 1,
      "username": "new_username",
      "email": "<EMAIL>",
      "phone": "13900139000",
      "credits": 10
    }
  }
  ```

## 4. 前端设计

### 4.1 页面结构

- **首页** (`HomePage.js`): 展示产品介绍、特点和使用流程
- **上传页面** (`UploadPage.js`): 提供照片上传和规格选择功能
- **结果页面** (`ResultPage.js`): 展示生成的证件照，提供预览和下载功能
- **登录页面** (`LoginPage.js`): 用户登录界面
- **注册页面** (`RegisterPage.js`): 用户注册界面
- **个人中心** (`UserProfilePage.js`): 用户个人资料、使用记录和账户安全设置
- **404页面** (`NotFoundPage.js`): 页面不存在提示

### 4.2 组件设计

#### 4.2.1 布局组件

- **MainLayout**: 主布局组件，包含页头、页脚和内容区域

#### 4.2.2 功能组件

- **PhotoUploader**: 照片上传组件
- **SpecSelector**: 证件照规格选择组件
- **PhotoPreview**: 照片预览组件
- **PhotoGrid**: 照片网格展示组件
- **TaskProgress**: 任务进度展示组件

### 4.3 路由设计

```javascript
<Routes>
  <Route path="/" element={<MainLayout />}>
    <Route index element={<HomePage />} />
    <Route path="upload" element={<UploadPage />} />
    <Route path="result/:taskId" element={<ResultPage />} />
    <Route path="profile" element={<UserProfilePage />} />
  </Route>
  <Route path="/login" element={<LoginPage />} />
  <Route path="/register" element={<RegisterPage />} />
  <Route path="*" element={<NotFoundPage />} />
</Routes>
```

## 5. AI模型接口设计

### 5.1 模型接口规范

```javascript
interface AIModelInterface {
  // 模型名称
  modelName: string;
  
  // 模型版本
  version: string;
  
  // 模型调用方法
  generatePhoto(input: {
    imageUrl: string;
    spec: PhotoSpec;
    background: BackgroundColor;
    options?: any;
  }): Promise<GeneratedPhoto[]>;
  
  // 模型健康检查
  healthCheck(): Promise<boolean>;
}
```

### 5.2 模型配置示例

```json
{
  "models": [
    {
      "name": "default_model",
      "endpoint": "https://api.example.com/generate",
      "api_key": "your_api_key",
      "timeout": 30000,
      "retry_count": 3
    },
    {
      "name": "custom_model_v1",
      "endpoint": "https://custom-api.example.com/process",
      "headers": {
        "Authorization": "Bearer token",
        "Content-Type": "application/json"
      }
    }
  ]
}
```

## 6. 部署方案

### 6.1 开发环境

- **前端**：使用React开发服务器（`npm start`）
- **后端**：使用Nodemon运行（`npm run dev`）
- **数据库**：本地MySQL实例

### 6.2 生产环境

#### 6.2.1 前端部署

1. 构建静态文件：`npm run build`
2. 将构建产物部署到Web服务器（Nginx/Apache）或CDN

#### 6.2.2 后端部署

1. 使用PM2进行Node.js应用管理
2. 配置Nginx作为反向代理
3. 设置SSL证书实现HTTPS

#### 6.2.3 数据库部署

1. 使用云数据库服务或自建MySQL服务器
2. 配置主从复制实现高可用
3. 定期备份数据

### 6.3 扩展性考虑

1. 使用负载均衡器分发请求
2. 将文件存储迁移到云存储服务（如OSS/S3）
3. 使用Redis缓存热点数据
4. 引入消息队列处理异步任务

## 7. 安全措施

### 7.1 认证与授权

- 使用JWT进行用户认证
- 基于角色的访问控制
- 令牌过期机制

### 7.2 数据安全

- 密码加密存储（bcrypt）
- HTTPS传输加密
- 敏感信息脱敏

### 7.3 防护措施

- 输入验证和过滤
- 防止SQL注入
- 防止XSS攻击
- 防止CSRF攻击
- 文件上传验证和限制

## 8. 性能优化

### 8.1 前端优化

- 代码分割和懒加载
- 资源压缩和CDN加速
- 图片优化和延迟加载
- 缓存策略

### 8.2 后端优化

- 数据库索引优化
- 查询优化
- 连接池管理
- 缓存热点数据

### 8.3 文件处理优化

- 图片压缩和格式转换
- 分布式存储
- CDN加速

## 9. 监控与日志

### 9.1 系统监控

- 服务器资源监控
- API请求监控
- 错误率监控
- 性能指标监控

### 9.2 日志管理

- 访问日志
- 错误日志
- 操作日志
- 安全审计日志

## 10. 测试策略

### 10.1 单元测试

- 前端组件测试
- 后端API测试
- 工具函数测试

### 10.2 集成测试

- API集成测试
- 前后端交互测试

### 10.3 性能测试

- 负载测试
- 压力测试
- 并发测试

### 10.4 安全测试

- 渗透测试
- 漏洞扫描

## 11. 维护与更新

### 11.1 版本管理

- 语义化版本控制
- 变更日志维护

### 11.2 持续集成/持续部署

- 自动化构建
- 自动化测试
- 自动化部署

### 11.3 问题跟踪

- Bug跟踪系统
- 用户反馈收集
- 性能监控告警

## 12. 总结

AI证件照生成平台采用现代化的前后端分离架构，前端使用React.js构建用户界面，后端使用Node.js和Express提供API服务。系统设计考虑了可扩展性、安全性和性能优化，预留了自定义AI模型接口，具有良好的扩展性和可维护性。

通过分阶段开发和测试，确保项目能够按时交付并满足用户需求。未来可以根据用户反馈和业务需求，进一步优化系统性能，增强AI模型能力，提升用户体验。