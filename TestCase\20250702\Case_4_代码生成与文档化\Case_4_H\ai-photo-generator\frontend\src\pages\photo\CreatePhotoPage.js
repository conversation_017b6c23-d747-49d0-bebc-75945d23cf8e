import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Steps, 
  Button, 
  Card, 
  Row, 
  Col, 
  Upload, 
  message, 
  Form, 
  Select, 
  Radio, 
  Divider,
  Alert
} from 'antd';
import { 
  UploadOutlined, 
  CameraOutlined, 
  SettingOutlined, 
  CheckOutlined, 
  InboxOutlined 
} from '@ant-design/icons';
import { 
  uploadPhoto, 
  generatePhoto, 
  selectPhotoLoading, 
  selectPhotoError,
  selectCurrentUpload
} from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS, BACKGROUND_COLORS } from '../../utils/constants';
import PhotoPreview from '../../components/photo/PhotoPreview';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;
const { Option } = Select;
const { Dragger } = Upload;

/**
 * 创建照片页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const CreatePhotoPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const loading = useSelector(selectPhotoLoading);
  const error = useSelector(selectPhotoError);
  const currentUpload = useSelector(selectCurrentUpload);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState(null);
  
  /**
   * 处理文件上传
   * 
   * @param {Object} info - 上传信息
   */
  const handleUpload = (info) => {
    const file = info.file;
    
    // 验证文件类型
    const isImage = file.type === 'image/jpeg' || 
                    file.type === 'image/png' || 
                    file.type === 'image/jpg';
    
    if (!isImage) {
      message.error('只能上传JPG/PNG格式的图片!');
      return;
    }
    
    // 验证文件大小
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过5MB!');
      return;
    }
    
    // 更新文件列表
    setFileList([file]);
    
    // 创建预览
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewImage(reader.result);
    };
    reader.readAsDataURL(file);
  };
  
  /**
   * 处理步骤变化
   * 
   * @param {number} step - 步骤索引
   */
  const handleStepChange = (step) => {
    // 如果是从第一步到第二步，需要先上传文件
    if (currentStep === 0 && step === 1) {
      if (fileList.length === 0) {
        message.error('请先上传照片');
        return;
      }
      
      // 上传照片
      dispatch(uploadPhoto({ file: fileList[0] }))
        .unwrap()
        .then(() => {
          setCurrentStep(1);
        })
        .catch((err) => {
          message.error('上传照片失败: ' + err.message);
        });
    } else {
      setCurrentStep(step);
    }
  };
  
  /**
   * 处理表单提交
   * 
   * @param {Object} values - 表单值
   */
  const handleSubmit = (values) => {
    if (!currentUpload?.id) {
      message.error('请先上传照片');
      return;
    }
    
    // 生成照片
    dispatch(generatePhoto({
      photoId: currentUpload.id,
      spec: values.spec,
      backgroundColor: values.backgroundColor,
      enhanceQuality: values.enhanceQuality === 'yes',
      removeBackground: true,
    }))
      .unwrap()
      .then((result) => {
        message.success('照片生成成功');
        navigate(`${ROUTES.PHOTO_RESULT}/${result.id}`);
      })
      .catch((err) => {
        message.error('生成照片失败: ' + err.message);
      });
  };
  
  // 步骤内容
  const steps = [
    {
      title: '上传照片',
      icon: <UploadOutlined />,
      content: (
        <div className="upload-step">
          <Paragraph>
            请上传一张清晰的正面照片，确保面部完全可见，无遮挡。
          </Paragraph>
          
          <div className="upload-area" style={{ marginTop: 24 }}>
            <Dragger
              name="photo"
              multiple={false}
              fileList={fileList}
              beforeUpload={() => false}
              onChange={(info) => handleUpload(info)}
              showUploadList={false}
              disabled={loading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽照片到此区域上传</p>
              <p className="ant-upload-hint">
                支持JPG、PNG格式，文件大小不超过5MB
              </p>
            </Dragger>
          </div>
          
          {previewImage && (
            <div className="preview-container" style={{ marginTop: 24, textAlign: 'center' }}>
              <Title level={5}>预览</Title>
              <img 
                src={previewImage} 
                alt="预览" 
                style={{ 
                  maxWidth: '100%', 
                  maxHeight: 300, 
                  margin: '0 auto',
                  borderRadius: 4,
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                }} 
              />
            </div>
          )}
        </div>
      ),
    },
    {
      title: '设置参数',
      icon: <SettingOutlined />,
      content: (
        <div className="settings-step">
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              spec: 'id_photo_2_inch',
              backgroundColor: 'white',
              enhanceQuality: 'yes',
            }}
            onFinish={handleSubmit}
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="spec"
                  label="证件照规格"
                  rules={[{ required: true, message: '请选择证件照规格' }]}
                >
                  <Select placeholder="选择证件照规格" disabled={loading}>
                    {Object.entries(PHOTO_SPECS).map(([key, spec]) => (
                      <Option key={key} value={key}>
                        {spec.name} ({spec.width}mm × {spec.height}mm)
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                
                <Form.Item
                  name="backgroundColor"
                  label="背景颜色"
                  rules={[{ required: true, message: '请选择背景颜色' }]}
                >
                  <Radio.Group disabled={loading}>
                    {Object.entries(BACKGROUND_COLORS).map(([key, color]) => (
                      <Radio.Button 
                        key={key} 
                        value={key}
                        style={{ 
                          backgroundColor: color.hex,
                          color: ['white', 'blue', 'red'].includes(key) ? '#fff' : '#000',
                          marginRight: 8,
                          marginBottom: 8,
                        }}
                      >
                        {color.name}
                      </Radio.Button>
                    ))}
                  </Radio.Group>
                </Form.Item>
                
                <Form.Item
                  name="enhanceQuality"
                  label="照片增强"
                  rules={[{ required: true, message: '请选择是否增强照片质量' }]}
                >
                  <Radio.Group disabled={loading}>
                    <Radio value="yes">是，增强照片质量</Radio>
                    <Radio value="no">否，保持原样</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
              
              <Col xs={24} md={12}>
                {currentUpload && (
                  <div className="preview-container">
                    <Title level={5}>照片预览</Title>
                    <PhotoPreview 
                      photo={currentUpload} 
                      spec={form.getFieldValue('spec')}
                      backgroundColor={form.getFieldValue('backgroundColor')}
                    />
                    <Text type="secondary">
                      预览效果仅供参考，实际效果可能有所不同
                    </Text>
                  </div>
                )}
              </Col>
            </Row>
            
            {error && (
              <Alert
                message="操作失败"
                description={error}
                type="error"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )}
            
            <Divider />
            
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className="btn-primary"
              >
                生成证件照
              </Button>
            </Form.Item>
          </Form>
        </div>
      ),
    },
  ];
  
  return (
    <div className="create-photo-page page-container">
      <div className="page-title">
        <Title level={2}>创建证件照</Title>
      </div>
      
      <Card bordered={false}>
        <Steps
          current={currentStep}
          onChange={handleStepChange}
          items={steps.map((step) => ({
            title: step.title,
            icon: step.icon,
          }))}
          style={{ marginBottom: 40 }}
        />
        
        <div className="steps-content">
          {steps[currentStep].content}
        </div>
        
        <div className="steps-action" style={{ marginTop: 24, textAlign: 'center' }}>
          {currentStep > 0 && (
            <Button 
              style={{ margin: '0 8px' }} 
              onClick={() => setCurrentStep(currentStep - 1)}
              disabled={loading}
            >
              上一步
            </Button>
          )}
          
          {currentStep < steps.length - 1 && (
            <Button 
              type="primary" 
              onClick={() => handleStepChange(currentStep + 1)}
              loading={loading && currentStep === 0}
              disabled={fileList.length === 0 && currentStep === 0}
            >
              下一步
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};

export default CreatePhotoPage;