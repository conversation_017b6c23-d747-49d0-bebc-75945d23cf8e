# AI证件照生成平台 API 文档

## 概述

本文档描述了AI证件照生成平台的RESTful API接口。所有API请求都使用JSON格式，响应也是JSON格式。

### 基础信息

- **基础URL**: `http://localhost:3000/api`
- **API版本**: v1.0
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

### 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "errors": []
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 413 | 文件大小超过限制 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 认证接口

### 用户注册

**POST** `/user/register`

注册新用户账户。

**请求参数:**
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例:**
```json
{
  "code": 201,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "credits": 10,
      "createdAt": "2024-01-01T10:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 用户登录

**POST** `/user/login`

用户登录获取访问令牌。

**请求参数:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "credits": 10,
      "lastLoginAt": "2024-01-01T10:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 获取用户信息

**GET** `/user/profile`

获取当前登录用户的详细信息。

**请求头:**
```
Authorization: Bearer <token>
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "avatar": null,
      "credits": 10,
      "isActive": true,
      "lastLoginAt": "2024-01-01T10:00:00.000Z",
      "createdAt": "2024-01-01T09:00:00.000Z",
      "updatedAt": "2024-01-01T10:00:00.000Z"
    }
  }
}
```

## 文件上传接口

### 单文件上传

**POST** `/upload/single`

上传单张照片文件。

**请求类型:** `multipart/form-data`

**请求参数:**
- `file`: 图片文件 (必需)
- `spec`: 证件照规格 (可选)

**响应示例:**
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "uploadId": "upload_123456",
    "fileUrl": "/uploads/original/original_123456.jpg",
    "originalFilename": "photo.jpg",
    "fileSize": 2048576,
    "metadata": {
      "width": 800,
      "height": 1200,
      "format": "jpeg"
    }
  }
}
```

### 多文件上传

**POST** `/upload/multiple`

批量上传多张照片文件。

**请求类型:** `multipart/form-data`

**请求参数:**
- `files`: 图片文件数组 (必需，最多5个)
- `spec`: 证件照规格 (可选)

**响应示例:**
```json
{
  "code": 200,
  "message": "成功上传 3 个文件",
  "data": {
    "uploads": [
      {
        "uploadId": "upload_123456",
        "fileUrl": "/uploads/original/original_123456.jpg",
        "originalFilename": "photo1.jpg",
        "fileSize": 2048576,
        "metadata": {...}
      },
      {
        "uploadId": "upload_123457",
        "fileUrl": "/uploads/original/original_123457.jpg",
        "originalFilename": "photo2.jpg",
        "fileSize": 1536000,
        "metadata": {...}
      }
    ],
    "totalCount": 2
  }
}
```

### 获取上传记录

**GET** `/upload/{uploadId}`

获取指定上传记录的详细信息。

**路径参数:**
- `uploadId`: 上传ID

**响应示例:**
```json
{
  "code": 200,
  "message": "获取上传记录成功",
  "data": {
    "uploadId": "upload_123456",
    "originalFilename": "photo.jpg",
    "fileSize": 2048576,
    "fileType": "image/jpeg",
    "uploadTime": "2024-01-01T10:00:00.000Z",
    "metadata": {
      "width": 800,
      "height": 1200,
      "format": "jpeg"
    },
    "status": "uploaded"
  }
}
```

## 证件照生成接口

### 创建生成任务

**POST** `/generate`

创建证件照生成任务。

**请求参数:**
```json
{
  "uploadId": "upload_123456",
  "spec": "1inch",
  "background": "blue",
  "count": 5,
  "modelConfig": {
    "modelName": "default_model",
    "parameters": {
      "quality": "high",
      "style": "formal"
    }
  },
  "customSpec": {
    "width": 295,
    "height": 413,
    "name": "自定义规格"
  }
}
```

**参数说明:**
- `uploadId`: 上传ID (必需)
- `spec`: 证件照规格 (必需) - `1inch`, `2inch`, `small2inch`, `big1inch`, `custom`
- `background`: 背景颜色 (必需) - `blue`, `red`, `white`, `lightblue`, `lightgray`
- `count`: 生成数量 (可选，默认5，最大10)
- `modelConfig`: 模型配置 (可选)
- `customSpec`: 自定义规格 (当spec为custom时必需)

**响应示例:**
```json
{
  "code": 200,
  "message": "生成任务创建成功",
  "data": {
    "taskId": "task_123456",
    "status": "pending",
    "progress": 0,
    "spec": "1inch",
    "background": "blue",
    "createdAt": "2024-01-01T10:00:00.000Z"
  }
}
```

### 获取任务状态

**GET** `/generate/task/{taskId}`

获取生成任务的当前状态和进度。

**路径参数:**
- `taskId`: 任务ID

**响应示例:**
```json
{
  "code": 200,
  "message": "获取任务状态成功",
  "data": {
    "taskId": "task_123456",
    "status": "completed",
    "progress": 100,
    "spec": "1inch",
    "background": "blue",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "completedAt": "2024-01-01T10:02:00.000Z",
    "generatedPhotos": [
      {
        "id": "photo_123456_1",
        "url": "/uploads/generated/generated_123456_1.jpg",
        "qualityScore": 0.95,
        "width": 295,
        "height": 413,
        "fileSize": 156800,
        "downloadCount": 0
      }
    ]
  }
}
```

### 获取用户任务列表

**GET** `/generate/user/tasks`

获取当前用户的生成任务列表。

**查询参数:**
- `page`: 页码 (可选，默认1)
- `limit`: 每页数量 (可选，默认10)
- `status`: 任务状态过滤 (可选)

**响应示例:**
```json
{
  "code": 200,
  "message": "获取任务列表成功",
  "data": {
    "tasks": [
      {
        "taskId": "task_123456",
        "status": "completed",
        "progress": 100,
        "spec": "1inch",
        "background": "blue",
        "createdAt": "2024-01-01T10:00:00.000Z",
        "completedAt": "2024-01-01T10:02:00.000Z",
        "originalFilename": "photo.jpg",
        "generatedCount": 5
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 1,
      "totalCount": 1,
      "limit": 10
    }
  }
}
```

## 任务管理接口

### 获取任务详情

**GET** `/task/{taskId}`

获取任务的详细信息，包括生成的所有照片。

**路径参数:**
- `taskId`: 任务ID

**响应示例:**
```json
{
  "code": 200,
  "message": "获取任务详情成功",
  "data": {
    "taskId": "task_123456",
    "status": "completed",
    "progress": 100,
    "spec": "1inch",
    "background": "blue",
    "modelName": "default_model",
    "createdAt": "2024-01-01T10:00:00.000Z",
    "completedAt": "2024-01-01T10:02:00.000Z",
    "upload": {
      "originalFilename": "photo.jpg",
      "fileSize": 2048576,
      "uploadTime": "2024-01-01T09:58:00.000Z"
    },
    "generatedPhotos": [
      {
        "id": "photo_123456_1",
        "url": "/uploads/generated/generated_123456_1.jpg",
        "qualityScore": 0.95,
        "width": 295,
        "height": 413,
        "fileSize": 156800,
        "downloadCount": 0,
        "createdAt": "2024-01-01T10:02:00.000Z"
      }
    ]
  }
}
```

### 下载单张照片

**GET** `/task/photo/{photoId}/download`

下载指定的生成照片。

**路径参数:**
- `photoId`: 照片ID

**响应:** 文件流 (image/jpeg)

### 批量下载任务照片

**GET** `/task/{taskId}/download`

批量下载任务的所有生成照片（ZIP格式）。

**路径参数:**
- `taskId`: 任务ID

**响应:** 文件流 (application/zip)

### 删除任务

**DELETE** `/task/{taskId}`

删除指定任务及其相关数据。

**路径参数:**
- `taskId`: 任务ID

**响应示例:**
```json
{
  "code": 200,
  "message": "任务删除成功",
  "data": null
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数验证失败",
  "errors": [
    "用户名长度必须在3-50个字符之间",
    "邮箱格式不正确"
  ]
}
```

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 用户不存在 | 指定的用户ID不存在 |
| 1002 | 密码错误 | 登录密码不正确 |
| 1003 | 账户已被禁用 | 用户账户被管理员禁用 |
| 2001 | 文件类型不支持 | 上传的文件格式不支持 |
| 2002 | 文件大小超过限制 | 文件大小超过10MB限制 |
| 2003 | 文件数量超过限制 | 批量上传文件数量超过5个 |
| 3001 | 任务不存在 | 指定的任务ID不存在 |
| 3002 | 任务已完成 | 尝试操作已完成的任务 |
| 3003 | AI服务不可用 | AI生成服务暂时不可用 |

## 限制说明

### 文件上传限制

- 支持格式：JPG、JPEG、PNG
- 单文件大小：最大10MB
- 批量上传：最多5个文件
- 文件名长度：最大255个字符

### 生成任务限制

- 单次生成数量：最多10张
- 并发任务数：每用户最多3个
- 任务保留时间：30天

### API调用限制

- 认证接口：每IP每分钟最多10次
- 上传接口：每用户每分钟最多5次
- 生成接口：每用户每分钟最多3次
- 下载接口：每用户每分钟最多20次
