const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const { Upload } = require('../models');
const { 
  getImageMetadata, 
  generateUniqueFileName,
  ensureDirectoryExists 
} = require('../utils/fileUtils');
const { validateFile, validateFiles } = require('../utils/validation');
const { originalDir } = require('../middleware/upload');
const logger = require('../utils/logger');

class UploadController {
  /**
   * 单文件上传
   */
  async uploadSingle(req, res, next) {
    try {
      if (!req.file) {
        return res.status(400).json({
          code: 400,
          message: '请选择要上传的文件',
          errors: ['文件不能为空']
        });
      }

      // 验证文件
      const validation = validateFile(req.file);
      if (!validation.isValid) {
        // 删除临时文件
        await fs.unlink(req.file.path).catch(() => {});
        return res.status(400).json({
          code: 400,
          message: '文件验证失败',
          errors: validation.errors
        });
      }

      // 生成上传记录ID
      const uploadId = uuidv4();
      
      // 生成最终文件名和路径
      const finalFileName = generateUniqueFileName(req.file.originalname, 'original_');
      const finalPath = path.join(originalDir, finalFileName);
      
      // 确保目标目录存在
      await ensureDirectoryExists(originalDir);
      
      // 移动文件到最终位置
      await fs.rename(req.file.path, finalPath);
      
      // 获取图片元数据
      const metadata = await getImageMetadata(finalPath);
      
      // 创建上传记录
      const upload = await Upload.create({
        id: uploadId,
        userId: req.user?.id || null,
        originalFilename: req.file.originalname,
        filePath: finalPath,
        fileSize: req.file.size,
        fileType: req.file.mimetype,
        metadata: metadata,
        status: 'uploaded'
      });

      logger.info('文件上传成功', {
        uploadId,
        userId: req.user?.id,
        filename: req.file.originalname,
        size: req.file.size
      });

      res.json({
        code: 200,
        message: '文件上传成功',
        data: {
          uploadId: upload.id,
          fileUrl: `/uploads/original/${finalFileName}`,
          originalFilename: upload.originalFilename,
          fileSize: upload.fileSize,
          metadata: upload.metadata
        }
      });
    } catch (error) {
      // 清理临时文件
      if (req.file?.path) {
        await fs.unlink(req.file.path).catch(() => {});
      }
      next(error);
    }
  }

  /**
   * 多文件上传
   */
  async uploadMultiple(req, res, next) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '请选择要上传的文件',
          errors: ['文件不能为空']
        });
      }

      // 验证所有文件
      const validation = validateFiles(req.files);
      if (!validation.isValid) {
        // 删除所有临时文件
        await Promise.all(
          req.files.map(file => fs.unlink(file.path).catch(() => {}))
        );
        return res.status(400).json({
          code: 400,
          message: '文件验证失败',
          errors: validation.errors
        });
      }

      const uploadResults = [];
      
      // 确保目标目录存在
      await ensureDirectoryExists(originalDir);

      // 处理每个文件
      for (const file of req.files) {
        try {
          // 生成上传记录ID
          const uploadId = uuidv4();
          
          // 生成最终文件名和路径
          const finalFileName = generateUniqueFileName(file.originalname, 'original_');
          const finalPath = path.join(originalDir, finalFileName);
          
          // 移动文件到最终位置
          await fs.rename(file.path, finalPath);
          
          // 获取图片元数据
          const metadata = await getImageMetadata(finalPath);
          
          // 创建上传记录
          const upload = await Upload.create({
            id: uploadId,
            userId: req.user?.id || null,
            originalFilename: file.originalname,
            filePath: finalPath,
            fileSize: file.size,
            fileType: file.mimetype,
            metadata: metadata,
            status: 'uploaded'
          });

          uploadResults.push({
            uploadId: upload.id,
            fileUrl: `/uploads/original/${finalFileName}`,
            originalFilename: upload.originalFilename,
            fileSize: upload.fileSize,
            metadata: upload.metadata
          });

          logger.info('文件上传成功', {
            uploadId,
            userId: req.user?.id,
            filename: file.originalname,
            size: file.size
          });
        } catch (error) {
          logger.error('单个文件处理失败', {
            filename: file.originalname,
            error: error.message
          });
          // 删除临时文件
          await fs.unlink(file.path).catch(() => {});
        }
      }

      if (uploadResults.length === 0) {
        return res.status(500).json({
          code: 500,
          message: '所有文件处理失败',
          errors: ['文件处理过程中发生错误']
        });
      }

      res.json({
        code: 200,
        message: `成功上传 ${uploadResults.length} 个文件`,
        data: {
          uploads: uploadResults,
          totalCount: uploadResults.length
        }
      });
    } catch (error) {
      // 清理所有临时文件
      if (req.files) {
        await Promise.all(
          req.files.map(file => fs.unlink(file.path).catch(() => {}))
        );
      }
      next(error);
    }
  }

  /**
   * 获取上传记录
   */
  async getUpload(req, res, next) {
    try {
      const { uploadId } = req.params;
      
      const upload = await Upload.findByPk(uploadId);
      if (!upload) {
        return res.status(404).json({
          code: 404,
          message: '上传记录不存在',
          errors: ['指定的上传ID无效']
        });
      }

      // 检查权限（如果有用户登录）
      if (req.user && upload.userId && upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权访问此上传记录',
          errors: ['权限不足']
        });
      }

      res.json({
        code: 200,
        message: '获取上传记录成功',
        data: {
          uploadId: upload.id,
          originalFilename: upload.originalFilename,
          fileSize: upload.fileSize,
          fileType: upload.fileType,
          uploadTime: upload.uploadTime,
          metadata: upload.metadata,
          status: upload.status
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除上传记录
   */
  async deleteUpload(req, res, next) {
    try {
      const { uploadId } = req.params;
      
      const upload = await Upload.findByPk(uploadId);
      if (!upload) {
        return res.status(404).json({
          code: 404,
          message: '上传记录不存在',
          errors: ['指定的上传ID无效']
        });
      }

      // 检查权限
      if (req.user && upload.userId && upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权删除此上传记录',
          errors: ['权限不足']
        });
      }

      // 删除文件
      try {
        await fs.unlink(upload.filePath);
      } catch (error) {
        logger.warn('删除文件失败', {
          uploadId,
          filePath: upload.filePath,
          error: error.message
        });
      }

      // 删除数据库记录
      await upload.destroy();

      logger.info('上传记录删除成功', {
        uploadId,
        userId: req.user?.id
      });

      res.json({
        code: 200,
        message: '删除成功',
        data: null
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new UploadController();
