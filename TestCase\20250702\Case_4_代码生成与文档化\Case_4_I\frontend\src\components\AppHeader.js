import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Layout, Menu, Button, Avatar, Dropdown } from 'antd';
import { UserOutlined, UploadOutlined, HomeOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import { logout } from '../services/userSlice';
import './AppHeader.scss';

const { Header } = Layout;

const AppHeader = () => {
  const { isAuthenticated, currentUser } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">个人中心</Link>
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        <Link to="/profile/settings">账户设置</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  return (
    <Header className="app-header">
      <div className="logo">
        <Link to="/">AI证件照生成平台</Link>
      </div>
      <Menu theme="dark" mode="horizontal" className="main-menu">
        <Menu.Item key="home" icon={<HomeOutlined />}>
          <Link to="/">首页</Link>
        </Menu.Item>
        <Menu.Item key="upload" icon={<UploadOutlined />}>
          <Link to="/upload">上传照片</Link>
        </Menu.Item>
      </Menu>
      <div className="user-actions">
        {isAuthenticated ? (
          <Dropdown overlay={userMenu} placement="bottomRight">
            <div className="user-info">
              <Avatar icon={<UserOutlined />} src={currentUser?.avatar} />
              <span className="username">{currentUser?.username}</span>
            </div>
          </Dropdown>
        ) : (
          <>
            <Button type="text" onClick={() => navigate('/login')}>
              登录
            </Button>
            <Button type="primary" onClick={() => navigate('/register')}>
              注册
            </Button>
          </>
        )}
      </div>
    </Header>
  );
};

export default AppHeader;