import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Form, 
  Input, 
  Button, 
  Typography, 
  message, 
  Space, 
  Result 
} from 'antd';
import { MailOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { forgotPassword } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;

/**
 * 样式化组件
 */
const FormHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
`;

const FormFooter = styled.div`
  text-align: center;
  margin-top: 24px;
`;

const BackLink = styled(Link)`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  
  .anticon {
    margin-right: 8px;
  }
`;

/**
 * 忘记密码页面组件
 * 
 * @returns {JSX.Element} 忘记密码页面组件
 */
const ForgotPassword = () => {
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const { loading } = useSelector((state) => state.auth);
  
  // 本地状态
  const [form] = Form.useForm();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [email, setEmail] = useState('');
  
  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      await dispatch(forgotPassword({ email: values.email })).unwrap();
      setEmail(values.email);
      setIsSubmitted(true);
    } catch (error) {
      message.error(error?.message || '发送重置密码邮件失败，请稍后再试');
    }
  };
  
  // 如果已提交表单，显示成功信息
  if (isSubmitted) {
    return (
      <Result
        status="success"
        title="重置密码邮件已发送"
        subTitle={
          <div>
            <Paragraph>
              我们已向 <Text strong>{email}</Text> 发送了一封包含重置密码链接的邮件。
            </Paragraph>
            <Paragraph>
              请检查您的邮箱并点击邮件中的链接来重置密码。
            </Paragraph>
            <Paragraph type="secondary">
              如果您没有收到邮件，请检查垃圾邮件文件夹，或者
              <Button type="link" onClick={() => setIsSubmitted(false)} style={{ padding: 0 }}>
                重新发送
              </Button>
            </Paragraph>
          </div>
        }
        extra={[
          <Button type="primary" key="login" onClick={() => window.location.href = ROUTES.LOGIN}>
            返回登录
          </Button>,
        ]}
      />
    );
  }
  
  return (
    <>
      <BackLink to={ROUTES.LOGIN}>
        <ArrowLeftOutlined /> 返回登录
      </BackLink>
      
      <FormHeader>
        <Title level={2}>忘记密码</Title>
        <Text type="secondary">
          请输入您的邮箱地址，我们将向您发送重置密码的链接
        </Text>
      </FormHeader>
      
      <Form
        form={form}
        name="forgotPassword"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="email"
          rules={[
            { required: true, message: '请输入您的邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Input 
            prefix={<MailOutlined />} 
            placeholder="邮箱" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            size="large" 
            block 
            loading={loading}
          >
            发送重置链接
          </Button>
        </Form.Item>
      </Form>
      
      <FormFooter>
        <Space>
          <Text type="secondary">记起密码了？</Text>
          <Link to={ROUTES.LOGIN}>立即登录</Link>
        </Space>
      </FormFooter>
    </>
  );
};

export default ForgotPassword;