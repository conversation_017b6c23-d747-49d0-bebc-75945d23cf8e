/**
 * AI证件照生成平台 - 主脚本
 * 版本: 1.0.0
 */

// 全局变量
const API_BASE_URL = 'https://api.example.com'; // 实际开发中替换为真实API地址
let uploadedFiles = [];
let generatedPhotos = [];
let selectedPhotos = [];

// DOM元素
document.addEventListener('DOMContentLoaded', function() {
    // 页面导航和UI交互
    initUIInteractions();
    
    // 照片上传功能
    initPhotoUpload();
    
    // 证件照生成功能
    initPhotoGeneration();
    
    // 结果预览和下载功能
    initResultsHandling();
});

/**
 * 初始化UI交互
 */
function initUIInteractions() {
    // 移动端菜单切换
    const menuToggle = document.querySelector('.mobile-menu-toggle');
    const header = document.querySelector('.site-header');
    
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            header.classList.toggle('menu-open');
        });
    }
    
    // 快速开始按钮
    const startBtn = document.getElementById('start-btn');
    if (startBtn) {
        startBtn.addEventListener('click', function() {
            document.getElementById('upload-section').scrollIntoView({ behavior: 'smooth' });
        });
    }
    
    // FAQ折叠面板
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        question.addEventListener('click', function() {
            item.classList.toggle('active');
        });
    });
    
    // 模态框处理
    initModals();
}

/**
 * 初始化模态框
 */
function initModals() {
    // 登录模态框
    const loginBtn = document.getElementById('login-btn');
    const loginModal = document.getElementById('login-modal');
    
    if (loginBtn && loginModal) {
        loginBtn.addEventListener('click', function() {
            loginModal.style.display = 'flex';
        });
    }
    
    // 注册模态框
    const registerBtn = document.getElementById('register-btn');
    const registerModal = document.getElementById('register-modal');
    
    if (registerBtn && registerModal) {
        registerBtn.addEventListener('click', function() {
            registerModal.style.display = 'flex';
        });
    }
    
    // 模态框切换
    const showRegister = document.getElementById('show-register');
    const showLogin = document.getElementById('show-login');
    
    if (showRegister) {
        showRegister.addEventListener('click', function(e) {
            e.preventDefault();
            loginModal.style.display = 'none';
            registerModal.style.display = 'flex';
        });
    }
    
    if (showLogin) {
        showLogin.addEventListener('click', function(e) {
            e.preventDefault();
            registerModal.style.display = 'none';
            loginModal.style.display = 'flex';
        });
    }
    
    // 关闭模态框
    const closeButtons = document.querySelectorAll('.close-modal');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });
    });
    
    // 表单提交处理
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            // 模拟登录请求
            console.log('登录请求:', { email, password });
            showNotification('登录成功', 'success');
            loginModal.style.display = 'none';
        });
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.getElementById('register-username').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;
            
            if (password !== confirmPassword) {
                showNotification('两次输入的密码不一致', 'error');
                return;
            }
            
            // 模拟注册请求
            console.log('注册请求:', { username, email, password });
            showNotification('注册成功', 'success');
            registerModal.style.display = 'none';
        });
    }
}

/**
 * 初始化照片上传功能
 */
function initPhotoUpload() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    const previewContainer = document.getElementById('preview-container');
    const uploadPreview = document.getElementById('upload-preview');
    const generateBtn = document.getElementById('generate-btn');
    
    if (!uploadArea || !fileInput) return;
    
    // 点击上传区域触发文件选择
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('drag-over');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        handleFiles(files);
    });
    
    // 文件选择处理
    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });
    
    // 处理上传的文件
    function handleFiles(files) {
        if (!files || files.length === 0) return;
        
        // 检查文件数量限制
        if (uploadedFiles.length + files.length > 5) {
            showNotification('最多只能上传5张照片', 'error');
            return;
        }
        
        // 处理每个文件
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // 检查文件类型
            if (!['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
                showNotification(`不支持的文件格式: ${file.name}`, 'error');
                continue;
            }
            
            // 检查文件大小
            if (file.size > 10 * 1024 * 1024) { // 10MB
                showNotification(`文件过大: ${file.name}`, 'error');
                continue;
            }
            
            // 添加到上传文件列表
            uploadedFiles.push(file);
            
            // 创建预览
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="上传预览">
                    <span class="preview-remove" data-index="${uploadedFiles.length - 1}">&times;</span>
                `;
                uploadPreview.appendChild(previewItem);
                
                // 显示预览容器
                previewContainer.style.display = 'block';
                
                // 启用生成按钮
                generateBtn.disabled = false;
            };
            reader.readAsDataURL(file);
        }
        
        // 重置文件输入
        fileInput.value = '';
    }
    
    // 删除预览图片
    uploadPreview.addEventListener('click', function(e) {
        if (e.target.classList.contains('preview-remove')) {
            const index = parseInt(e.target.dataset.index);
            uploadedFiles.splice(index, 1);
            e.target.parentElement.remove();
            
            // 更新剩余预览图片的索引
            const removeButtons = uploadPreview.querySelectorAll('.preview-remove');
            removeButtons.forEach((button, i) => {
                button.dataset.index = i;
            });
            
            // 如果没有上传的文件，禁用生成按钮
            if (uploadedFiles.length === 0) {
                generateBtn.disabled = true;
                previewContainer.style.display = 'none';
            }
        }
    });
}

/**
 * 初始化证件照生成功能
 */
function initPhotoGeneration() {
    const generateBtn = document.getElementById('generate-btn');
    const uploadSection = document.getElementById('upload-section');
    const resultsSection = document.getElementById('results-section');
    const resultsGrid = document.getElementById('results-grid');
    
    if (!generateBtn) return;
    
    generateBtn.addEventListener('click', function() {
        if (uploadedFiles.length === 0) {
            showNotification('请先上传照片', 'error');
            return;
        }
        
        // 获取选择的规格和背景色
        const photoSpec = document.getElementById('photo-spec').value;
        const backgroundColor = document.querySelector('input[name="background"]:checked').value;
        
        // 显示加载状态
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span class="loading-spinner"></span> 生成中...';
        
        // 模拟API请求
        setTimeout(() => {
            // 生成模拟结果
            generatedPhotos = [];
            for (let i = 0; i < uploadedFiles.length; i++) {
                // 为每个上传的照片生成3-5个结果
                const resultCount = Math.floor(Math.random() * 3) + 3; // 3-5个结果
                
                for (let j = 0; j < resultCount; j++) {
                    const quality = Math.random().toFixed(2);
                    generatedPhotos.push({
                        id: `photo_${i}_${j}`,
                        originalFile: uploadedFiles[i],
                        url: URL.createObjectURL(uploadedFiles[i]), // 实际应该是API返回的URL
                        spec: photoSpec,
                        background: backgroundColor,
                        qualityScore: quality
                    });
                }
            }
            
            // 显示结果
            displayResults();
            
            // 切换到结果页面
            uploadSection.style.display = 'none';
            resultsSection.style.display = 'block';
            
            // 重置按钮状态
            generateBtn.disabled = false;
            generateBtn.innerHTML = '开始生成';
            
            // 滚动到结果区域
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }, 2000);
    });
    
    // 返回上传页面
    const backBtn = document.getElementById('back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            resultsSection.style.display = 'none';
            uploadSection.style.display = 'block';
        });
    }
    
    // 重新生成
    const regenerateBtn = document.getElementById('regenerate-btn');
    if (regenerateBtn) {
        regenerateBtn.addEventListener('click', function() {
            // 模拟重新生成
            showNotification('正在重新生成...', 'info');
            
            setTimeout(() => {
                // 更新质量分数
                generatedPhotos.forEach(photo => {
                    photo.qualityScore = Math.random().toFixed(2);
                });
                
                // 重新显示结果
                displayResults();
                showNotification('重新生成完成', 'success');
            }, 1500);
        });
    }
    
    // 显示结果
    function displayResults() {
        if (!resultsGrid) return;
        
        // 清空结果网格
        resultsGrid.innerHTML = '';
        selectedPhotos = [];
        
        // 添加结果项
        generatedPhotos.forEach((photo, index) => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            
            // 确定质量等级
            let qualityClass = 'medium';
            if (photo.qualityScore > 0.8) {
                qualityClass = 'high';
            } else if (photo.qualityScore < 0.5) {
                qualityClass = 'low';
            }
            
            resultItem.innerHTML = `
                <img src="${photo.url}" alt="生成的证件照">
                <input type="checkbox" class="result-checkbox" data-index="${index}">
                <div class="result-quality">
                    <span class="quality-score ${qualityClass}">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 1L10.5 6L16 6.75L12 10.5L13 16L8 13.5L3 16L4 10.5L0 6.75L5.5 6L8 1Z" fill="currentColor"/>
                        </svg>
                        ${photo.qualityScore}
                    </span>
                    <span>${getSpecName(photo.spec)}</span>
                </div>
            `;
            resultsGrid.appendChild(resultItem);
        });
        
        // 更新全选状态
        updateSelectAllState();
    }
    
    // 获取规格名称
    function getSpecName(spec) {
        const specMap = {
            '1inch': '1寸',
            '2inch': '2寸',
            'small2inch': '小2寸',
            'large1inch': '大1寸',
            'custom': '自定义'
        };
        return specMap[spec] || spec;
    }
}

/**
 * 初始化结果处理功能
 */
function initResultsHandling() {
    const resultsGrid = document.getElementById('results-grid');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const previewBtn = document.getElementById('preview-btn');
    const downloadBtn = document.getElementById('download-btn');
    const previewModal = document.getElementById('preview-modal');
    
    if (!resultsGrid || !selectAllCheckbox) return;
    
    // 选择/取消选择照片
    resultsGrid.addEventListener('change', function(e) {
        if (e.target.classList.contains('result-checkbox')) {
            const index = parseInt(e.target.dataset.index);
            
            if (e.target.checked) {
                selectedPhotos.push(index);
            } else {
                const selectedIndex = selectedPhotos.indexOf(index);
                if (selectedIndex !== -1) {
                    selectedPhotos.splice(selectedIndex, 1);
                }
            }
            
            // 更新按钮状态
            updateButtonsState();
            
            // 更新全选状态
            updateSelectAllState();
        }
    });
    
    // 全选/取消全选
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = resultsGrid.querySelectorAll('.result-checkbox');
            
            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = this.checked;
                
                if (this.checked) {
                    if (!selectedPhotos.includes(index)) {
                        selectedPhotos.push(index);
                    }
                } else {
                    selectedPhotos = [];
                }
            });
            
            // 更新按钮状态
            updateButtonsState();
        });
    }
    
    // 预览按钮
    if (previewBtn && previewModal) {
        previewBtn.addEventListener('click', function() {
            if (selectedPhotos.length !== 1) {
                showNotification('请选择一张照片进行预览', 'error');
                return;
            }
            
            const selectedPhoto = generatedPhotos[selectedPhotos[0]];
            
            // 更新预览模态框内容
            document.getElementById('preview-image').src = selectedPhoto.url;
            document.getElementById('preview-spec').textContent = getSpecName(selectedPhoto.spec);
            document.getElementById('preview-background').textContent = getBackgroundName(selectedPhoto.background);
            document.getElementById('preview-quality').textContent = selectedPhoto.qualityScore;
            
            // 显示预览模态框
            previewModal.style.display = 'flex';
        });
        
        // 预览模态框中的下载按钮
        const previewDownloadBtn = document.getElementById('preview-download-btn');
        if (previewDownloadBtn) {
            previewDownloadBtn.addEventListener('click', function() {
                if (selectedPhotos.length === 1) {
                    downloadPhoto(selectedPhotos[0]);
                }
            });
        }
    }
    
    // 下载按钮
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            if (selectedPhotos.length === 0) {
                showNotification('请选择要下载的照片', 'error');
                return;
            }
            
            if (selectedPhotos.length === 1) {
                // 下载单张照片
                downloadPhoto(selectedPhotos[0]);
            } else {
                // 下载多张照片（实际应该是ZIP打包）
                showNotification('正在准备下载多张照片...', 'info');
                
                // 模拟下载延迟
                setTimeout(() => {
                    selectedPhotos.forEach(index => {
                        downloadPhoto(index);
                    });
                    showNotification('下载完成', 'success');
                }, 1000);
            }
        });
    }
    
    // 更新按钮状态
    function updateButtonsState() {
        if (previewBtn) {
            previewBtn.disabled = selectedPhotos.length !== 1;
        }
        
        if (downloadBtn) {
            downloadBtn.disabled = selectedPhotos.length === 0;
        }
    }
    
    // 更新全选状态
    function updateSelectAllState() {
        if (!selectAllCheckbox || !resultsGrid) return;
        
        const checkboxes = resultsGrid.querySelectorAll('.result-checkbox');
        const checkedCount = resultsGrid.querySelectorAll('.result-checkbox:checked').length;
        
        selectAllCheckbox.checked = checkboxes.length > 0 && checkedCount === checkboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < checkboxes.length;
    }
    
    // 下载照片
    function downloadPhoto(index) {
        const photo = generatedPhotos[index];
        if (!photo) return;
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = photo.url;
        link.download = `证件照_${getSpecName(photo.spec)}_${getBackgroundName(photo.background)}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // 获取规格名称
    function getSpecName(spec) {
        const specMap = {
            '1inch': '1寸',
            '2inch': '2寸',
            'small2inch': '小2寸',
            'large1inch': '大1寸',
            'custom': '自定义'
        };
        return specMap[spec] || spec;
    }
    
    // 获取背景颜色名称
    function getBackgroundName(background) {
        const backgroundMap = {
            'blue': '蓝底',
            'red': '红底',
            'white': '白底'
        };
        return backgroundMap[background] || background;
    }
}

/**
 * 显示通知
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型 (success, error, info, warning)
 */
function showNotification(message, type = 'info') {
    // 检查是否已存在通知容器
    let notificationContainer = document.querySelector('.notification-container');
    
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
        </div>
        <span class="notification-close">&times;</span>
    `;
    
    // 添加到容器
    notificationContainer.appendChild(notification);
    
    // 添加关闭事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // 自动关闭
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 3000);
}

// 添加通知样式
(function addNotificationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        
        .notification {
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 300px;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            background-color: white;
            animation: slide-in 0.3s ease;
        }
        
        .notification.fade-out {
            animation: fade-out 0.3s ease forwards;
        }
        
        .notification.success {
            border-left: 4px solid #28a745;
        }
        
        .notification.error {
            border-left: 4px solid #dc3545;
        }
        
        .notification.info {
            border-left: 4px solid #17a2b8;
        }
        
        .notification.warning {
            border-left: 4px solid #ffc107;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-close {
            cursor: pointer;
            font-size: 18px;
            margin-left: 10px;
            color: #999;
        }
        
        @keyframes slide-in {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes fade-out {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
})();