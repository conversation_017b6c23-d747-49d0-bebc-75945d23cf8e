import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Card,
  Button,
  Typography,
  message,
  Space,
  Modal,
  Spin,
} from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import { getPhotoById, updatePhoto } from '../services/photoService';
import { setCurrentPhoto } from '../store/photoSlice';
import { addNotification } from '../store/uiSlice';
import { ROUTES } from '../utils/constants';
import PhotoEditor from '../components/common/PhotoEditor';
import LoadingSpinner from '../components/common/LoadingSpinner';

const { Title } = Typography;

const Edit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // 从Redux获取当前照片
  const currentPhoto = useSelector((state) => state.photos.currentPhoto);
  
  // 本地状态
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [exitModalVisible, setExitModalVisible] = useState(false);
  const [editorSettings, setEditorSettings] = useState({});
  
  // 加载照片数据
  useEffect(() => {
    const fetchPhotoData = async () => {
      try {
        setLoading(true);
        const photoData = await getPhotoById(id);
        dispatch(setCurrentPhoto(photoData));
        
        // 如果照片有保存的编辑设置，加载它们
        if (photoData.editorSettings) {
          setEditorSettings(photoData.editorSettings);
        }
      } catch (error) {
        console.error('获取照片失败:', error);
        message.error('获取照片失败，请重试');
        navigate(ROUTES.HOME);
      } finally {
        setLoading(false);
      }
    };
    
    // 如果Redux中没有当前照片或ID不匹配，则从API获取
    if (!currentPhoto || currentPhoto.id !== id) {
      fetchPhotoData();
    } else {
      // 如果照片有保存的编辑设置，加载它们
      if (currentPhoto.editorSettings) {
        setEditorSettings(currentPhoto.editorSettings);
      }
      setLoading(false);
    }
    
    // 添加页面离开确认
    const handleBeforeUnload = (e) => {
      if (unsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [id, dispatch, navigate, currentPhoto, unsavedChanges]);
  
  // 处理保存
  const handleSave = async (blob, settings) => {
    try {
      setSaving(true);
      
      // 创建FormData对象
      const formData = new FormData();
      formData.append('photo', blob, 'edited-photo.jpg');
      formData.append('editorSettings', JSON.stringify(settings));
      
      // 调用API保存编辑后的照片
      const updatedPhoto = await updatePhoto(id, formData);
      
      // 更新Redux状态
      dispatch(setCurrentPhoto(updatedPhoto));
      
      // 显示成功消息
      dispatch(addNotification({
        type: 'success',
        title: '保存成功',
        message: '照片已成功保存',
      }));
      
      // 重置未保存更改状态
      setUnsavedChanges(false);
      
      // 导航到结果页面
      navigate(`${ROUTES.RESULT}/${id}`);
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };
  
  // 处理设置变更
  const handleSettingsChange = (newSettings) => {
    setUnsavedChanges(true);
    setEditorSettings(newSettings);
  };
  
  // 处理返回
  const handleBack = () => {
    if (unsavedChanges) {
      setExitModalVisible(true);
    } else {
      navigate(`${ROUTES.RESULT}/${id}`);
    }
  };
  
  // 确认离开
  const confirmExit = () => {
    setExitModalVisible(false);
    navigate(`${ROUTES.RESULT}/${id}`);
  };
  
  // 如果正在加载，显示加载状态
  if (loading) {
    return <LoadingSpinner tip="正在加载照片..." />;
  }
  
  // 如果没有照片数据，显示错误信息
  if (!currentPhoto) {
    return (
      <div className="error-container">
        <Title level={3}>照片不存在或已被删除</Title>
        <Button type="primary" onClick={() => navigate(ROUTES.HOME)}>
          返回首页
        </Button>
      </div>
    );
  }
  
  return (
    <div className="edit-page">
      <div className="edit-header">
        <Button 
          icon={<ArrowLeftOutlined />} 
          onClick={handleBack}
          className="back-button"
        >
          返回
        </Button>
        <Title level={3}>编辑照片</Title>
      </div>
      
      <Card className="editor-card">
        <PhotoEditor
          imageUrl={currentPhoto.url}
          onSave={handleSave}
          initialSettings={editorSettings}
          loading={saving}
          onSettingsChange={handleSettingsChange}
        />
      </Card>
      
      {/* 未保存更改确认模态框 */}
      <Modal
        title="未保存的更改"
        visible={exitModalVisible}
        onCancel={() => setExitModalVisible(false)}
        onOk={confirmExit}
        okText="放弃更改"
        cancelText="继续编辑"
      >
        <p>您有未保存的更改，确定要离开吗？</p>
      </Modal>
    </div>
  );
};

export default Edit;