import React from 'react';
import { Link } from 'react-router-dom';
import { Layout, Typography, Space, Divider } from 'antd';
import {
  GithubOutlined,
  TwitterOutlined,
  InstagramOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import { ROUTES } from '../../utils/constants';

const { Footer: AntFooter } = Layout;
const { Text, Link: TextLink } = Typography;

/**
 * 样式化组件
 */
const StyledFooter = styled(AntFooter)`
  text-align: center;
  background: ${({ theme }) => theme.colorBgContainer};
  padding: 16px 50px;
  
  @media (max-width: 576px) {
    padding: 16px;
  }
`;

const FooterContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const FooterLinks = styled.div`
  margin-bottom: 12px;
  
  a {
    margin: 0 8px;
    color: ${({ theme }) => theme.colorTextSecondary};
    
    &:hover {
      color: ${({ theme }) => theme.colorPrimary};
    }
  }
  
  @media (max-width: 576px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    a {
      margin: 4px 0;
    }
  }
`;

const SocialLinks = styled.div`
  margin-bottom: 12px;
  
  .anticon {
    margin: 0 8px;
    font-size: 18px;
    color: ${({ theme }) => theme.colorTextSecondary};
    
    &:hover {
      color: ${({ theme }) => theme.colorPrimary};
    }
  }
`;

/**
 * 页脚组件
 * 
 * 显示在应用的底部，包含版权信息和其他链接
 * 
 * @returns {JSX.Element} 页脚组件
 */
const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <StyledFooter>
      <FooterContent>
        <FooterLinks>
          <Link to={ROUTES.PRIVACY}>隐私政策</Link>
          <Link to={ROUTES.TERMS}>服务条款</Link>
          <Link to={ROUTES.HELP}>帮助中心</Link>
          <TextLink href="mailto:<EMAIL>">联系我们</TextLink>
        </FooterLinks>
        
        <SocialLinks>
          <TextLink href="https://github.com/aiphotogenerator" target="_blank" rel="noopener noreferrer">
            <GithubOutlined />
          </TextLink>
          <TextLink href="https://twitter.com/aiphotogenerator" target="_blank" rel="noopener noreferrer">
            <TwitterOutlined />
          </TextLink>
          <TextLink href="https://instagram.com/aiphotogenerator" target="_blank" rel="noopener noreferrer">
            <InstagramOutlined />
          </TextLink>
        </SocialLinks>
        
        <Text type="secondary">
          © {currentYear} AI照片生成器 版权所有
        </Text>
      </FooterContent>
    </StyledFooter>
  );
};

export default Footer;