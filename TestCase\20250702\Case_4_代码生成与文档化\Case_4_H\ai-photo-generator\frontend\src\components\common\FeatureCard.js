import React from 'react';
import PropTypes from 'prop-types';
import { Card, Typography, Button } from 'antd';
import { Link } from 'react-router-dom';

const { Title, Paragraph } = Typography;

/**
 * 功能卡片组件
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.title - 卡片标题
 * @param {string} props.description - 卡片描述
 * @param {string} props.icon - 卡片图标
 * @param {string} props.image - 卡片图片
 * @param {string} props.linkText - 链接文本
 * @param {string} props.linkTo - 链接地址
 * @param {string} props.buttonText - 按钮文本
 * @param {string} props.buttonType - 按钮类型
 * @param {Function} props.onClick - 点击回调
 * @returns {React.ReactNode} 渲染的组件
 */
const FeatureCard = ({ 
  title, 
  description, 
  icon, 
  image, 
  linkText, 
  linkTo, 
  buttonText, 
  buttonType = 'default',
  onClick 
}) => {
  return (
    <Card 
      className="feature-card" 
      bordered={false}
      hoverable
    >
      <div className="feature-card-content">
        {icon && (
          <div className="feature-icon">
            {icon}
          </div>
        )}
        
        {image && (
          <div className="feature-image">
            <img src={image} alt={title} />
          </div>
        )}
        
        <div className="feature-text">
          <Title level={4}>{title}</Title>
          <Paragraph>{description}</Paragraph>
        </div>
        
        <div className="feature-action">
          {linkText && linkTo && (
            <Link to={linkTo} className="feature-link">
              {linkText}
            </Link>
          )}
          
          {buttonText && (
            <Button 
              type={buttonType} 
              onClick={onClick}
              className={buttonType === 'primary' ? 'btn-primary' : ''}
            >
              {buttonText}
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};

FeatureCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  icon: PropTypes.node,
  image: PropTypes.string,
  linkText: PropTypes.string,
  linkTo: PropTypes.string,
  buttonText: PropTypes.string,
  buttonType: PropTypes.string,
  onClick: PropTypes.func,
};

export default FeatureCard;