import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    
    // 如果有token，添加到请求头
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      // 清除token
      localStorage.removeItem('token');
      
      // 重定向到登录页
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (userData) => {
    return api.post('/auth/register', userData);
  },
  
  // 用户登录
  login: (credentials) => {
    return api.post('/auth/login', credentials);
  }
};

// 用户相关API
export const userAPI = {
  // 获取用户个人资料
  getProfile: () => {
    return api.get('/user/profile');
  },
  
  // 更新用户个人资料
  updateProfile: (profileData) => {
    return api.put('/user/profile', profileData);
  },
  
  // 修改密码
  changePassword: (passwordData) => {
    return api.put('/user/password', passwordData);
  },
  
  // 获取用户历史记录
  getHistory: () => {
    return api.get('/user/history');
  },
  
  // 购买积分
  purchaseCredits: (amount) => {
    return api.post('/user/credits/purchase', { amount });
  }
};

// 上传相关API
export const uploadAPI = {
  // 上传单张照片
  uploadPhoto: (file, onUploadProgress) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    });
  },
  
  // 批量上传照片
  uploadPhotos: (files, onUploadProgress) => {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    
    return api.post('/upload/batch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    });
  }
};

// 照片处理相关API
export const photoAPI = {
  // 生成证件照
  generatePhotos: (data) => {
    return api.post('/photo/generate', data);
  },
  
  // 获取任务状态
  getTaskStatus: (taskId) => {
    return api.get(`/photo/task/${taskId}`);
  },
  
  // 下载照片
  downloadPhotos: (photoIds) => {
    return api.post('/photo/download', { photo_ids: photoIds });
  },
  
  // 下载单张照片
  downloadPhoto: (photoId) => {
    return api.get(`/photo/download/${photoId}`);
  }
};

export default api;