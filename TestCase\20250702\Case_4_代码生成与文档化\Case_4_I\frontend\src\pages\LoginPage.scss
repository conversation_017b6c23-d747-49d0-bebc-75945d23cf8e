.login-page {
  padding: 40px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px - 70px); // 减去header和footer的高度
  display: flex;
  align-items: center;
  
  .login-card {
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .login-header {
    text-align: center;
    margin-bottom: 24px;
    
    h2 {
      margin-bottom: 8px;
    }
  }
  
  .login-alert {
    margin-bottom: 24px;
  }
  
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .forgot-password {
      float: right;
    }
  }
  
  .login-button {
    height: 40px;
  }
  
  .register-link {
    text-align: center;
    margin-top: 16px;
  }
}

// 响应式样式
@media (max-width: 768px) {
  .login-page {
    padding: 20px 16px;
    
    .login-card {
      width: 100%;
    }
  }
}