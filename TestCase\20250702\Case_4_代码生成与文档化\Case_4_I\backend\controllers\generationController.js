const fs = require('fs');
const path = require('path');
const PhotoTask = require('../models/PhotoTask');

// @desc    获取任务状态
// @route   GET /api/generation/status/:taskId
// @access  Private
exports.getTaskStatus = async (req, res) => {
  try {
    const task = await PhotoTask.findById(req.params.taskId);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    // 检查是否是任务所有者
    if (task.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权访问此任务'
      });
    }
    
    res.status(200).json({
      success: true,
      task: task.getStatus()
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    获取生成的照片列表
// @route   GET /api/generation/photos/:taskId
// @access  Private
exports.getGeneratedPhotos = async (req, res) => {
  try {
    const task = await PhotoTask.findById(req.params.taskId);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    // 检查是否是任务所有者
    if (task.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权访问此任务'
      });
    }
    
    // 检查任务是否完成
    if (task.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: `任务尚未完成，当前状态: ${task.status}`,
        status: task.status
      });
    }
    
    res.status(200).json({
      success: true,
      photos: task.getGeneratedPhotos(),
      specification: task.specification
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    下载单张生成的照片
// @route   GET /api/generation/download/:taskId/:photoId
// @access  Private
exports.downloadGeneratedPhoto = async (req, res) => {
  try {
    const task = await PhotoTask.findById(req.params.taskId);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    // 检查是否是任务所有者
    if (task.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权访问此任务'
      });
    }
    
    // 查找指定的照片
    const photo = task.generatedPhotos.id(req.params.photoId);
    
    if (!photo) {
      return res.status(404).json({
        success: false,
        message: '照片不存在'
      });
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(photo.path)) {
      return res.status(404).json({
        success: false,
        message: '照片文件不存在'
      });
    }
    
    // 设置下载文件名
    const downloadFilename = `${task.specification}_${path.basename(photo.filename)}`;
    
    // 发送文件
    res.download(photo.path, downloadFilename);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    批量下载生成的照片（ZIP）
// @route   GET /api/generation/download-batch/:taskId
// @access  Private
exports.downloadBatchPhotos = async (req, res) => {
  try {
    const task = await PhotoTask.findById(req.params.taskId);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    // 检查是否是任务所有者
    if (task.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权访问此任务'
      });
    }
    
    // 检查任务是否完成
    if (task.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: `任务尚未完成，当前状态: ${task.status}`
      });
    }
    
    // 检查是否有生成的照片
    if (task.generatedPhotos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '没有可下载的照片'
      });
    }
    
    // 在实际项目中，这里应该创建一个ZIP文件包含所有照片
    // 这里简化处理，只返回第一张照片
    const photo = task.generatedPhotos[0];
    
    // 检查文件是否存在
    if (!fs.existsSync(photo.path)) {
      return res.status(404).json({
        success: false,
        message: '照片文件不存在'
      });
    }
    
    // 设置下载文件名
    const downloadFilename = `${task.specification}_batch.jpg`;
    
    // 发送文件
    res.download(photo.path, downloadFilename);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    重新生成照片
// @route   POST /api/generation/regenerate/:taskId
// @access  Private
exports.regeneratePhoto = async (req, res) => {
  try {
    const task = await PhotoTask.findById(req.params.taskId);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    // 检查是否是任务所有者
    if (task.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '无权访问此任务'
      });
    }
    
    // 检查用户积分是否足够
    const requiredCredits = task.originalPhotos.length;
    if (req.user.remainingCredits < requiredCredits) {
      return res.status(402).json({
        success: false,
        message: '积分不足，请充值'
      });
    }
    
    // 清除之前生成的照片
    task.generatedPhotos = [];
    task.status = 'pending';
    await task.save();
    
    // 扣除用户积分
    await req.user.decreaseCredits(requiredCredits);
    
    res.status(200).json({
      success: true,
      message: '开始重新生成照片',
      taskId: task._id
    });
    
    // 异步处理照片（实际项目中应该调用AI服务或队列）
    processPhotoTask(task._id);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 模拟处理照片任务（实际项目中应该调用AI服务）
async function processPhotoTask(taskId) {
  try {
    // 获取任务
    const task = await PhotoTask.findById(taskId);
    if (!task) {
      console.error(`任务不存在: ${taskId}`);
      return;
    }

    // 更新状态为处理中
    await task.updateStatus('processing');

    // 模拟处理时间（实际项目中应该调用AI服务）
    const processingTime = Math.floor(Math.random() * 3000) + 2000; // 2-5秒
    
    setTimeout(async () => {
      try {
        // 为每张原始照片生成一张证件照
        for (let i = 0; i < task.originalPhotos.length; i++) {
          const originalPhoto = task.originalPhotos[i];
          
          // 模拟生成证件照（实际项目中应该调用AI服务）
          const generatedFilename = `generated_${Date.now()}_${i}.jpg`;
          const generatedPath = path.join(
            path.dirname(originalPhoto.path),
            generatedFilename
          );
          
          // 这里只是复制原图，实际项目中应该调用AI服务生成证件照
          fs.copyFileSync(originalPhoto.path, generatedPath);
          
          // 添加生成的照片到任务中
          await task.addGeneratedPhoto({
            path: generatedPath,
            filename: generatedFilename,
            originalPhotoIndex: i,
            qualityScore: Math.floor(Math.random() * 30) + 70 // 70-100的随机分数
          });
        }
        
        // 更新任务状态为完成
        await task.updateStatus('completed');
        
        console.log(`任务完成: ${taskId}`);
      } catch (error) {
        console.error(`处理任务失败: ${taskId}`, error);
        await task.updateStatus('failed', error.message);
      }
    }, processingTime);
    
  } catch (error) {
    console.error(`处理任务失败: ${taskId}`, error);
    const task = await PhotoTask.findById(taskId);
    if (task) {
      await task.updateStatus('failed', error.message);
    }
  }
}