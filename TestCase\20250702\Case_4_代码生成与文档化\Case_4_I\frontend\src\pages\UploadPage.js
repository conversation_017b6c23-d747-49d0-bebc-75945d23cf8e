import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Typography, Alert, Steps } from 'antd';
import { UploadOutlined, RobotOutlined, PictureOutlined } from '@ant-design/icons';
import PhotoUploader from '../components/PhotoUploader';
import { generateIdPhoto } from '../services/generationSlice';
import './UploadPage.scss';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const UploadPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { currentUpload, error: uploadError } = useSelector((state) => state.upload);
  const { loading: generationLoading, error: generationError } = useSelector((state) => state.generation);
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedSpec, setSelectedSpec] = useState('');
  
  // 处理上传成功
  const handleUploadSuccess = (uploadId, spec) => {
    setSelectedSpec(spec);
    setCurrentStep(1);
    startGeneration(uploadId, spec);
  };
  
  // 开始生成证件照
  const startGeneration = async (uploadId, spec) => {
    try {
      // 默认背景色为蓝色
      const background = 'blue';
      // 默认生成5张供选择
      const count = 5;
      
      const result = await dispatch(generateIdPhoto({
        uploadId,
        spec,
        background,
        count,
        modelConfig: {
          model_name: 'default_model',
          parameters: {
            quality: 'high',
            style: 'formal'
          }
        }
      })).unwrap();
      
      // 生成成功后跳转到结果页面
      if (result.data.task_id) {
        setCurrentStep(2);
        navigate(`/result/${result.data.task_id}`);
      }
    } catch (error) {
      console.error('生成证件照失败:', error);
    }
  };
  
  // 步骤配置
  const steps = [
    {
      title: '上传照片',
      icon: <UploadOutlined />
    },
    {
      title: 'AI处理中',
      icon: <RobotOutlined />
    },
    {
      title: '查看结果',
      icon: <PictureOutlined />
    }
  ];
  
  return (
    <div className="upload-page">
      <div className="container">
        <Title level={2} className="page-title text-center">上传照片生成证件照</Title>
        
        <div className="steps-container">
          <Steps current={currentStep}>
            {steps.map((step, index) => (
              <Step key={index} title={step.title} icon={step.icon} />
            ))}
          </Steps>
        </div>
        
        {(uploadError || generationError) && (
          <Alert 
            message="错误提示" 
            description={uploadError || generationError} 
            type="error" 
            showIcon 
            className="error-alert"
          />
        )}
        
        <div className="upload-container">
          {currentStep === 0 && (
            <>
              <Paragraph className="upload-description text-center">
                请上传您的照片，我们将使用AI技术自动生成符合标准的证件照。
                支持JPG、PNG、JPEG格式，单张照片大小不超过10MB。
              </Paragraph>
              
              <PhotoUploader onUploadSuccess={handleUploadSuccess} />
            </>
          )}
          
          {currentStep === 1 && (
            <div className="processing-container text-center">
              <RobotOutlined className="processing-icon" />
              <Title level={3}>AI正在处理您的照片</Title>
              <Paragraph>
                我们正在使用先进的AI技术处理您的照片，生成符合{selectedSpec}规格的证件照。
                请稍候，这通常需要30-60秒的时间。
              </Paragraph>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadPage;