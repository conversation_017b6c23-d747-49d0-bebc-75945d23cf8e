import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { Form, Input, Button, Card, Alert, Typography, Result } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import axios from 'axios';
import { API_ENDPOINTS, ROUTES } from '../../utils/constants';
import Logo from '../../components/common/Logo';

const { Title, Text, Paragraph } = Typography;

/**
 * 重置密码页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const ResetPasswordPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { token } = useParams();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);
  const [validating, setValidating] = useState(true);
  
  // 验证重置令牌
  useEffect(() => {
    const validateToken = async () => {
      try {
        await axios.get(`${API_ENDPOINTS.RESET_PASSWORD}/validate/${token}`);
        setTokenValid(true);
      } catch (err) {
        setTokenValid(false);
        setError('密码重置链接无效或已过期，请重新申请');
      } finally {
        setValidating(false);
      }
    };
    
    if (token) {
      validateToken();
    } else {
      setTokenValid(false);
      setValidating(false);
      setError('缺少重置令牌，请重新申请');
    }
  }, [token]);
  
  /**
   * 处理表单提交
   * 
   * @param {Object} values - 表单值
   */
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);
    
    try {
      await axios.post(`${API_ENDPOINTS.RESET_PASSWORD}/${token}`, {
        password: values.password,
      });
      setSuccess(true);
      
      // 3秒后重定向到登录页
      setTimeout(() => {
        navigate(ROUTES.LOGIN);
      }, 3000);
    } catch (err) {
      setError(
        err.response?.data?.message || 
        '重置密码失败，请稍后再试'
      );
    } finally {
      setLoading(false);
    }
  };
  
  // 如果正在验证令牌，显示加载状态
  if (validating) {
    return (
      <div className="reset-password-page" style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
        <div className="reset-password-logo" style={{ textAlign: 'center', marginBottom: 24 }}>
          <Logo size="large" />
          <Title level={2} style={{ marginTop: 16 }}>验证中</Title>
          <Text type="secondary">正在验证您的重置链接，请稍候...</Text>
        </div>
      </div>
    );
  }
  
  // 如果重置成功，显示成功页面
  if (success) {
    return (
      <div className="reset-password-page" style={{ maxWidth: 500, margin: '0 auto', padding: '40px 20px' }}>
        <Result
          status="success"
          title="密码重置成功"
          subTitle="您的密码已成功重置，即将跳转到登录页面..."
          extra={[
            <Button type="primary" key="login">
              <Link to="/login">立即登录</Link>
            </Button>
          ]}
        />
      </div>
    );
  }
  
  // 如果令牌无效，显示错误页面
  if (!tokenValid) {
    return (
      <div className="reset-password-page" style={{ maxWidth: 500, margin: '0 auto', padding: '40px 20px' }}>
        <Result
          status="error"
          title="链接无效"
          subTitle={error || '密码重置链接无效或已过期'}
          extra={[
            <Button type="primary" key="forgot">
              <Link to="/forgot-password">重新申请</Link>
            </Button>,
            <Button key="login">
              <Link to="/login">返回登录</Link>
            </Button>
          ]}
        />
      </div>
    );
  }
  
  return (
    <div className="reset-password-page" style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
      <div className="reset-password-logo" style={{ textAlign: 'center', marginBottom: 24 }}>
        <Logo size="large" />
        <Title level={2} style={{ marginTop: 16 }}>重置密码</Title>
        <Text type="secondary">请设置您的新密码</Text>
      </div>
      
      {error && (
        <Alert
          message="操作失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false} style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)' }}>
        <Form
          form={form}
          name="resetPassword"
          onFinish={handleSubmit}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码长度至少为8个字符' },
            ]}
            hasFeedback
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="新密码"
              autoComplete="new-password"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            hasFeedback
            rules={[
              { required: true, message: '请确认您的新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="确认新密码"
              autoComplete="new-password"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="btn-primary"
            >
              重置密码
            </Button>
          </Form.Item>
          
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Link to="/login">返回登录</Link>
          </div>
        </Form>
      </Card>
      
      <Paragraph type="secondary" style={{ textAlign: 'center', marginTop: 24 }}>
        如果您还没有账号，请 <Link to="/register">立即注册</Link>。
      </Paragraph>
    </div>
  );
};

export default ResetPasswordPage;