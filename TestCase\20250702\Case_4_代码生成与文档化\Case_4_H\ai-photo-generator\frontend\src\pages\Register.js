import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Divider, message, Checkbox } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login } from '../store/authSlice';
import { registerUser } from '../services/authService';
import { ROUTES } from '../utils/constants';

const { Title, Paragraph, Text } = Typography;

const Register = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const [form] = Form.useForm();

  const onFinish = async (values) => {
    try {
      setLoading(true);
      
      // 调用注册API
      const response = await registerUser(
        values.username,
        values.email,
        values.password
      );
      
      // 注册成功后自动登录
      dispatch(login({
        user: response.user,
        token: response.token,
      }));
      
      message.success('注册成功！');
      navigate(ROUTES.HOME);
    } catch (error) {
      console.error('注册失败:', error);
      message.error(error.message || '注册失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialRegister = (provider) => {
    // 实际项目中，这里会实现社交媒体注册逻辑
    message.info(`${provider}注册功能正在开发中...`);
  };

  return (
    <div className="register-page">
      <Card className="register-card">
        <div className="register-header">
          <Title level={2}>创建账号</Title>
          <Paragraph>注册一个新账号以使用所有功能</Paragraph>
        </div>
        
        <Form
          form={form}
          name="register"
          onFinish={onFinish}
          size="large"
          layout="vertical"
          scrollToFirstError
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少需要3个字符' },
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="用户名" 
            />
          </Form.Item>
          
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input 
              prefix={<MailOutlined />} 
              placeholder="邮箱" 
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少需要6个字符' },
            ]}
            hasFeedback
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
            />
          </Form.Item>
          
          <Form.Item
            name="confirm"
            dependencies={['password']}
            hasFeedback
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="确认密码"
            />
          </Form.Item>
          
          <Form.Item
            name="agreement"
            valuePropName="checked"
            rules={[
              {
                validator: (_, value) =>
                  value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意用户协议和隐私政策')),
              },
            ]}
          >
            <Checkbox>
              我已阅读并同意 <Link to={ROUTES.TERMS}>用户协议</Link> 和 <Link to={ROUTES.PRIVACY}>隐私政策</Link>
            </Checkbox>
          </Form.Item>
          
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              className="register-form-button"
              loading={loading}
              block
            >
              注册
            </Button>
          </Form.Item>
          
          <div className="register-form-login">
            <Text>已有账号? </Text>
            <Link to={ROUTES.LOGIN}>立即登录</Link>
          </div>
        </Form>
        
        <Divider plain>或使用以下方式注册</Divider>
        
        <div className="social-register">
          <Button 
            icon={<GoogleOutlined />} 
            onClick={() => handleSocialRegister('Google')}
            className="google-register-btn"
          >
            Google注册
          </Button>
          <Button 
            icon={<FacebookOutlined />} 
            onClick={() => handleSocialRegister('Facebook')}
            className="facebook-register-btn"
          >
            Facebook注册
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default Register;