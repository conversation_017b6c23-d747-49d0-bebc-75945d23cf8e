.photo-uploader {
  margin-bottom: 24px;
  
  .upload-card {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .ant-upload-drag {
      border: 2px dashed #d9d9d9;
      border-radius: 4px;
      padding: 24px;
      transition: border-color 0.3s;
      
      &:hover {
        border-color: #1890ff;
      }
    }
    
    .ant-upload-text {
      font-size: 16px;
      margin: 8px 0;
    }
    
    .ant-upload-hint {
      color: rgba(0, 0, 0, 0.45);
    }
  }
  
  .upload-progress {
    margin-top: 16px;
  }
  
  .upload-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    
    .spec-selector {
      display: flex;
      align-items: center;
      
      span {
        margin-right: 8px;
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .photo-uploader {
    .upload-actions {
      flex-direction: column;
      align-items: flex-start;
      
      .spec-selector {
        margin-bottom: 16px;
        width: 100%;
        
        .ant-select {
          width: 100% !important;
        }
      }
      
      button {
        width: 100%;
      }
    }
  }
}