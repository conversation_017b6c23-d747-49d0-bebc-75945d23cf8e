import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Upload, 
  Button, 
  message, 
  Card, 
  Radio, 
  Row, 
  Col, 
  Progress,
  Space 
} from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { UploadFile } from 'antd/es/upload/interface';
import { uploadAPI, photoAPI } from '../api';
import { UploadActionTypes } from '../store/reducers/uploadReducer';
import { PhotoActionTypes } from '../store/reducers/photoReducer';
import './UploadPage.scss';

const { Title, Paragraph } = Typography;
const { Dragger } = Upload;

// 规格选项
const specOptions = [
  { label: '1寸', value: '1inch' },
  { label: '2寸', value: '2inch' },
  { label: '小2寸', value: 'small2inch' },
  { label: '大1寸', value: 'large1inch' }
];

// 背景色选项
const backgroundOptions = [
  { label: '蓝色背景', value: 'blue' },
  { label: '红色背景', value: 'red' },
  { label: '白色背景', value: 'white' }
];

const UploadPage: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // 本地状态
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedSpec, setSelectedSpec] = useState('1inch');
  const [selectedBackground, setSelectedBackground] = useState('blue');
  
  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: false,
    fileList,
    beforeUpload: (file: File) => {
      // 检查文件类型
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传JPG/PNG格式的图片！');
        return Upload.LIST_IGNORE;
      }
      
      // 检查文件大小
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('图片必须小于10MB！');
        return Upload.LIST_IGNORE;
      }
      
      setFileList([file as UploadFile]);
      return false; // 阻止自动上传
    },
    onRemove: () => {
      setFileList([]);
    }
  };
  
  // 处理上传
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择一张照片！');
      return;
    }
    
    setUploading(true);
    
    try {
      // 上传照片
      dispatch({ type: UploadActionTypes.UPLOAD_START, payload: fileList[0] });
      
      const uploadResponse = await uploadAPI.uploadPhoto(fileList[0] as unknown as File, (progress) => {
        setUploadProgress(progress);
        dispatch({ type: UploadActionTypes.UPLOAD_PROGRESS, payload: progress });
      });
      
      const { upload_id } = uploadResponse.data.data;
      
      dispatch({
        type: UploadActionTypes.UPLOAD_SUCCESS,
        payload: { uploadId: upload_id }
      });
      
      // 生成证件照
      const generateResponse = await photoAPI.generatePhotos(upload_id, selectedSpec, selectedBackground);
      const { task_id } = generateResponse.data.data;
      
      dispatch({ type: PhotoActionTypes.GENERATE_START, payload: task_id });
      
      // 跳转到结果页面
      navigate(`/results/${task_id}`);
      
    } catch (error) {
      console.error('Upload error:', error);
      message.error('上传失败，请重试！');
      
      dispatch({
        type: UploadActionTypes.UPLOAD_FAILURE,
        payload: '上传失败，请重试！'
      });
      
      setUploading(false);
    }
  };
  
  return (
    <div className="upload-page">
      <Title level={2} className="page-title">上传照片</Title>
      <Paragraph className="page-description">
        上传一张您的照片，我们将为您生成专业证件照。
      </Paragraph>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} md={14}>
          <Card title="上传照片" className="upload-card">
            <Dragger {...uploadProps} className="upload-dragger">
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽照片到此区域上传</p>
              <p className="ant-upload-hint">
                支持JPG、PNG格式，文件大小不超过10MB
              </p>
            </Dragger>
            
            {uploading && (
              <div className="upload-progress">
                <Progress percent={uploadProgress} status="active" />
              </div>
            )}
          </Card>
        </Col>
        
        <Col xs={24} md={10}>
          <Card title="选择规格和背景" className="options-card">
            <div className="option-section">
              <Title level={5}>证件照规格</Title>
              <Radio.Group
                options={specOptions}
                onChange={(e) => setSelectedSpec(e.target.value)}
                value={selectedSpec}
                optionType="button"
                buttonStyle="solid"
              />
            </div>
            
            <div className="option-section">
              <Title level={5}>背景颜色</Title>
              <Radio.Group
                options={backgroundOptions}
                onChange={(e) => setSelectedBackground(e.target.value)}
                value={selectedBackground}
                optionType="button"
                buttonStyle="solid"
              />
            </div>
            
            <div className="action-section">
              <Space>
                <Button
                  type="primary"
                  onClick={handleUpload}
                  loading={uploading}
                  disabled={fileList.length === 0}
                  size="large"
                  block
                >
                  {uploading ? '处理中...' : '开始生成'}
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default UploadPage; 