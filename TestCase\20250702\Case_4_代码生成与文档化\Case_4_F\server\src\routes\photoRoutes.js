const express = require('express');
const router = express.Router();
const { 
  generatePhotos, 
  checkTaskStatus, 
  downloadGeneratedPhotos 
} = require('../controllers/photoController');
const { optionalAuth } = require('../middleware/authMiddleware');

// 生成证件照路由（可选身份验证）
router.post('/', optionalAuth, generatePhotos);

// 查询任务状态
router.get('/:taskId', checkTaskStatus);

// 下载照片
router.post('/download', downloadGeneratedPhotos);

module.exports = router; 