const mongoose = require('mongoose');

const PhotoTaskSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    originalPhotos: [{
      path: {
        type: String,
        required: true
      },
      filename: {
        type: String,
        required: true
      }
    }],
    generatedPhotos: [{
      path: {
        type: String
      },
      filename: {
        type: String
      },
      originalPhotoIndex: {
        type: Number
      },
      qualityScore: {
        type: Number,
        min: 0,
        max: 100
      }
    }],
    specification: {
      type: String,
      required: true,
      enum: ['one_inch', 'two_inch', 'small_two_inch', 'passport', 'visa']
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    errorMessage: {
      type: String
    },
    processingTime: {
      type: Number
    },
    creditsUsed: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// 获取任务状态
PhotoTaskSchema.methods.getStatus = function() {
  return {
    id: this._id,
    status: this.status,
    originalCount: this.originalPhotos.length,
    generatedCount: this.generatedPhotos.length,
    specification: this.specification,
    createdAt: this.createdAt,
    errorMessage: this.errorMessage
  };
};

// 获取生成的照片列表
PhotoTaskSchema.methods.getGeneratedPhotos = function() {
  return this.generatedPhotos.map(photo => ({
    id: photo._id,
    path: photo.path,
    filename: photo.filename,
    originalPhotoIndex: photo.originalPhotoIndex,
    qualityScore: photo.qualityScore
  }));
};

// 更新任务状态
PhotoTaskSchema.methods.updateStatus = async function(status, errorMessage = null) {
  this.status = status;
  if (errorMessage) {
    this.errorMessage = errorMessage;
  }
  
  if (status === 'completed') {
    this.processingTime = (new Date() - this.createdAt) / 1000; // 处理时间（秒）
  }
  
  await this.save();
  return this;
};

// 添加生成的照片
PhotoTaskSchema.methods.addGeneratedPhoto = async function(photoData) {
  this.generatedPhotos.push(photoData);
  
  if (this.generatedPhotos.length === this.originalPhotos.length) {
    this.status = 'completed';
    this.processingTime = (new Date() - this.createdAt) / 1000; // 处理时间（秒）
  }
  
  await this.save();
  return this;
};

module.exports = mongoose.model('PhotoTask', PhotoTaskSchema);