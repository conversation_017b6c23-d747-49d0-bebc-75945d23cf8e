const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const upload = multer({ dest: 'uploads/' });

app.use(express.static('public'));

app.post('/api/generate', upload.single('photo'), (req, res) => {
    // 模拟AI处理
    const resultUrl = `data:image/jpeg;base64,${fs.readFileSync(req.file.path, { encoding: 'base64' })}`;
    res.json({ url: resultUrl });
});

const PORT = 3000;
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});