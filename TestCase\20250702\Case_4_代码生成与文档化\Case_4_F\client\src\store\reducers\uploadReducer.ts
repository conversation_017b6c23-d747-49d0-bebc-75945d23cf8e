// 上传状态类型
export interface UploadState {
  uploadId: string | null;
  uploadedFile: File | null;
  uploadProgress: number;
  isUploading: boolean;
  error: string | null;
  spec: string;
  background: string;
}

// 初始状态
const initialState: UploadState = {
  uploadId: null,
  uploadedFile: null,
  uploadProgress: 0,
  isUploading: false,
  error: null,
  spec: '1inch', // 默认1寸
  background: 'blue' // 默认蓝色背景
};

// Action 类型
export enum UploadActionTypes {
  UPLOAD_START = 'UPLOAD_START',
  UPLOAD_PROGRESS = 'UPLOAD_PROGRESS',
  UPLOAD_SUCCESS = 'UPLOAD_SUCCESS',
  UPLOAD_FAILURE = 'UPLOAD_FAILURE',
  SET_SPEC = 'SET_SPEC',
  SET_BACKGROUND = 'SET_BACKGROUND',
  RESET_UPLOAD = 'RESET_UPLOAD'
}

interface UploadStartAction {
  type: UploadActionTypes.UPLOAD_START;
  payload: File;
}

interface UploadProgressAction {
  type: UploadActionTypes.UPLOAD_PROGRESS;
  payload: number;
}

interface UploadSuccessAction {
  type: UploadActionTypes.UPLOAD_SUCCESS;
  payload: {
    uploadId: string;
  };
}

interface UploadFailureAction {
  type: UploadActionTypes.UPLOAD_FAILURE;
  payload: string;
}

interface SetSpecAction {
  type: UploadActionTypes.SET_SPEC;
  payload: string;
}

interface SetBackgroundAction {
  type: UploadActionTypes.SET_BACKGROUND;
  payload: string;
}

interface ResetUploadAction {
  type: UploadActionTypes.RESET_UPLOAD;
}

export type UploadAction =
  | UploadStartAction
  | UploadProgressAction
  | UploadSuccessAction
  | UploadFailureAction
  | SetSpecAction
  | SetBackgroundAction
  | ResetUploadAction;

// Reducer
const uploadReducer = (state = initialState, action: UploadAction): UploadState => {
  switch (action.type) {
    case UploadActionTypes.UPLOAD_START:
      return {
        ...state,
        uploadedFile: action.payload,
        isUploading: true,
        error: null,
        uploadProgress: 0
      };
    
    case UploadActionTypes.UPLOAD_PROGRESS:
      return {
        ...state,
        uploadProgress: action.payload
      };
    
    case UploadActionTypes.UPLOAD_SUCCESS:
      return {
        ...state,
        uploadId: action.payload.uploadId,
        isUploading: false,
        uploadProgress: 100
      };
    
    case UploadActionTypes.UPLOAD_FAILURE:
      return {
        ...state,
        isUploading: false,
        error: action.payload
      };
    
    case UploadActionTypes.SET_SPEC:
      return {
        ...state,
        spec: action.payload
      };
    
    case UploadActionTypes.SET_BACKGROUND:
      return {
        ...state,
        background: action.payload
      };
    
    case UploadActionTypes.RESET_UPLOAD:
      return initialState;
    
    default:
      return state;
  }
};

export default uploadReducer; 