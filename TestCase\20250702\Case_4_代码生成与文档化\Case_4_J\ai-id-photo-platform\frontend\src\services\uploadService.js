import { uploadApi } from './api'

class UploadService {
  /**
   * 单文件上传
   * @param {File} file 文件对象
   * @param {Object} options 上传选项
   * @param {Function} onProgress 进度回调
   * @returns {Promise} 上传响应
   */
  async uploadSingle(file, options = {}, onProgress = null) {
    const formData = new FormData()
    formData.append('file', file)
    
    if (options.spec) {
      formData.append('spec', options.spec)
    }

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    // 添加进度监听
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }

    return await uploadApi.post('/upload/single', formData, config)
  }

  /**
   * 多文件上传
   * @param {FileList|Array} files 文件列表
   * @param {Object} options 上传选项
   * @param {Function} onProgress 进度回调
   * @returns {Promise} 上传响应
   */
  async uploadMultiple(files, options = {}, onProgress = null) {
    const formData = new FormData()
    
    // 添加文件到FormData
    Array.from(files).forEach((file) => {
      formData.append('files', file)
    })
    
    if (options.spec) {
      formData.append('spec', options.spec)
    }

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }

    // 添加进度监听
    if (onProgress) {
      config.onUploadProgress = (progressEvent) => {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        )
        onProgress(percentCompleted)
      }
    }

    return await uploadApi.post('/upload/multiple', formData, config)
  }

  /**
   * 获取上传信息
   * @param {string} uploadId 上传ID
   * @returns {Promise} 上传信息响应
   */
  async getUploadInfo(uploadId) {
    return await uploadApi.get(`/upload/${uploadId}`)
  }

  /**
   * 删除上传记录
   * @param {string} uploadId 上传ID
   * @returns {Promise} 删除响应
   */
  async deleteUpload(uploadId) {
    return await uploadApi.delete(`/upload/${uploadId}`)
  }

  /**
   * 验证文件
   * @param {File} file 文件对象
   * @returns {Object} 验证结果
   */
  validateFile(file) {
    const errors = []
    
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      errors.push('文件类型不支持，仅支持 JPG、JPEG、PNG 格式')
    }
    
    // 检查文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      errors.push('文件大小超过限制，最大支持 10MB')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证多个文件
   * @param {FileList|Array} files 文件列表
   * @returns {Object} 验证结果
   */
  validateFiles(files) {
    const errors = []
    const fileArray = Array.from(files)
    
    // 检查文件数量
    if (fileArray.length > 5) {
      errors.push('文件数量超过限制，最多支持 5 张照片')
    }
    
    // 验证每个文件
    fileArray.forEach((file, index) => {
      const fileValidation = this.validateFile(file)
      if (!fileValidation.isValid) {
        errors.push(`文件 ${index + 1}: ${fileValidation.errors.join(', ')}`)
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取文件预览URL
   * @param {File} file 文件对象
   * @returns {string} 预览URL
   */
  getFilePreviewUrl(file) {
    return URL.createObjectURL(file)
  }

  /**
   * 释放文件预览URL
   * @param {string} url 预览URL
   */
  revokeFilePreviewUrl(url) {
    URL.revokeObjectURL(url)
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

export default new UploadService()
