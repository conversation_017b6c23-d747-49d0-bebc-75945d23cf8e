.home-page {
  .hero-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%);
    color: white;
    margin-bottom: 40px;
    
    .hero-content {
      text-align: center;
      
      h1 {
        font-size: 2.5rem;
        color: white;
        margin-bottom: 16px;
      }
      
      .subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 32px;
      }
    }
  }
  
  .section {
    margin-bottom: 60px;
    padding: 0 16px;
    
    .section-title {
      text-align: center;
      margin-bottom: 40px;
    }
  }
  
  .features-section {
    background-color: #f7f9fc;
    padding: 60px 16px;
    
    .feature-card {
      height: 100%;
      text-align: center;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  .cta-section {
    text-align: center;
    padding: 60px 16px;
    background-color: #f0f5ff;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .home-page {
    .hero-section {
      padding: 40px 0;
      
      .hero-content {
        h1 {
          font-size: 2rem;
        }
        
        .subtitle {
          font-size: 1rem;
        }
      }
    }
    
    .section {
      margin-bottom: 40px;
    }
  }
} 