# 服务器配置
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3001

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ai_photo_platform
DB_USER=root
DB_PASSWORD=

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# AI模型配置
DEFAULT_AI_ENDPOINT=https://api.example.com/generate
DEFAULT_AI_API_KEY=your-default-ai-api-key
CUSTOM_AI_ENDPOINT=https://custom-api.example.com/process
CUSTOM_AI_TOKEN=your-custom-ai-token

# 开发配置
USE_MOCK_AI=true
LOG_LEVEL=info

# 文件上传配置
MAX_FILE_SIZE=10485760
MAX_FILES_COUNT=5

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>
