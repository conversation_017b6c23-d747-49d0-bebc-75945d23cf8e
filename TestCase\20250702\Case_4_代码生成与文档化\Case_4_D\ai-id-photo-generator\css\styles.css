/* 
 * AI证件照生成平台 - 主样式表
 * 版本: 1.0.0
 */

/* 基础样式 */
:root {
    --primary-color: #4a6bff;
    --primary-dark: #3a56d4;
    --primary-light: #eef1ff;
    --secondary-color: #ff6b4a;
    --text-color: #333333;
    --text-light: #666666;
    --text-lighter: #999999;
    --bg-color: #ffffff;
    --bg-light: #f8f9fa;
    --border-color: #e0e0e0;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--bg-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

ul, ol {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

button, input, select, textarea {
    font-family: inherit;
    font-size: inherit;
}

/* 布局 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-color);
}

section {
    padding: 4rem 0;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    outline: none;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-light);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 头部样式 */
.site-header {
    position: sticky;
    top: 0;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
    padding: 1rem 0;
}

.site-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.main-nav ul {
    display: flex;
    gap: 2rem;
}

.main-nav a {
    color: var(--text-color);
    font-weight: 500;
}

.main-nav a:hover, .main-nav a.active {
    color: var(--primary-color);
}

.user-actions {
    display: flex;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
}

.mobile-menu-toggle span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 5px 0;
    transition: var(--transition);
}

/* 英雄区域 */
.hero-section {
    padding: 5rem 0;
    background-color: var(--primary-light);
}

.hero-section .container {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.hero-content {
    flex: 1;
}

.hero-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.hero-content p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.hero-image {
    flex: 1;
    text-align: center;
}

/* 功能特点 */
.features-section {
    background-color: var(--bg-color);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--box-shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.feature-card p {
    color: var(--text-light);
}

/* 工作流程 */
.workflow-section {
    background-color: var(--bg-light);
}

.workflow-steps {
    display: flex;
    justify-content: space-between;
    gap: 1.5rem;
    margin-top: 3rem;
}

.workflow-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.workflow-step:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 2.5rem;
    right: -1rem;
    width: 2rem;
    height: 2px;
    background-color: var(--border-color);
}

.step-number {
    width: 5rem;
    height: 5rem;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.workflow-step h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.workflow-step p {
    color: var(--text-light);
}

/* 上传区域 */
.upload-section {
    background-color: white;
}

.upload-container {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.upload-area {
    flex: 1;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.upload-placeholder h3 {
    margin: 1rem 0;
    font-size: 1.3rem;
}

.upload-placeholder p {
    color: var(--text-light);
}

.preview-container {
    flex: 1;
    display: none;
}

.preview-container h3 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.upload-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.preview-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.preview-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.preview-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    color: var(--error-color);
}

.upload-actions {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.spec-selector, .background-selector {
    flex: 1;
    min-width: 200px;
}

.spec-selector label, .background-selector label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.spec-selector select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
}

.color-options {
    display: flex;
    gap: 1rem;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.color-option input {
    position: absolute;
    opacity: 0;
}

.color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid transparent;
}

.color-option input:checked + .color-preview {
    border-color: var(--primary-color);
}

.color-preview.blue {
    background-color: #2c5ec1;
}

.color-preview.red {
    background-color: #c12c2c;
}

.color-preview.white {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
}

/* 结果区域 */
.results-section {
    background-color: var(--bg-light);
    display: none;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.select-all label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.results-actions {
    display: flex;
    gap: 1rem;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.result-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background-color: white;
}

.result-item img {
    width: 100%;
    height: 240px;
    object-fit: cover;
}

.result-checkbox {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.result-quality {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 0.9rem;
    display: flex;
    justify-content: space-between;
}

.quality-score {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.quality-score.high {
    color: var(--success-color);
}

.quality-score.medium {
    color: var(--warning-color);
}

.quality-score.low {
    color: var(--error-color);
}

.results-footer {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

/* 价格方案 */
.pricing-section {
    background-color: white;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.pricing-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.pricing-header {
    padding: 2rem;
    text-align: center;
    background-color: var(--primary-light);
}

.pricing-card.featured .pricing-header {
    background-color: var(--primary-color);
    color: white;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
}

.price span {
    font-size: 1rem;
    font-weight: 400;
}

.pricing-features {
    padding: 2rem;
}

.pricing-features ul {
    margin-bottom: 2rem;
}

.pricing-features li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.pricing-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
}

.pricing-features li.not-included {
    color: var(--text-lighter);
}

.pricing-features li.not-included::before {
    content: "×";
    color: var(--text-lighter);
}

/* FAQ部分 */
.faq-section {
    background-color: var(--bg-light);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    background-color: white;
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-question h3 {
    font-size: 1.2rem;
    font-weight: 500;
}

.faq-toggle {
    width: 20px;
    height: 20px;
    position: relative;
}

.faq-toggle::before,
.faq-toggle::after {
    content: "";
    position: absolute;
    background-color: var(--text-color);
    transition: var(--transition);
}

.faq-toggle::before {
    width: 2px;
    height: 20px;
    top: 0;
    left: 9px;
}

.faq-toggle::after {
    width: 20px;
    height: 2px;
    top: 9px;
    left: 0;
}

.faq-item.active .faq-toggle::before {
    transform: rotate(90deg);
    opacity: 0;
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 1000px;
}

/* 页脚 */
.site-footer {
    background-color: #333;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-column h3, .footer-column h4 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-column p {
    color: #ccc;
    margin-bottom: 1rem;
}

.footer-column ul li {
    margin-bottom: 0.5rem;
}

.footer-column a {
    color: #ccc;
}

.footer-column a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #ccc;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    width: 100%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-large {
    max-width: 800px;
}

.close-modal {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-lighter);
}

.modal h2 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.form-footer {
    text-align: center;
    margin-top: 1.5rem;
}

.preview-modal-content {
    display: flex;
    gap: 2rem;
}

#preview-image {
    flex: 1;
    max-height: 400px;
    object-fit: contain;
}

.preview-info {
    flex: 1;
}

.preview-info h3 {
    margin-bottom: 1rem;
}

.preview-info ul {
    margin-bottom: 2rem;
}

.preview-info li {
    margin-bottom: 0.5rem;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(74, 107, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.loading-text {
    margin-top: 1rem;
    color: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .hero-section .container {
        flex-direction: column;
    }
    
    .workflow-steps {
        flex-direction: column;
        gap: 2rem;
    }
    
    .workflow-step:not(:last-child)::after {
        display: none;
    }
    
    .upload-container {
        flex-direction: column;
    }
    
    .preview-modal-content {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .main-nav, .user-actions {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .site-header.menu-open .main-nav,
    .site-header.menu-open .user-actions {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    
    .site-header.menu-open .main-nav ul {
        flex-direction: column;
        gap: 1rem;
    }
    
    .pricing-card.featured {
        transform: none;
    }
}

@media (max-width: 576px) {
    html {
        font-size: 14px;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .upload-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}