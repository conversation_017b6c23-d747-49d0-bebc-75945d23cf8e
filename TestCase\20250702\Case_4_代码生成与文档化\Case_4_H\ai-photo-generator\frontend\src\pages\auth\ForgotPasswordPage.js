import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Form, Input, Button, Card, Alert, Typography, Result } from 'antd';
import { MailOutlined } from '@ant-design/icons';
import axios from 'axios';
import { API_ENDPOINTS } from '../../utils/constants';
import Logo from '../../components/common/Logo';

const { Title, Text, Paragraph } = Typography;

/**
 * 忘记密码页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const ForgotPasswordPage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [email, setEmail] = useState('');
  
  /**
   * 处理表单提交
   * 
   * @param {Object} values - 表单值
   */
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);
    
    try {
      await axios.post(API_ENDPOINTS.FORGOT_PASSWORD, { email: values.email });
      setSuccess(true);
      setEmail(values.email);
    } catch (err) {
      setError(
        err.response?.data?.message || 
        '发送重置密码邮件失败，请稍后再试'
      );
    } finally {
      setLoading(false);
    }
  };
  
  // 如果已成功发送重置邮件，显示成功页面
  if (success) {
    return (
      <div className="forgot-password-page" style={{ maxWidth: 500, margin: '0 auto', padding: '40px 20px' }}>
        <Result
          status="success"
          title="重置密码邮件已发送"
          subTitle={
            <div>
              <p>我们已向 <strong>{email}</strong> 发送了一封包含重置密码链接的邮件。</p>
              <p>请检查您的邮箱并点击邮件中的链接来重置密码。</p>
            </div>
          }
          extra={[
            <Button type="primary" key="login">
              <Link to="/login">返回登录</Link>
            </Button>,
            <Button key="resend" onClick={() => form.submit()} loading={loading}>
              重新发送邮件
            </Button>,
          ]}
        />
        <Paragraph type="secondary" style={{ textAlign: 'center', marginTop: 24 }}>
          如果您没有收到邮件，请检查垃圾邮件文件夹，或 <Link to="/contact">联系客服</Link>。
        </Paragraph>
      </div>
    );
  }
  
  return (
    <div className="forgot-password-page" style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
      <div className="forgot-password-logo" style={{ textAlign: 'center', marginBottom: 24 }}>
        <Logo size="large" />
        <Title level={2} style={{ marginTop: 16 }}>忘记密码</Title>
        <Text type="secondary">输入您的邮箱地址，我们将发送重置密码链接</Text>
      </div>
      
      {error && (
        <Alert
          message="操作失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false} style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)' }}>
        <Form
          form={form}
          name="forgotPassword"
          onFinish={handleSubmit}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入您的邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="邮箱"
              autoComplete="email"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="btn-primary"
            >
              发送重置链接
            </Button>
          </Form.Item>
          
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Link to="/login">返回登录</Link>
          </div>
        </Form>
      </Card>
      
      <Paragraph type="secondary" style={{ textAlign: 'center', marginTop: 24 }}>
        如果您还没有账号，请 <Link to="/register">立即注册</Link>。
      </Paragraph>
    </div>
  );
};

export default ForgotPasswordPage;