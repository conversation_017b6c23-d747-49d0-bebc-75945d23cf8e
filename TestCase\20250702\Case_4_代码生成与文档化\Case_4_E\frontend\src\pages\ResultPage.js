import React, { useState, useEffect } from 'react';
import { 
  Typography, 
  Card, 
  Row, 
  Col, 
  Button, 
  Checkbox, 
  Modal, 
  Spin, 
  message,
  Progress,
  Empty,
  Space
} from 'antd';
import { 
  DownloadOutlined, 
  ReloadOutlined, 
  LeftOutlined, 
  EyeOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;

const ResultPage = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [photos, setPhotos] = useState([]);
  const [selectedPhotos, setSelectedPhotos] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [downloading, setDownloading] = useState(false);

  // 模拟获取任务进度和结果
  useEffect(() => {
    const fetchTaskStatus = () => {
      // 模拟进度更新
      const timer = setInterval(() => {
        setProgress(prevProgress => {
          const newProgress = prevProgress + 10;
          if (newProgress >= 100) {
            clearInterval(timer);
            // 模拟加载完成后的数据
            setTimeout(() => {
              const mockPhotos = Array(5).fill().map((_, index) => ({
                id: `photo_${index}`,
                url: `https://via.placeholder.com/300x400?text=证件照${index + 1}`,
                qualityScore: (Math.random() * 0.2 + 0.8).toFixed(2) // 0.8-1.0之间的随机分数
              }));
              setPhotos(mockPhotos);
              setLoading(false);
            }, 500);
            return 100;
          }
          return newProgress;
        });
      }, 300);

      return () => clearInterval(timer);
    };

    fetchTaskStatus();
  }, [taskId]);

  // 处理照片选择
  const handleSelect = (photoId) => {
    setSelectedPhotos(prev => {
      if (prev.includes(photoId)) {
        return prev.filter(id => id !== photoId);
      } else {
        return [...prev, photoId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedPhotos(photos.map(photo => photo.id));
    } else {
      setSelectedPhotos([]);
    }
  };

  // 预览照片
  const handlePreview = (photo) => {
    setPreviewImage(photo.url);
    setPreviewVisible(true);
  };

  // 下载选中的照片
  const handleDownload = () => {
    if (selectedPhotos.length === 0) {
      message.warning('请至少选择一张照片进行下载');
      return;
    }

    setDownloading(true);
    
    // 模拟下载过程
    setTimeout(() => {
      message.success(`成功下载${selectedPhotos.length}张照片`);
      setDownloading(false);
    }, 2000);
    
    // 实际项目中，这里应该调用API进行下载
  };

  // 重新生成
  const handleRegenerate = () => {
    setLoading(true);
    setProgress(0);
    setPhotos([]);
    
    // 模拟重新生成过程
    const timer = setInterval(() => {
      setProgress(prevProgress => {
        const newProgress = prevProgress + 10;
        if (newProgress >= 100) {
          clearInterval(timer);
          // 模拟加载完成后的数据
          setTimeout(() => {
            const mockPhotos = Array(5).fill().map((_, index) => ({
              id: `photo_${index}_new`,
              url: `https://via.placeholder.com/300x400?text=新证件照${index + 1}`,
              qualityScore: (Math.random() * 0.2 + 0.8).toFixed(2)
            }));
            setPhotos(mockPhotos);
            setSelectedPhotos([]);
            setLoading(false);
          }, 500);
          return 100;
        }
        return newProgress;
      });
    }, 300);
  };

  return (
    <div className="result-page">
      <Title level={2} style={{ textAlign: 'center', marginBottom: 30 }}>证件照生成结果</Title>
      
      <Card style={{ maxWidth: 1000, margin: '0 auto' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 20 }}>
              <Progress percent={progress} status="active" />
              <Paragraph style={{ marginTop: 10 }}>
                正在处理您的照片，请稍候...
              </Paragraph>
            </div>
          </div>
        ) : (
          <>
            {photos.length > 0 ? (
              <>
                <div style={{ marginBottom: 20 }}>
                  <Checkbox 
                    onChange={handleSelectAll}
                    checked={selectedPhotos.length === photos.length}
                    indeterminate={selectedPhotos.length > 0 && selectedPhotos.length < photos.length}
                  >
                    全选
                  </Checkbox>
                  <div style={{ float: 'right' }}>
                    <Space>
                      <Button 
                        type="primary" 
                        icon={<DownloadOutlined />}
                        onClick={handleDownload}
                        disabled={selectedPhotos.length === 0}
                        loading={downloading}
                      >
                        下载选中
                      </Button>
                      <Button 
                        icon={<ReloadOutlined />}
                        onClick={handleRegenerate}
                      >
                        重新生成
                      </Button>
                      <Button 
                        icon={<LeftOutlined />}
                        onClick={() => navigate('/upload')}
                      >
                        返回上传
                      </Button>
                    </Space>
                  </div>
                </div>

                <Row gutter={[16, 16]}>
                  {photos.map(photo => (
                    <Col xs={24} sm={12} md={8} lg={6} xl={4} key={photo.id}>
                      <Card
                        hoverable
                        cover={
                          <div style={{ position: 'relative' }}>
                            <img 
                              alt="证件照" 
                              src={photo.url} 
                              style={{ width: '100%' }}
                            />
                            <div 
                              style={{ 
                                position: 'absolute', 
                                top: 0, 
                                right: 0, 
                                padding: '5px 10px', 
                                background: 'rgba(0,0,0,0.5)', 
                                color: 'white',
                                borderBottomLeftRadius: '4px'
                              }}
                            >
                              {photo.qualityScore}分
                            </div>
                          </div>
                        }
                        bodyStyle={{ padding: '12px' }}
                        style={{ 
                          border: selectedPhotos.includes(photo.id) 
                            ? '2px solid #1890ff' 
                            : '1px solid #d9d9d9'
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Checkbox
                            checked={selectedPhotos.includes(photo.id)}
                            onChange={() => handleSelect(photo.id)}
                          />
                          <Button 
                            type="text" 
                            icon={<EyeOutlined />} 
                            onClick={() => handlePreview(photo)}
                          />
                        </div>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </>
            ) : (
              <Empty description="暂无生成结果" />
            )}
          </>
        )}
      </Card>

      <Modal
        visible={previewVisible}
        title="照片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="预览" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default ResultPage;