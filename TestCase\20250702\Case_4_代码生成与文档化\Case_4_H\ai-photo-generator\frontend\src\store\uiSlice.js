import { createSlice } from '@reduxjs/toolkit';
import { THEME_MODES } from '../utils/constants';

/**
 * 初始状态
 */
const initialState = {
  sidebarCollapsed: false,
  mobileMenuVisible: false,
  currentTheme: THEME_MODES.LIGHT,
  loading: {
    global: false,
  },
  modals: {
    deleteConfirm: {
      visible: false,
      itemId: null,
      itemType: null,
    },
    photoPreview: {
      visible: false,
      photo: null,
    },
    sharePhoto: {
      visible: false,
      photoId: null,
    },
    subscriptionPrompt: {
      visible: false,
      feature: null,
    },
  },
};

/**
 * UI slice
 */
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 侧边栏折叠状态
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // 移动端菜单可见状态
    toggleMobileMenu: (state) => {
      state.mobileMenuVisible = !state.mobileMenuVisible;
    },
    setMobileMenuVisible: (state, action) => {
      state.mobileMenuVisible = action.payload;
    },
    
    // 主题设置
    setTheme: (state, action) => {
      state.currentTheme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    
    // 全局加载状态
    setGlobalLoading: (state, action) => {
      state.loading.global = action.payload;
    },
    
    // 删除确认模态框
    showDeleteConfirmModal: (state, action) => {
      state.modals.deleteConfirm = {
        visible: true,
        itemId: action.payload.itemId,
        itemType: action.payload.itemType,
      };
    },
    hideDeleteConfirmModal: (state) => {
      state.modals.deleteConfirm = {
        visible: false,
        itemId: null,
        itemType: null,
      };
    },
    
    // 照片预览模态框
    showPhotoPreviewModal: (state, action) => {
      state.modals.photoPreview = {
        visible: true,
        photo: action.payload,
      };
    },
    hidePhotoPreviewModal: (state) => {
      state.modals.photoPreview = {
        visible: false,
        photo: null,
      };
    },
    
    // 分享照片模态框
    showSharePhotoModal: (state, action) => {
      state.modals.sharePhoto = {
        visible: true,
        photoId: action.payload,
      };
    },
    hideSharePhotoModal: (state) => {
      state.modals.sharePhoto = {
        visible: false,
        photoId: null,
      };
    },
    
    // 订阅提示模态框
    showSubscriptionPromptModal: (state, action) => {
      state.modals.subscriptionPrompt = {
        visible: true,
        feature: action.payload,
      };
    },
    hideSubscriptionPromptModal: (state) => {
      state.modals.subscriptionPrompt = {
        visible: false,
        feature: null,
      };
    },
    
    // 重置所有UI状态
    resetUiState: () => initialState,
  },
});

export const {
  toggleSidebar,
  setSidebarCollapsed,
  toggleMobileMenu,
  setMobileMenuVisible,
  setTheme,
  setGlobalLoading,
  showDeleteConfirmModal,
  hideDeleteConfirmModal,
  showPhotoPreviewModal,
  hidePhotoPreviewModal,
  showSharePhotoModal,
  hideSharePhotoModal,
  showSubscriptionPromptModal,
  hideSubscriptionPromptModal,
  resetUiState,
} = uiSlice.actions;

export default uiSlice.reducer;