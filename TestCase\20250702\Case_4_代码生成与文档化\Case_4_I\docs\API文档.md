# AI证件照生成平台 API文档

## 基础信息

- 基础URL: `http://localhost:5000/api`
- 所有需要认证的API都需要在请求头中包含JWT令牌：
  ```
  Authorization: Bearer <token>
  ```

## 用户认证API

### 注册用户

- **URL**: `/users/register`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "username": "用户名",
    "email": "邮箱@example.com",
    "password": "密码"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "success": true,
    "token": "JWT令牌",
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "邮箱@example.com",
      "avatar": "",
      "role": "user",
      "remainingCredits": 100
    }
  }
  ```
- **错误响应** (400):
  ```json
  {
    "success": false,
    "message": "该邮箱已被注册"
  }
  ```

### 用户登录

- **URL**: `/users/login`
- **方法**: `POST`
- **认证**: 不需要
- **请求体**:
  ```json
  {
    "email": "邮箱@example.com",
    "password": "密码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "success": true,
    "token": "JWT令牌",
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "邮箱@example.com",
      "avatar": "",
      "role": "user",
      "remainingCredits": 100
    }
  }
  ```
- **错误响应** (401):
  ```json
  {
    "success": false,
    "message": "无效的凭据"
  }
  ```

### 获取当前用户信息

- **URL**: `/users/me`
- **方法**: `GET`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "success": true,
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "邮箱@example.com",
      "avatar": "",
      "role": "user",
      "remainingCredits": 100,
      "createdAt": "2023-07-01T12:00:00.000Z"
    }
  }
  ```

### 更新用户信息

- **URL**: `/users/me`
- **方法**: `PUT`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "username": "新用户名",
    "avatar": "头像URL"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "success": true,
    "user": {
      "id": "用户ID",
      "username": "新用户名",
      "email": "邮箱@example.com",
      "avatar": "头像URL",
      "role": "user",
      "remainingCredits": 100
    }
  }
  ```

### 更新密码

- **URL**: `/users/password`
- **方法**: `PUT`
- **认证**: 需要
- **请求体**:
  ```json
  {
    "currentPassword": "当前密码",
    "newPassword": "新密码"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "success": true,
    "message": "密码更新成功",
    "token": "新JWT令牌"
  }
  ```
- **错误响应** (401):
  ```json
  {
    "success": false,
    "message": "当前密码不正确"
  }
  ```

## 照片上传API

### 上传单张照片

- **URL**: `/upload/single`
- **方法**: `POST`
- **认证**: 需要
- **请求体**: `multipart/form-data`
  - `photo`: 照片文件 (JPG/JPEG/PNG, 最大10MB)
  - `specification`: 证件照规格 (one_inch, two_inch, small_two_inch, passport, visa)
- **成功响应** (201):
  ```json
  {
    "success": true,
    "taskId": "任务ID",
    "message": "照片上传成功，开始处理"
  }
  ```
- **错误响应** (400):
  ```json
  {
    "success": false,
    "message": "请上传照片"
  }
  ```

### 批量上传照片

- **URL**: `/upload/batch`
- **方法**: `POST`
- **认证**: 需要
- **请求体**: `multipart/form-data`
  - `photos`: 照片文件数组 (JPG/JPEG/PNG, 最大10MB, 最多5张)
  - `specification`: 证件照规格 (one_inch, two_inch, small_two_inch, passport, visa)
- **成功响应** (201):
  ```json
  {
    "success": true,
    "taskId": "任务ID",
    "message": "照片上传成功，开始处理",
    "uploadedCount": 3
  }
  ```
- **错误响应** (400):
  ```json
  {
    "success": false,
    "message": "最多只能上传5张照片"
  }
  ```

## 照片生成API

### 获取任务状态

- **URL**: `/generation/status/:taskId`
- **方法**: `GET`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "success": true,
    "task": {
      "id": "任务ID",
      "status": "pending|processing|completed|failed",
      "originalCount": 3,
      "generatedCount": 0,
      "specification": "one_inch",
      "createdAt": "2023-07-01T12:00:00.000Z",
      "errorMessage": null
    }
  }
  ```

### 获取生成的照片列表

- **URL**: `/generation/photos/:taskId`
- **方法**: `GET`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "success": true,
    "photos": [
      {
        "id": "照片ID",
        "path": "照片路径",
        "filename": "文件名",
        "originalPhotoIndex": 0,
        "qualityScore": 85
      },
      {
        "id": "照片ID",
        "path": "照片路径",
        "filename": "文件名",
        "originalPhotoIndex": 1,
        "qualityScore": 92
      }
    ],
    "specification": "one_inch"
  }
  ```
- **错误响应** (400):
  ```json
  {
    "success": false,
    "message": "任务尚未完成，当前状态: processing",
    "status": "processing"
  }
  ```

### 下载单张生成的照片

- **URL**: `/generation/download/:taskId/:photoId`
- **方法**: `GET`
- **认证**: 需要
- **响应**: 照片文件 (Content-Type: image/jpeg)

### 批量下载生成的照片

- **URL**: `/generation/download-batch/:taskId`
- **方法**: `GET`
- **认证**: 需要
- **响应**: 照片文件 (Content-Type: image/jpeg)

### 重新生成照片

- **URL**: `/generation/regenerate/:taskId`
- **方法**: `POST`
- **认证**: 需要
- **成功响应** (200):
  ```json
  {
    "success": true,
    "message": "开始重新生成照片",
    "taskId": "任务ID"
  }
  ```
- **错误响应** (402):
  ```json
  {
    "success": false,
    "message": "积分不足，请充值"
  }
  ```

## 错误码说明

- **400**: 请求参数错误
- **401**: 未授权（未登录或token无效）
- **402**: 需要付款（积分不足）
- **403**: 禁止访问（权限不足）
- **404**: 资源不存在
- **500**: 服务器内部错误