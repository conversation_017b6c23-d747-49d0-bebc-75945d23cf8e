import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import uploadService from '@/services/uploadService'

// 异步actions
export const uploadSingle = createAsyncThunk(
  'upload/uploadSingle',
  async (file, { rejectWithValue }) => {
    try {
      const response = await uploadService.uploadSingle(file)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '上传失败' })
    }
  }
)

export const uploadMultiple = createAsyncThunk(
  'upload/uploadMultiple',
  async (files, { rejectWithValue }) => {
    try {
      const response = await uploadService.uploadMultiple(files)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '批量上传失败' })
    }
  }
)

export const getUploadInfo = createAsyncThunk(
  'upload/getUploadInfo',
  async (uploadId, { rejectWithValue }) => {
    try {
      const response = await uploadService.getUploadInfo(uploadId)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '获取上传信息失败' })
    }
  }
)

export const deleteUpload = createAsyncThunk(
  'upload/deleteUpload',
  async (uploadId, { rejectWithValue }) => {
    try {
      await uploadService.deleteUpload(uploadId)
      return uploadId
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '删除上传记录失败' })
    }
  }
)

// 初始状态
const initialState = {
  uploads: [],
  currentUpload: null,
  uploading: false,
  uploadProgress: 0,
  error: null
}

// 创建slice
const uploadSlice = createSlice({
  name: 'upload',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearUploads: (state) => {
      state.uploads = []
      state.currentUpload = null
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload
    },
    resetUploadProgress: (state) => {
      state.uploadProgress = 0
    }
  },
  extraReducers: (builder) => {
    builder
      // 单文件上传
      .addCase(uploadSingle.pending, (state) => {
        state.uploading = true
        state.error = null
        state.uploadProgress = 0
      })
      .addCase(uploadSingle.fulfilled, (state, action) => {
        state.uploading = false
        state.currentUpload = action.payload
        state.uploads.push(action.payload)
        state.uploadProgress = 100
        state.error = null
      })
      .addCase(uploadSingle.rejected, (state, action) => {
        state.uploading = false
        state.error = action.payload
        state.uploadProgress = 0
      })
      
      // 多文件上传
      .addCase(uploadMultiple.pending, (state) => {
        state.uploading = true
        state.error = null
        state.uploadProgress = 0
      })
      .addCase(uploadMultiple.fulfilled, (state, action) => {
        state.uploading = false
        state.uploads = [...state.uploads, ...action.payload.uploads]
        state.uploadProgress = 100
        state.error = null
      })
      .addCase(uploadMultiple.rejected, (state, action) => {
        state.uploading = false
        state.error = action.payload
        state.uploadProgress = 0
      })
      
      // 获取上传信息
      .addCase(getUploadInfo.pending, (state) => {
        state.error = null
      })
      .addCase(getUploadInfo.fulfilled, (state, action) => {
        const existingIndex = state.uploads.findIndex(
          upload => upload.uploadId === action.payload.uploadId
        )
        if (existingIndex >= 0) {
          state.uploads[existingIndex] = action.payload
        } else {
          state.uploads.push(action.payload)
        }
        state.error = null
      })
      .addCase(getUploadInfo.rejected, (state, action) => {
        state.error = action.payload
      })
      
      // 删除上传记录
      .addCase(deleteUpload.pending, (state) => {
        state.error = null
      })
      .addCase(deleteUpload.fulfilled, (state, action) => {
        state.uploads = state.uploads.filter(
          upload => upload.uploadId !== action.payload
        )
        if (state.currentUpload?.uploadId === action.payload) {
          state.currentUpload = null
        }
        state.error = null
      })
      .addCase(deleteUpload.rejected, (state, action) => {
        state.error = action.payload
      })
  }
})

export const { 
  clearError, 
  clearUploads, 
  setUploadProgress, 
  resetUploadProgress 
} = uploadSlice.actions

export default uploadSlice.reducer
