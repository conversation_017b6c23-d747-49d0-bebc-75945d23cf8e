import React from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import { Layout, Menu, Button, Avatar, Dropdown } from 'antd';
import { UserOutlined, DownOutlined } from '@ant-design/icons';
import { RootState } from '../../store';
import './AppHeader.scss';

const { Header } = Layout;

const AppHeader: React.FC = () => {
  const location = useLocation();
  const { currentUser, isAuthenticated } = useSelector((state: RootState) => state.user);
  
  // 导航菜单项
  const menuItems = [
    {
      key: '/',
      label: <Link to="/">首页</Link>
    },
    {
      key: '/upload',
      label: <Link to="/upload">上传照片</Link>
    },
    {
      key: '/pricing',
      label: <Link to="/pricing">价格方案</Link>
    },
    {
      key: '/about',
      label: <Link to="/about">关于我们</Link>
    }
  ];
  
  // 用户下拉菜单
  const userMenu = (
    <Menu>
      <Menu.Item key="profile">
        <Link to="/user/profile">个人信息</Link>
      </Menu.Item>
      <Menu.Item key="history">
        <Link to="/user/history">使用记录</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout">
        退出登录
      </Menu.Item>
    </Menu>
  );
  
  return (
    <Header className="app-header">
      <div className="header-content">
        <div className="logo">
          <Link to="/">
            <h1>AI证件照</h1>
          </Link>
        </div>
        
        <div className="nav-menu">
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
          />
        </div>
        
        <div className="user-info">
          {isAuthenticated && currentUser ? (
            <Dropdown overlay={userMenu}>
              <div className="user-dropdown">
                <Avatar src={currentUser.avatar} icon={<UserOutlined />} />
                <span className="username">{currentUser.username}</span>
                <DownOutlined />
              </div>
            </Dropdown>
          ) : (
            <div className="auth-buttons">
              <Link to="/login">
                <Button>登录</Button>
              </Link>
              <Link to="/register">
                <Button type="primary">注册</Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </Header>
  );
};

export default AppHeader; 