const express = require('express');
const router = express.Router();
const { uploadSinglePhoto, uploadBatchPhotos } = require('../controllers/uploadController');
const { protect } = require('../middleware/auth');
const { upload, handleUploadError } = require('../middleware/upload');
const { checkCredits } = require('../middleware/auth');

// 上传单张照片
router.post(
  '/single',
  protect,
  checkCredits(1), // 需要1积分
  upload.single('photo'),
  handleUploadError,
  uploadSinglePhoto
);

// 批量上传照片
router.post(
  '/batch',
  protect,
  upload.array('photos', 5), // 最多5张
  handleUploadError,
  uploadBatchPhotos
);

module.exports = router;