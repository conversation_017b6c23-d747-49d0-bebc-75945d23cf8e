import React, { useState } from 'react';
import { 
  Typography, 
  Upload, 
  Button, 
  message, 
  Radio, 
  Form, 
  Card, 
  Divider,
  Space,
  Spin
} from 'antd';
import { 
  InboxOutlined, 
  UploadOutlined, 
  CheckCircleOutlined 
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;
const { Dragger } = Upload;

const UploadPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  // 证件照规格选项
  const photoSpecs = [
    { label: '1寸证件照 (25mm × 35mm)', value: '1inch' },
    { label: '2寸证件照 (35mm × 49mm)', value: '2inch' },
    { label: '小2寸证件照 (33mm × 48mm)', value: 'small2inch' },
    { label: '大1寸证件照 (33mm × 45mm)', value: 'large1inch' },
  ];

  // 背景颜色选项
  const backgroundColors = [
    { label: '蓝色背景', value: 'blue' },
    { label: '红色背景', value: 'red' },
    { label: '白色背景', value: 'white' },
  ];

  // 文件上传前的校验
  const beforeUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传JPG/PNG格式的图片!');
      return false;
    }
    
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片大小不能超过10MB!');
      return false;
    }
    
    // 限制上传数量
    if (fileList.length >= 5) {
      message.error('最多只能上传5张照片!');
      return false;
    }
    
    return true;
  };

  // 处理文件上传状态变化
  const handleChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    if (fileList.length === 0) {
      message.error('请至少上传一张照片!');
      return;
    }

    setUploading(true);

    try {
      // 模拟上传过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 实际项目中，这里应该调用API上传文件并获取任务ID
      const mockTaskId = 'task_' + Date.now();
      
      setUploadSuccess(true);
      
      // 延迟跳转到结果页面
      setTimeout(() => {
        navigate(`/result/${mockTaskId}`);
      }, 1500);
      
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败，请重试!');
    } finally {
      setUploading(false);
    }
  };

  // 上传组件的属性配置
  const uploadProps = {
    name: 'file',
    multiple: true,
    fileList,
    beforeUpload,
    onChange: handleChange,
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    customRequest: ({ onSuccess }) => {
      // 阻止默认上传行为，改为表单提交时统一处理
      setTimeout(() => {
        onSuccess("ok");
      }, 0);
    },
  };

  return (
    <div className="upload-page">
      <Title level={2} style={{ textAlign: 'center', marginBottom: 30 }}>上传照片</Title>
      
      <Card style={{ maxWidth: 800, margin: '0 auto' }}>
        {uploadSuccess ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a' }} />
            <Title level={3} style={{ margin: '20px 0' }}>上传成功!</Title>
            <Paragraph>正在跳转到处理页面...</Paragraph>
            <Spin />
          </div>
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              spec: '1inch',
              background: 'blue'
            }}
          >
            <Form.Item
              label="上传照片"
              name="photos"
              rules={[{ required: true, message: '请上传照片!' }]}
            >
              <Dragger {...uploadProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持JPG、PNG格式，单张照片大小不超过10MB，最多上传5张照片
                </p>
              </Dragger>
            </Form.Item>

            <Divider />

            <Form.Item
              label="证件照规格"
              name="spec"
              rules={[{ required: true, message: '请选择证件照规格!' }]}
            >
              <Radio.Group>
                <Space direction="vertical">
                  {photoSpecs.map(spec => (
                    <Radio key={spec.value} value={spec.value}>{spec.label}</Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="背景颜色"
              name="background"
              rules={[{ required: true, message: '请选择背景颜色!' }]}
            >
              <Radio.Group>
                <Space>
                  {backgroundColors.map(color => (
                    <Radio key={color.value} value={color.value}>{color.label}</Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                icon={<UploadOutlined />}
                loading={uploading}
                block
              >
                {uploading ? '处理中...' : '开始生成'}
              </Button>
            </Form.Item>
          </Form>
        )}
      </Card>
    </div>
  );
};

export default UploadPage;