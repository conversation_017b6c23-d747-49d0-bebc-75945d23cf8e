import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Typography, 
  Card, 
  Button, 
  Tabs, 
  Form, 
  Input, 
  Upload, 
  Avatar, 
  Row, 
  Col, 
  Divider, 
  Alert, 
  Spin, 
  Modal,
  message
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  LockOutlined, 
  UploadOutlined, 
  EditOutlined,
  SaveOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { 
  fetchUserProfile, 
  updateUserProfile, 
  updateUserPassword, 
  deleteUserAccount,
  selectUser, 
  selectUserLoading, 
  selectUserError 
} from '../../store/userSlice';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

/**
 * 用户个人资料页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const ProfilePage = () => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const loading = useSelector(selectUserLoading);
  const error = useSelector(selectUserError);
  
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('profile');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  
  // 获取用户资料
  useEffect(() => {
    dispatch(fetchUserProfile());
  }, [dispatch]);
  
  // 初始化表单值
  useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        username: user.username,
        email: user.email,
        fullName: user.fullName || '',
        phone: user.phone || '',
      });
      
      setAvatarUrl(user.avatarUrl || '');
    }
  }, [user, profileForm]);
  
  /**
   * 处理头像上传
   * 
   * @param {Object} info - 上传信息
   */
  const handleAvatarUpload = (info) => {
    if (info.file.status === 'done') {
      setAvatarUrl(info.file.response.url);
      message.success('头像上传成功');
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };
  
  /**
   * 处理个人资料更新
   * 
   * @param {Object} values - 表单值
   */
  const handleProfileUpdate = async (values) => {
    setUpdateLoading(true);
    try {
      await dispatch(updateUserProfile({
        ...values,
        avatarUrl
      })).unwrap();
      
      message.success('个人资料更新成功');
      setIsEditing(false);
    } catch (err) {
      message.error('更新失败: ' + err.message);
    } finally {
      setUpdateLoading(false);
    }
  };
  
  /**
   * 处理密码更新
   * 
   * @param {Object} values - 表单值
   */
  const handlePasswordUpdate = async (values) => {
    setPasswordLoading(true);
    try {
      await dispatch(updateUserPassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      })).unwrap();
      
      message.success('密码更新成功');
      passwordForm.resetFields();
    } catch (err) {
      message.error('更新失败: ' + err.message);
    } finally {
      setPasswordLoading(false);
    }
  };
  
  /**
   * 处理账户删除
   */
  const handleAccountDelete = () => {
    confirm({
      title: '确认删除账户',
      content: '您确定要删除您的账户吗？此操作无法撤销，所有数据将被永久删除。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deleteUserAccount()).unwrap();
          message.success('账户已成功删除');
          // 重定向到首页或登录页
          window.location.href = '/';
        } catch (err) {
          message.error('删除失败: ' + err.message);
        }
      },
    });
  };
  
  // 如果正在加载，显示加载状态
  if (loading && !user) {
    return (
      <div className="profile-page page-container">
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </div>
    );
  }
  
  return (
    <div className="profile-page page-container">
      <div className="page-title">
        <Title level={2}>个人资料</Title>
      </div>
      
      {error && (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="个人资料" key="profile">
            <div className="profile-content">
              <Row gutter={[24, 24]}>
                <Col xs={24} md={8}>
                  <div className="avatar-section" style={{ textAlign: 'center' }}>
                    <Avatar 
                      size={120} 
                      src={avatarUrl} 
                      icon={<UserOutlined />} 
                      style={{ marginBottom: 16 }}
                    />
                    
                    {isEditing && (
                      <div className="avatar-upload">
                        <Upload
                          name="avatar"
                          action="/api/user/avatar"
                          showUploadList={false}
                          onChange={handleAvatarUpload}
                        >
                          <Button icon={<UploadOutlined />}>更换头像</Button>
                        </Upload>
                      </div>
                    )}
                    
                    <div className="user-info" style={{ marginTop: 16 }}>
                      <Title level={4}>{user?.username}</Title>
                      <Text type="secondary">{user?.email}</Text>
                      
                      <div style={{ marginTop: 16 }}>
                        <Text>会员等级: </Text>
                        <Text strong>{user?.membership || '免费用户'}</Text>
                      </div>
                      
                      <div style={{ marginTop: 8 }}>
                        <Text>注册时间: </Text>
                        <Text>{user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : '-'}</Text>
                      </div>
                    </div>
                  </div>
                </Col>
                
                <Col xs={24} md={16}>
                  <div className="profile-form">
                    <div className="section-header" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                      <Title level={4}>基本信息</Title>
                      {!isEditing ? (
                        <Button 
                          icon={<EditOutlined />} 
                          onClick={() => setIsEditing(true)}
                        >
                          编辑资料
                        </Button>
                      ) : (
                        <Button 
                          icon={<SaveOutlined />} 
                          type="primary"
                          onClick={() => profileForm.submit()}
                          loading={updateLoading}
                          className="btn-primary"
                        >
                          保存
                        </Button>
                      )}
                    </div>
                    
                    <Form
                      form={profileForm}
                      layout="vertical"
                      onFinish={handleProfileUpdate}
                      disabled={!isEditing}
                    >
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input prefix={<UserOutlined />} placeholder="用户名" />
                      </Form.Item>
                      
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' },
                        ]}
                      >
                        <Input prefix={<MailOutlined />} placeholder="邮箱" disabled />
                      </Form.Item>
                      
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name="fullName"
                            label="姓名"
                          >
                            <Input placeholder="您的真实姓名" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="phone"
                            label="手机号码"
                          >
                            <Input placeholder="您的手机号码" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </div>
                </Col>
              </Row>
            </div>
          </TabPane>
          
          <TabPane tab="安全设置" key="security">
            <div className="security-content">
              <Title level={4}>修改密码</Title>
              <Paragraph>
                定期更改密码可以提高账户安全性。请确保使用强密码，包含字母、数字和特殊字符。
              </Paragraph>
              
              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordUpdate}
              >
                <Form.Item
                  name="currentPassword"
                  label="当前密码"
                  rules={[{ required: true, message: '请输入当前密码' }]}
                >
                  <Input.Password 
                    prefix={<LockOutlined />} 
                    placeholder="当前密码" 
                  />
                </Form.Item>
                
                <Form.Item
                  name="newPassword"
                  label="新密码"
                  rules={[
                    { required: true, message: '请输入新密码' },
                    { min: 8, message: '密码长度至少为8个字符' },
                  ]}
                  hasFeedback
                >
                  <Input.Password 
                    prefix={<LockOutlined />} 
                    placeholder="新密码" 
                  />
                </Form.Item>
                
                <Form.Item
                  name="confirmPassword"
                  label="确认新密码"
                  dependencies={['newPassword']}
                  hasFeedback
                  rules={[
                    { required: true, message: '请确认新密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('newPassword') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password 
                    prefix={<LockOutlined />} 
                    placeholder="确认新密码" 
                  />
                </Form.Item>
                
                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={passwordLoading}
                    className="btn-primary"
                  >
                    更新密码
                  </Button>
                </Form.Item>
              </Form>
              
              <Divider />
              
              <div className="danger-zone">
                <Title level={4}>危险操作</Title>
                <Paragraph type="danger">
                  以下操作将永久删除您的账户和所有相关数据，此操作无法撤销。
                </Paragraph>
                
                <Button 
                  danger 
                  icon={<DeleteOutlined />}
                  onClick={handleAccountDelete}
                >
                  删除我的账户
                </Button>
              </div>
            </div>
          </TabPane>
          
          <TabPane tab="订阅信息" key="subscription">
            <div className="subscription-content">
              <Title level={4}>当前订阅</Title>
              
              <Card className="subscription-card">
                <div className="subscription-info">
                  <div className="subscription-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                    <div>
                      <Title level={5} style={{ margin: 0 }}>
                        {user?.membership === 'premium' ? '高级会员' : '免费用户'}
                      </Title>
                      {user?.membership === 'premium' && (
                        <Text type="success">有效期至 {user?.membershipExpiry ? new Date(user.membershipExpiry).toLocaleDateString() : '永久'}</Text>
                      )}
                    </div>
                    
                    {user?.membership !== 'premium' && (
                      <Button type="primary" className="btn-primary">
                        升级到高级会员
                      </Button>
                    )}
                  </div>
                  
                  <div className="subscription-details">
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <Text type="secondary">每月可生成照片</Text>
                        <div>
                          <Text strong>{user?.membership === 'premium' ? '无限' : '10'}</Text>
                        </div>
                      </Col>
                      <Col span={8}>
                        <Text type="secondary">本月已生成</Text>
                        <div>
                          <Text strong>{user?.photosGenerated || 0}</Text>
                        </div>
                      </Col>
                      <Col span={8}>
                        <Text type="secondary">剩余可生成</Text>
                        <div>
                          <Text strong>
                            {user?.membership === 'premium' 
                              ? '无限' 
                              : Math.max(0, 10 - (user?.photosGenerated || 0))}
                          </Text>
                        </div>
                      </Col>
                    </Row>
                  </div>
                </div>
              </Card>
              
              {user?.membership !== 'premium' && (
                <div className="upgrade-section" style={{ marginTop: 24 }}>
                  <Title level={4}>升级到高级会员</Title>
                  <Paragraph>
                    升级到高级会员，享受更多功能和服务：
                  </Paragraph>
                  
                  <Row gutter={[16, 16]}>
                    <Col xs={24} md={8}>
                      <Card title="无限生成">
                        <Paragraph>
                          每月无限次生成证件照，不再受到数量限制。
                        </Paragraph>
                      </Card>
                    </Col>
                    <Col xs={24} md={8}>
                      <Card title="高级编辑">
                        <Paragraph>
                          使用高级编辑功能，包括AI美化、背景替换等。
                        </Paragraph>
                      </Card>
                    </Col>
                    <Col xs={24} md={8}>
                      <Card title="批量处理">
                        <Paragraph>
                          支持批量上传和处理多张照片，提高效率。
                        </Paragraph>
                      </Card>
                    </Col>
                  </Row>
                  
                  <div style={{ textAlign: 'center', marginTop: 24 }}>
                    <Button type="primary" size="large" className="btn-primary">
                      查看会员方案
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ProfilePage;