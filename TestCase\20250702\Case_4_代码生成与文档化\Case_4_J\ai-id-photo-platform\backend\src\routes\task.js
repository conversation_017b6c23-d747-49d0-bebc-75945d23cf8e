const express = require('express');
const router = express.Router();
const taskController = require('../controllers/taskController');
const { optionalAuth, authenticateToken } = require('../middleware/auth');

/**
 * @route GET /api/task/:taskId
 * @desc 获取任务详情
 * @access Public (可选认证)
 */
router.get('/:taskId',
  optionalAuth,
  taskController.getTaskDetail
);

/**
 * @route GET /api/task/:taskId/download
 * @desc 批量下载任务的所有照片
 * @access Public (可选认证)
 */
router.get('/:taskId/download',
  optionalAuth,
  taskController.downloadTaskPhotos
);

/**
 * @route GET /api/task/photo/:photoId/download
 * @desc 下载单张照片
 * @access Public (可选认证)
 */
router.get('/photo/:photoId/download',
  optionalAuth,
  taskController.downloadPhoto
);

/**
 * @route DELETE /api/task/:taskId
 * @desc 删除任务及其相关数据
 * @access Public (可选认证)
 */
router.delete('/:taskId',
  optionalAuth,
  taskController.deleteTask
);

/**
 * @route GET /api/task/user/stats
 * @desc 获取用户任务统计信息
 * @access Private
 */
router.get('/user/stats',
  authenticateToken,
  taskController.getTaskStats
);

module.exports = router;
