# AI证件照生成平台 数据库设计文档

## 数据库概述

AI证件照生成平台使用MySQL关系型数据库存储数据。主要包含以下几个表：
- 用户表 (users)
- 上传记录表 (uploads)
- 生成任务表 (generation_tasks)
- 生成照片表 (generated_photos)

## 数据库表结构

### 用户表 (users)

存储用户的基本信息和认证数据。

```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  avatar VARCHAR(500),
  credits INT NOT NULL DEFAULT 5,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

| 字段名 | 类型 | 约束 | 描述 |
|-------|-----|------|-----|
| id | INT | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | NOT NULL, UNIQUE | 用户名 |
| email | VARCHAR(100) | NOT NULL, UNIQUE | 电子邮箱 |
| password | VARCHAR(255) | NOT NULL | 密码哈希 |
| avatar | VARCHAR(500) | | 用户头像URL |
| credits | INT | NOT NULL, DEFAULT 5 | 用户积分/可用次数 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 上传记录表 (uploads)

存储用户上传的照片信息。

```sql
CREATE TABLE uploads (
  id VARCHAR(50) PRIMARY KEY,
  user_id INT,
  original_filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INT NOT NULL,
  file_type VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

| 字段名 | 类型 | 约束 | 描述 |
|-------|-----|------|-----|
| id | VARCHAR(50) | PRIMARY KEY | 上传ID (UUID) |
| user_id | INT | FOREIGN KEY | 用户ID (如果已登录) |
| original_filename | VARCHAR(255) | NOT NULL | 原始文件名 |
| file_path | VARCHAR(500) | NOT NULL | 文件存储路径 |
| file_size | INT | NOT NULL | 文件大小 (字节) |
| file_type | VARCHAR(20) | NOT NULL | 文件类型 (MIME) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 上传时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 生成任务表 (generation_tasks)

存储证件照生成任务的信息。

```sql
CREATE TABLE generation_tasks (
  id VARCHAR(50) PRIMARY KEY,
  upload_id VARCHAR(50) NOT NULL,
  spec VARCHAR(20) NOT NULL,
  background VARCHAR(20) NOT NULL,
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  progress INT DEFAULT 0,
  completed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE
);

CREATE INDEX idx_generation_tasks_upload_id ON generation_tasks(upload_id);
CREATE INDEX idx_generation_tasks_status ON generation_tasks(status);
```

| 字段名 | 类型 | 约束 | 描述 |
|-------|-----|------|-----|
| id | VARCHAR(50) | PRIMARY KEY | 任务ID (UUID) |
| upload_id | VARCHAR(50) | NOT NULL, FOREIGN KEY | 关联的上传ID |
| spec | VARCHAR(20) | NOT NULL | 证件照规格 |
| background | VARCHAR(20) | NOT NULL | 背景颜色 |
| status | ENUM | DEFAULT 'pending' | 任务状态 |
| progress | INT | DEFAULT 0 | 处理进度 (0-100) |
| completed_at | TIMESTAMP | NULL | 完成时间 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 生成照片表 (generated_photos)

存储生成的证件照信息。

```sql
CREATE TABLE generated_photos (
  id VARCHAR(50) PRIMARY KEY,
  task_id VARCHAR(50) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_url VARCHAR(500) NOT NULL,
  quality_score DECIMAL(3,2) NOT NULL DEFAULT 0.0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE
);

CREATE INDEX idx_generated_photos_task_id ON generated_photos(task_id);
```

| 字段名 | 类型 | 约束 | 描述 |
|-------|-----|------|-----|
| id | VARCHAR(50) | PRIMARY KEY | 照片ID (UUID) |
| task_id | VARCHAR(50) | NOT NULL, FOREIGN KEY | 关联的任务ID |
| file_path | VARCHAR(500) | NOT NULL | 文件存储路径 |
| file_url | VARCHAR(500) | NOT NULL | 文件访问URL |
| quality_score | DECIMAL(3,2) | NOT NULL, DEFAULT 0.0 | 照片质量评分 (0-1) |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

## ER图

```
+-------+      +---------+      +----------------+      +----------------+
|       |      |         |      |                |      |                |
| users |------| uploads |------| generation_    |------| generated_     |
|       |      |         |      | tasks          |      | photos         |
+-------+      +---------+      +----------------+      +----------------+
   |                |                  |                       |
   |                |                  |                       |
   v                v                  v                       v
 id (PK)          id (PK)            id (PK)                 id (PK)
 username         user_id (FK)       upload_id (FK)          task_id (FK)
 email            file_path          spec                    file_path
 password         file_size          background              file_url
 avatar           file_type          status                  quality_score
 credits          ...                ...                     ...
 ...
```

## 索引设计

为提高查询性能，以下是主要的索引设计：

1. 主键索引 (自动创建)
   - users.id
   - uploads.id
   - generation_tasks.id
   - generated_photos.id

2. 唯一索引
   - users.username
   - users.email

3. 外键索引 (自动创建)
   - uploads.user_id
   - generation_tasks.upload_id
   - generated_photos.task_id

4. 其他索引
   - generation_tasks.status (用于按状态查询任务)

## 数据关系

1. 用户与上传记录: 一对多关系
   - 一个用户可以有多个上传记录
   - 一个上传记录最多属于一个用户 (未登录用户的上传为NULL)

2. 上传记录与生成任务: 一对多关系
   - 一个上传记录可以有多个生成任务
   - 一个生成任务只属于一个上传记录

3. 生成任务与生成照片: 一对多关系
   - 一个任务可以生成多张照片
   - 一张生成的照片只属于一个任务 