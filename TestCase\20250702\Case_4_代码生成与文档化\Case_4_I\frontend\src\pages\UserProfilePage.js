import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Typography, Tabs, Card, List, Avatar, Tag, Button, Empty, Spin, Alert } from 'antd';
import { UserOutlined, HistoryOutlined, SettingOutlined, PictureOutlined } from '@ant-design/icons';
import { fetchUserProfile } from '../services/userSlice';
import './UserProfilePage.scss';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const UserProfilePage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentUser, isAuthenticated, loading, error } = useSelector((state) => state.user);
  
  const [historyData, setHistoryData] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  
  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (!isAuthenticated && !loading) {
      navigate('/login');
    }
  }, [isAuthenticated, loading, navigate]);
  
  // 获取用户信息
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchUserProfile());
      fetchHistory();
    }
  }, [isAuthenticated, dispatch]);
  
  // 模拟获取历史记录
  const fetchHistory = async () => {
    setHistoryLoading(true);
    // 这里应该是实际的API调用，这里使用模拟数据
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          date: '2024-07-01',
          type: '1寸证件照',
          count: 5,
          thumbnail: 'https://via.placeholder.com/100'
        },
        {
          id: '2',
          date: '2024-06-28',
          type: '2寸证件照',
          count: 3,
          thumbnail: 'https://via.placeholder.com/100'
        },
        {
          id: '3',
          date: '2024-06-25',
          type: '小2寸证件照',
          count: 2,
          thumbnail: 'https://via.placeholder.com/100'
        }
      ];
      setHistoryData(mockData);
      setHistoryLoading(false);
    }, 1000);
  };
  
  // 查看历史记录详情
  const viewHistoryDetail = (id) => {
    navigate(`/result/${id}`);
  };
  
  // 渲染用户信息
  const renderUserInfo = () => {
    if (!currentUser) return null;
    
    return (
      <Card className="profile-card">
        <div className="user-header">
          <Avatar 
            size={80} 
            icon={<UserOutlined />} 
            src={currentUser.avatar}
          />
          <div className="user-info">
            <Title level={3}>{currentUser.username}</Title>
            <Paragraph>{currentUser.email}</Paragraph>
            <Tag color="blue">普通用户</Tag>
          </div>
        </div>
        
        <div className="user-stats">
          <div className="stat-item">
            <div className="stat-value">{historyData.length}</div>
            <div className="stat-label">生成记录</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">100</div>
            <div className="stat-label">剩余次数</div>
          </div>
          <div className="stat-item">
            <div className="stat-value">0</div>
            <div className="stat-label">优惠券</div>
          </div>
        </div>
      </Card>
    );
  };
  
  // 渲染历史记录
  const renderHistory = () => {
    if (historyLoading) {
      return (
        <div className="loading-container">
          <Spin tip="加载中..." />
        </div>
      );
    }
    
    if (historyData.length === 0) {
      return (
        <Empty 
          description="暂无生成记录" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    return (
      <List
        itemLayout="horizontal"
        dataSource={historyData}
        renderItem={(item) => (
          <List.Item
            actions={[
              <Button 
                type="link" 
                onClick={() => viewHistoryDetail(item.id)}
              >
                查看
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar shape="square" size={64} src={item.thumbnail} />}
              title={`${item.type} (${item.count}张)`}
              description={`生成时间：${item.date}`}
            />
          </List.Item>
        )}
      />
    );
  };
  
  // 渲染账户设置
  const renderSettings = () => {
    return (
      <div className="settings-container">
        <Paragraph>账户设置功能正在开发中...</Paragraph>
      </div>
    );
  };
  
  if (loading) {
    return (
      <div className="profile-page loading-container">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="profile-page">
        <div className="container">
          <Alert
            message="获取用户信息失败"
            description={error}
            type="error"
            showIcon
          />
        </div>
      </div>
    );
  }
  
  if (!isAuthenticated) {
    return null; // 未登录时不渲染，会被重定向到登录页
  }
  
  return (
    <div className="profile-page">
      <div className="container">
        <Title level={2} className="page-title">个人中心</Title>
        
        {renderUserInfo()}
        
        <Card className="tabs-card">
          <Tabs defaultActiveKey="history">
            <TabPane 
              tab={<span><HistoryOutlined />生成记录</span>} 
              key="history"
            >
              {renderHistory()}
            </TabPane>
            <TabPane 
              tab={<span><SettingOutlined />账户设置</span>} 
              key="settings"
            >
              {renderSettings()}
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default UserProfilePage;