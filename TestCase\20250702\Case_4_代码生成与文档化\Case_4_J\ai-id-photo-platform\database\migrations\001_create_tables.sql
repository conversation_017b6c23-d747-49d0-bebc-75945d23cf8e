-- 迁移脚本：创建基础表结构
-- 版本：001
-- 描述：创建用户、上传、任务、照片等核心表

-- 检查数据库是否存在
CREATE DATABASE IF NOT EXISTS ai_photo_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE ai_photo_platform;

-- 创建迁移记录表
CREATE TABLE IF NOT EXISTS migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(20) NOT NULL,
    name VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_version (version)
) ENGINE=InnoDB COMMENT='数据库迁移记录表';

-- 检查是否已执行此迁移
SET @migration_exists = (SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_schema = 'ai_photo_platform' 
                        AND table_name = 'migrations');

-- 如果迁移表存在，检查此版本是否已执行
SET @version_exists = 0;
IF @migration_exists > 0 THEN
    SET @version_exists = (SELECT COUNT(*) FROM migrations WHERE version = '001');
END IF;

-- 只有在未执行过此迁移时才执行
SET @sql = IF(@version_exists = 0, 'SELECT 1', 'SELECT 0');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 如果需要执行迁移，则创建表
-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    avatar VARCHAR(500) NULL COMMENT '头像URL',
    credits INT DEFAULT 10 COMMENT '积分余额',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- 上传记录表
CREATE TABLE IF NOT EXISTS uploads (
    id VARCHAR(50) PRIMARY KEY COMMENT '上传ID',
    user_id INT NULL COMMENT '用户ID',
    original_filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(20) NOT NULL COMMENT '文件类型',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    metadata JSON NULL COMMENT '图片元数据',
    status ENUM('uploaded', 'processing', 'completed', 'failed', 'deleted') DEFAULT 'uploaded' COMMENT '状态',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_upload_time (upload_time),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='上传记录表';

-- 生成任务表
CREATE TABLE IF NOT EXISTS generation_tasks (
    id VARCHAR(50) PRIMARY KEY COMMENT '任务ID',
    upload_id VARCHAR(50) NOT NULL COMMENT '上传ID',
    spec VARCHAR(20) NOT NULL COMMENT '证件照规格',
    background VARCHAR(20) NOT NULL COMMENT '背景颜色',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '进度百分比',
    model_name VARCHAR(50) NULL COMMENT '使用的AI模型名称',
    model_config JSON NULL COMMENT '模型配置参数',
    custom_spec JSON NULL COMMENT '自定义规格参数',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_spec (spec),
    INDEX idx_background (background)
) ENGINE=InnoDB COMMENT='生成任务表';

-- 生成照片表
CREATE TABLE IF NOT EXISTS generated_photos (
    id VARCHAR(50) PRIMARY KEY COMMENT '照片ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    quality_score DECIMAL(3,2) NULL COMMENT '质量评分',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    width INT NOT NULL COMMENT '图片宽度',
    height INT NOT NULL COMMENT '图片高度',
    format VARCHAR(10) DEFAULT 'jpeg' COMMENT '图片格式',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    metadata JSON NULL COMMENT '生成照片的元数据',
    
    FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_quality_score (quality_score),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='生成照片表';

-- 记录迁移执行
INSERT IGNORE INTO migrations (version, name) VALUES ('001', 'create_tables');
