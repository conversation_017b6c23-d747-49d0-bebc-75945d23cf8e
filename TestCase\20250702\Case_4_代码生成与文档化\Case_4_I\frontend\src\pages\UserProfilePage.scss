.profile-page {
  padding: 40px 0;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px - 70px); // 减去header和footer的高度
  
  .page-title {
    margin-bottom: 24px;
  }
  
  .profile-card {
    margin-bottom: 24px;
    
    .user-header {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      
      .user-info {
        margin-left: 24px;
        
        h3 {
          margin-bottom: 4px;
        }
        
        p {
          margin-bottom: 8px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
    
    .user-stats {
      display: flex;
      justify-content: space-around;
      text-align: center;
      border-top: 1px solid #f0f0f0;
      padding-top: 24px;
      
      .stat-item {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #1890ff;
        }
        
        .stat-label {
          color: rgba(0, 0, 0, 0.65);
          margin-top: 4px;
        }
      }
    }
  }
  
  .tabs-card {
    .ant-tabs-nav {
      margin-bottom: 16px;
    }
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }
  
  .settings-container {
    padding: 24px 0;
    text-align: center;
  }
}

// 响应式样式
@media (max-width: 768px) {
  .profile-page {
    padding: 20px 16px;
    
    .profile-card {
      .user-header {
        flex-direction: column;
        text-align: center;
        
        .user-info {
          margin-left: 0;
          margin-top: 16px;
        }
      }
      
      .user-stats {
        flex-direction: column;
        
        .stat-item {
          margin-bottom: 16px;
        }
      }
    }
  }
}