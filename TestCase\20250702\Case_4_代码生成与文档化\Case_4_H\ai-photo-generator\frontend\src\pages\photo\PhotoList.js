import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Card, 
  Typography, 
  Input, 
  Button, 
  Select, 
  Empty, 
  Pagination, 
  Spin, 
  Tag, 
  Dropdown, 
  Menu, 
  Space,
  Tooltip,
  Modal
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  PlusOutlined,
  HeartOutlined,
  HeartFilled,
  DownloadOutlined,
  DeleteOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { 
  fetchPhotos, 
  toggleFavorite, 
  deletePhoto, 
  downloadPhoto 
} from '../../store/photoSlice';
import { ROUTES, PHOTO_STYLES } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { confirm } = Modal;

/**
 * 样式化组件
 */
const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-bottom: 24px;
`;

const FiltersContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const PhotoCard = styled(Card)`
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  height: 100%;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .ant-card-cover {
    height: 200px;
    overflow: hidden;
  }
  
  .ant-card-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
  }
  
  &:hover .ant-card-cover img {
    transform: scale(1.05);
  }
  
  .ant-card-actions {
    background: ${({ theme }) => theme.colorBgContainer};
  }
`;

const PhotoPrompt = styled(Paragraph)`
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const PhotoMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
`;

const EmptyContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  padding: 48px 0;
`;

/**
 * 照片列表页面组件
 * 
 * 显示用户生成的所有照片，并提供筛选、排序和搜索功能
 * 
 * @returns {JSX.Element} 照片列表页面组件
 */
const PhotoList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从Redux获取状态
  const { photos, totalPhotos, loading } = useSelector((state) => state.photo);
  
  // 本地状态
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [sortBy, setSortBy] = useState('createdAt_desc');
  const [filterStyle, setFilterStyle] = useState('');
  const [filterFavorites, setFilterFavorites] = useState(false);
  
  // 获取照片列表
  useEffect(() => {
    const fetchPhotoList = () => {
      const [sortField, sortOrder] = sortBy.split('_');
      
      dispatch(fetchPhotos({
        page: currentPage,
        limit: pageSize,
        search: searchTerm,
        sortBy: sortField,
        sortOrder,
        style: filterStyle,
        favorites: filterFavorites
      }));
    };
    
    fetchPhotoList();
  }, [dispatch, currentPage, pageSize, sortBy, filterStyle, filterFavorites, searchTerm]);
  
  // 处理搜索
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };
  
  // 处理排序变更
  const handleSortChange = (value) => {
    setSortBy(value);
    setCurrentPage(1);
  };
  
  // 处理风格筛选变更
  const handleStyleFilterChange = (value) => {
    setFilterStyle(value);
    setCurrentPage(1);
  };
  
  // 处理收藏筛选变更
  const handleFavoritesFilterChange = (checked) => {
    setFilterFavorites(checked);
    setCurrentPage(1);
  };
  
  // 处理页码变更
  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  
  // 处理收藏切换
  const handleToggleFavorite = (photoId, isFavorite) => {
    dispatch(toggleFavorite({ photoId, isFavorite: !isFavorite }));
  };
  
  // 处理照片下载
  const handleDownload = (photoId) => {
    dispatch(downloadPhoto(photoId));
  };
  
  // 处理照片删除
  const handleDelete = (photoId) => {
    confirm({
      title: '确定要删除这张照片吗？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后将无法恢复',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        dispatch(deletePhoto(photoId));
      }
    });
  };
  
  // 渲染照片卡片
  const renderPhotoCard = (photo) => {
    const menu = (
      <Menu>
        <Menu.Item 
          key="download" 
          icon={<DownloadOutlined />}
          onClick={() => handleDownload(photo.id)}
        >
          下载照片
        </Menu.Item>
        <Menu.Item 
          key="delete" 
          icon={<DeleteOutlined />} 
          danger
          onClick={() => handleDelete(photo.id)}
        >
          删除照片
        </Menu.Item>
      </Menu>
    );
    
    return (
      <Col xs={24} sm={12} md={8} lg={6} key={photo.id} style={{ marginBottom: 24 }}>
        <PhotoCard
          hoverable
          cover={
            <img 
              alt={photo.prompt} 
              src={photo.thumbnailUrl} 
              onClick={() => navigate(`${ROUTES.PHOTOS}/${photo.id}`)}
            />
          }
          actions={[
            <Tooltip title={photo.isFavorite ? "取消收藏" : "添加到收藏"}>
              {photo.isFavorite ? (
                <HeartFilled 
                  style={{ color: '#ff4d4f' }} 
                  onClick={() => handleToggleFavorite(photo.id, photo.isFavorite)}
                />
              ) : (
                <HeartOutlined onClick={() => handleToggleFavorite(photo.id, photo.isFavorite)} />
              )}
            </Tooltip>,
            <Tooltip title="下载照片">
              <DownloadOutlined onClick={() => handleDownload(photo.id)} />
            </Tooltip>,
            <Dropdown overlay={menu} placement="bottomRight">
              <EllipsisOutlined />
            </Dropdown>
          ]}
        >
          <div onClick={() => navigate(`${ROUTES.PHOTOS}/${photo.id}`)}>
            <PhotoPrompt ellipsis={{ rows: 2 }}>
              {photo.prompt}
            </PhotoPrompt>
            <PhotoMeta>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {new Date(photo.createdAt).toLocaleDateString()}
              </Text>
              {photo.style && (
                <Tag color="blue">{PHOTO_STYLES.find(s => s.value === photo.style)?.label || photo.style}</Tag>
              )}
            </PhotoMeta>
          </div>
        </PhotoCard>
      </Col>
    );
  };
  
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>我的照片</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => navigate(ROUTES.CREATE_PHOTO)}
        >
          创建新照片
        </Button>
      </div>
      
      {/* 筛选和搜索 */}
      <StyledCard>
        <FiltersContainer>
          <Input.Search
            placeholder="搜索照片描述"
            allowClear
            enterButton={<SearchOutlined />}
            onSearch={handleSearch}
            style={{ width: 250 }}
          />
          
          <Select
            placeholder="排序方式"
            style={{ width: 150 }}
            value={sortBy}
            onChange={handleSortChange}
          >
            <Option value="createdAt_desc">最新创建</Option>
            <Option value="createdAt_asc">最早创建</Option>
            <Option value="prompt_asc">描述 A-Z</Option>
            <Option value="prompt_desc">描述 Z-A</Option>
          </Select>
          
          <Select
            placeholder="照片风格"
            style={{ width: 150 }}
            value={filterStyle}
            onChange={handleStyleFilterChange}
            allowClear
          >
            {PHOTO_STYLES.map(style => (
              <Option key={style.value} value={style.value}>{style.label}</Option>
            ))}
          </Select>
          
          <Button
            type={filterFavorites ? 'primary' : 'default'}
            icon={filterFavorites ? <HeartFilled /> : <HeartOutlined />}
            onClick={() => handleFavoritesFilterChange(!filterFavorites)}
          >
            仅显示收藏
          </Button>
        </FiltersContainer>
      </StyledCard>
      
      {/* 照片列表 */}
      {loading && photos.length === 0 ? (
        <LoadingContainer>
          <Spin size="large" />
        </LoadingContainer>
      ) : photos.length > 0 ? (
        <>
          <Row gutter={16}>
            {photos.map(renderPhotoCard)}
          </Row>
          
          {/* 分页 */}
          <div style={{ display: 'flex', justifyContent: 'center', marginTop: 24 }}>
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={totalPhotos}
              onChange={handlePageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `共 ${total} 张照片`}
            />
          </div>
        </>
      ) : (
        <EmptyContainer>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span>
                {searchTerm || filterStyle || filterFavorites ? 
                  '没有找到符合条件的照片' : 
                  '您还没有创建任何照片'}
              </span>
            }
          >
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => navigate(ROUTES.CREATE_PHOTO)}
            >
              创建第一张照片
            </Button>
          </Empty>
        </EmptyContainer>
      )}
    </div>
  );
};

export default PhotoList;