import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// 生成证件照
export const generateIdPhoto = createAsyncThunk(
  'generation/generateIdPhoto',
  async ({ uploadId, spec, background, count, modelConfig }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.post(`${API_URL}/generate`, {
        upload_id: uploadId,
        spec,
        background,
        count,
        model_config: modelConfig
      }, {
        headers
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '生成失败' });
    }
  }
);

// 查询任务状态
export const checkTaskStatus = createAsyncThunk(
  'generation/checkTaskStatus',
  async (taskId, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.get(`${API_URL}/task/${taskId}`, {
        headers
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '查询任务状态失败' });
    }
  }
);

// 下载证件照
export const downloadPhoto = createAsyncThunk(
  'generation/downloadPhoto',
  async ({ photoId, fileName }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.get(`${API_URL}/download/${photoId}`, {
        headers,
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName || `photo_${photoId}.jpg`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      return { photoId, success: true };
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '下载失败' });
    }
  }
);

// 批量下载证件照
export const downloadMultiplePhotos = createAsyncThunk(
  'generation/downloadMultiplePhotos',
  async ({ photoIds }, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.post(`${API_URL}/download/batch`, {
        photo_ids: photoIds
      }, {
        headers,
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `photos_${Date.now()}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      return { success: true };
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '批量下载失败' });
    }
  }
);

const generationSlice = createSlice({
  name: 'generation',
  initialState: {
    currentTask: null,
    taskStatus: null,
    generatedPhotos: [],
    selectedPhotos: [],
    loading: false,
    error: null,
    downloadLoading: false,
    downloadError: null,
  },
  reducers: {
    selectPhoto: (state, action) => {
      const photoId = action.payload;
      if (!state.selectedPhotos.includes(photoId)) {
        state.selectedPhotos.push(photoId);
      }
    },
    unselectPhoto: (state, action) => {
      const photoId = action.payload;
      state.selectedPhotos = state.selectedPhotos.filter(id => id !== photoId);
    },
    selectAllPhotos: (state) => {
      state.selectedPhotos = state.generatedPhotos.map(photo => photo.id);
    },
    unselectAllPhotos: (state) => {
      state.selectedPhotos = [];
    },
    clearGenerationError: (state) => {
      state.error = null;
    },
    clearDownloadError: (state) => {
      state.downloadError = null;
    },
    resetGeneration: (state) => {
      state.currentTask = null;
      state.taskStatus = null;
      state.generatedPhotos = [];
      state.selectedPhotos = [];
      state.error = null;
      state.downloadError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 生成证件照
      .addCase(generateIdPhoto.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateIdPhoto.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTask = action.payload.data.task_id;
        state.taskStatus = action.payload.data.status;
      })
      .addCase(generateIdPhoto.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || '生成证件照失败';
      })
      // 查询任务状态
      .addCase(checkTaskStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(checkTaskStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.taskStatus = action.payload.data.status;
        if (action.payload.data.status === 'completed') {
          state.generatedPhotos = action.payload.data.generated_photos;
        }
      })
      .addCase(checkTaskStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || '查询任务状态失败';
      })
      // 下载证件照
      .addCase(downloadPhoto.pending, (state) => {
        state.downloadLoading = true;
        state.downloadError = null;
      })
      .addCase(downloadPhoto.fulfilled, (state) => {
        state.downloadLoading = false;
      })
      .addCase(downloadPhoto.rejected, (state, action) => {
        state.downloadLoading = false;
        state.downloadError = action.payload?.message || '下载失败';
      })
      // 批量下载证件照
      .addCase(downloadMultiplePhotos.pending, (state) => {
        state.downloadLoading = true;
        state.downloadError = null;
      })
      .addCase(downloadMultiplePhotos.fulfilled, (state) => {
        state.downloadLoading = false;
      })
      .addCase(downloadMultiplePhotos.rejected, (state, action) => {
        state.downloadLoading = false;
        state.downloadError = action.payload?.message || '批量下载失败';
      });
  },
});

export const {
  selectPhoto,
  unselectPhoto,
  selectAllPhotos,
  unselectAllPhotos,
  clearGenerationError,
  clearDownloadError,
  resetGeneration
} = generationSlice.actions;

export default generationSlice.reducer;