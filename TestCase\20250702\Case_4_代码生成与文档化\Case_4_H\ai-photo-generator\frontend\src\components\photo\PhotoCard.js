import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { 
  Card, 
  Typography, 
  Space, 
  Tag, 
  Button, 
  Dropdown, 
  Checkbox, 
  Tooltip,
  Modal,
  message
} from 'antd';
import { 
  EditOutlined, 
  DownloadOutlined, 
  Ellip<PERSON>Outlined, 
  DeleteOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { downloadPhoto, deletePhoto, toggleFavorite } from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS } from '../../utils/constants';
import ShareModal from './ShareModal';

const { Text, Paragraph } = Typography;
const { confirm } = Modal;

/**
 * 照片卡片组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.photo - 照片对象
 * @param {boolean} props.selectable - 是否可选择
 * @param {boolean} props.selected - 是否已选择
 * @param {Function} props.onSelect - 选择回调
 * @param {Function} props.onDelete - 删除回调
 * @returns {React.ReactNode} 渲染的组件
 */
const PhotoCard = ({ photo, selectable = false, selected = false, onSelect, onDelete }) => {
  const dispatch = useDispatch();
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  if (!photo) return null;
  
  // 获取照片规格信息
  const specInfo = PHOTO_SPECS[photo.spec] || { name: '未知规格', width: '-', height: '-' };
  
  /**
   * 处理下载照片
   * 
   * @param {Event} e - 事件对象
   */
  const handleDownload = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    setDownloadLoading(true);
    try {
      await dispatch(downloadPhoto(photo.id)).unwrap();
      message.success('照片下载成功');
    } catch (err) {
      message.error('下载失败: ' + err.message);
    } finally {
      setDownloadLoading(false);
    }
  };
  
  /**
   * 处理删除照片
   * 
   * @param {Event} e - 事件对象
   */
  const handleDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这张照片吗？此操作无法撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deletePhoto(photo.id)).unwrap();
          message.success('照片删除成功');
          if (onDelete) {
            onDelete(photo.id);
          }
        } catch (err) {
          message.error('删除失败: ' + err.message);
        }
      },
    });
  };
  
  /**
   * 处理收藏/取消收藏
   * 
   * @param {Event} e - 事件对象
   */
  const handleToggleFavorite = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      await dispatch(toggleFavorite(photo.id)).unwrap();
      message.success(photo.isFavorite ? '已取消收藏' : '已添加到收藏');
    } catch (err) {
      message.error('操作失败: ' + err.message);
    }
  };
  
  /**
   * 处理分享
   * 
   * @param {Event} e - 事件对象
   */
  const handleShare = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShareModalVisible(true);
  };
  
  /**
   * 处理选择
   * 
   * @param {Event} e - 事件对象
   */
  const handleSelect = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onSelect) {
      onSelect(!selected);
    }
  };
  
  // 下拉菜单项
  const dropdownItems = [
    {
      key: 'edit',
      label: (
        <Link to={`${ROUTES.EDIT_PHOTO}/${photo.id}`}>
          <EditOutlined /> 编辑照片
        </Link>
      ),
    },
    {
      key: 'download',
      label: (
        <a onClick={handleDownload}>
          <DownloadOutlined /> 下载照片
        </a>
      ),
    },
    {
      key: 'share',
      label: (
        <a onClick={handleShare}>
          <ShareAltOutlined /> 分享照片
        </a>
      ),
    },
    {
      key: 'favorite',
      label: (
        <a onClick={handleToggleFavorite}>
          {photo.isFavorite ? (
            <>
              <StarFilled style={{ color: '#faad14' }} /> 取消收藏
            </>
          ) : (
            <>
              <StarOutlined /> 添加收藏
            </>
          )}
        </a>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      danger: true,
      label: (
        <a onClick={handleDelete}>
          <DeleteOutlined /> 删除照片
        </a>
      ),
    },
  ];
  
  return (
    <div className="photo-card">
      <Card
        hoverable
        cover={
          <div className="photo-card-cover">
            {selectable && (
              <div className="photo-card-checkbox" onClick={handleSelect}>
                <Checkbox checked={selected} onChange={(e) => onSelect(e.target.checked)} />
              </div>
            )}
            
            {photo.isFavorite && (
              <div className="photo-card-favorite">
                <StarFilled style={{ color: '#faad14' }} />
              </div>
            )}
            
            <Link to={`${ROUTES.PHOTO_RESULT}/${photo.id}`}>
              <img 
                src={photo.resultUrl || photo.thumbnailUrl} 
                alt={photo.name || '证件照'} 
                className="photo-card-image"
              />
            </Link>
          </div>
        }
        actions={[
          <Tooltip title="编辑" key="edit">
            <Link to={`${ROUTES.EDIT_PHOTO}/${photo.id}`}>
              <EditOutlined />
            </Link>
          </Tooltip>,
          <Tooltip title="下载" key="download">
            <DownloadOutlined onClick={handleDownload} />
          </Tooltip>,
          <Dropdown menu={{ items: dropdownItems }} placement="bottomRight" key="more">
            <EllipsisOutlined />
          </Dropdown>,
        ]}
      >
        <div className="photo-card-content">
          <div className="photo-card-title">
            <Paragraph ellipsis={{ rows: 1 }} strong>
              <Link to={`${ROUTES.PHOTO_RESULT}/${photo.id}`}>
                {photo.name || '未命名照片'}
              </Link>
            </Paragraph>
          </div>
          
          <Space size={[0, 4]} wrap>
            <Tag>{specInfo.name}</Tag>
            <Tag color={photo.status === 'completed' ? 'success' : 'processing'}>
              {photo.status === 'completed' ? '已完成' : '处理中'}
            </Tag>
          </Space>
          
          <div className="photo-card-date">
            <Text type="secondary">
              {new Date(photo.createdAt).toLocaleDateString()}
            </Text>
          </div>
        </div>
      </Card>
      
      {/* 分享模态框 */}
      <ShareModal
        visible={shareModalVisible}
        photo={photo}
        onClose={() => setShareModalVisible(false)}
      />
    </div>
  );
};

PhotoCard.propTypes = {
  photo: PropTypes.object.isRequired,
  selectable: PropTypes.bool,
  selected: PropTypes.bool,
  onSelect: PropTypes.func,
  onDelete: PropTypes.func,
};

export default PhotoCard;