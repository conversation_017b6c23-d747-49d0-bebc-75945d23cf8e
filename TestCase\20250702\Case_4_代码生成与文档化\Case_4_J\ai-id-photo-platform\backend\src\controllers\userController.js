const { User } = require('../models');
const { generateToken, generateRefreshToken } = require('../middleware/auth');
const logger = require('../utils/logger');

class UserController {
  /**
   * 用户注册
   */
  async register(req, res, next) {
    try {
      const { username, email, password } = req.body;

      // 检查用户是否已存在
      const existingUser = await User.findOne({
        where: {
          $or: [
            { email: email },
            { username: username }
          ]
        }
      });

      if (existingUser) {
        return res.status(409).json({
          code: 409,
          message: '用户已存在',
          errors: existingUser.email === email ? ['邮箱已被注册'] : ['用户名已被使用']
        });
      }

      // 创建新用户
      const user = await User.create({
        username,
        email,
        passwordHash: password, // 会在模型的beforeCreate钩子中自动加密
        credits: 10 // 新用户赠送10个积分
      });

      // 生成令牌
      const token = generateToken(user.id);
      const refreshToken = generateRefreshToken(user.id);

      logger.info('用户注册成功', {
        userId: user.id,
        username: user.username,
        email: user.email
      });

      res.status(201).json({
        code: 201,
        message: '注册成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            credits: user.credits,
            createdAt: user.createdAt
          },
          token,
          refreshToken
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户登录
   */
  async login(req, res, next) {
    try {
      const { email, password } = req.body;

      // 查找用户
      const user = await User.findOne({ where: { email } });
      if (!user) {
        return res.status(401).json({
          code: 401,
          message: '登录失败',
          errors: ['邮箱或密码错误']
        });
      }

      // 验证密码
      const isValidPassword = await user.validatePassword(password);
      if (!isValidPassword) {
        return res.status(401).json({
          code: 401,
          message: '登录失败',
          errors: ['邮箱或密码错误']
        });
      }

      // 检查用户状态
      if (!user.isActive) {
        return res.status(401).json({
          code: 401,
          message: '账户已被禁用',
          errors: ['请联系管理员']
        });
      }

      // 更新最后登录时间
      await user.update({ lastLoginAt: new Date() });

      // 生成令牌
      const token = generateToken(user.id);
      const refreshToken = generateRefreshToken(user.id);

      logger.info('用户登录成功', {
        userId: user.id,
        username: user.username,
        email: user.email
      });

      res.json({
        code: 200,
        message: '登录成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            credits: user.credits,
            lastLoginAt: user.lastLoginAt
          },
          token,
          refreshToken
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户信息
   */
  async getProfile(req, res, next) {
    try {
      const user = req.user;

      res.json({
        code: 200,
        message: '获取用户信息成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            avatar: user.avatar,
            credits: user.credits,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
          }
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(req, res, next) {
    try {
      const user = req.user;
      const { username, avatar } = req.body;

      const updateData = {};
      
      // 检查用户名是否需要更新
      if (username && username !== user.username) {
        // 检查用户名是否已被使用
        const existingUser = await User.findOne({
          where: { 
            username: username,
            id: { $ne: user.id }
          }
        });

        if (existingUser) {
          return res.status(409).json({
            code: 409,
            message: '用户名已被使用',
            errors: ['请选择其他用户名']
          });
        }

        updateData.username = username;
      }

      // 更新头像
      if (avatar !== undefined) {
        updateData.avatar = avatar;
      }

      // 如果有更新数据，执行更新
      if (Object.keys(updateData).length > 0) {
        await user.update(updateData);
      }

      logger.info('用户信息更新成功', {
        userId: user.id,
        updateData
      });

      res.json({
        code: 200,
        message: '用户信息更新成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            avatar: user.avatar,
            credits: user.credits,
            updatedAt: user.updatedAt
          }
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req, res, next) {
    try {
      const user = req.user;
      const { currentPassword, newPassword } = req.body;

      // 验证当前密码
      const isValidPassword = await user.validatePassword(currentPassword);
      if (!isValidPassword) {
        return res.status(400).json({
          code: 400,
          message: '当前密码错误',
          errors: ['请输入正确的当前密码']
        });
      }

      // 更新密码
      await user.update({ passwordHash: newPassword });

      logger.info('用户密码修改成功', {
        userId: user.id,
        username: user.username
      });

      res.json({
        code: 200,
        message: '密码修改成功',
        data: null
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户使用统计
   */
  async getUserStats(req, res, next) {
    try {
      const user = req.user;

      // 这里可以添加更多统计信息的查询
      // 例如：上传次数、生成次数、下载次数等

      const stats = {
        credits: user.credits,
        memberSince: user.createdAt,
        lastLogin: user.lastLoginAt,
        // 可以添加更多统计数据
        totalUploads: 0,
        totalGenerations: 0,
        totalDownloads: 0
      };

      res.json({
        code: 200,
        message: '获取用户统计成功',
        data: { stats }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 注销登录
   */
  async logout(req, res, next) {
    try {
      // 在实际应用中，这里可能需要将token加入黑名单
      // 或者在Redis中记录已注销的token

      logger.info('用户注销成功', {
        userId: req.user?.id,
        username: req.user?.username
      });

      res.json({
        code: 200,
        message: '注销成功',
        data: null
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new UserController();
