const { getUserInfo, updateUserInfo } = require('../services/userService');

// 获取用户信息
const getUser = async (req, res) => {
  try {
    // 从认证中间件获取用户ID
    const userId = req.user.id;
    
    // 获取用户信息
    const result = await getUserInfo(userId);
    
    if (!result.success) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在',
        error: result.error
      });
    }
    
    res.status(200).json({
      code: 200,
      data: {
        user: result.user
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 更新用户信息
const updateUser = async (req, res) => {
  try {
    // 从认证中间件获取用户ID
    const userId = req.user.id;
    
    // 从请求体获取需要更新的数据
    const { username, avatar } = req.body;
    
    // 构建更新数据对象
    const updateData = {};
    if (username) updateData.username = username;
    if (avatar) updateData.avatar = avatar;
    
    // 执行更新
    const result = await updateUserInfo(userId, updateData);
    
    if (!result.success) {
      return res.status(400).json({
        code: 400,
        message: '更新失败',
        error: result.error
      });
    }
    
    res.status(200).json({
      code: 200,
      message: '更新成功',
      data: {
        user: result.user
      }
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 获取使用历史
const getUserHistory = async (req, res) => {
  try {
    // 从认证中间件获取用户
    const user = req.user;
    
    // 获取用户的上传记录和生成任务
    const uploads = await user.getUploads({
      include: 'tasks',
      limit: 10,
      order: [['createdAt', 'DESC']]
    });
    
    // 转换为前端需要的格式
    const history = uploads.map(upload => ({
      id: upload.id,
      date: upload.createdAt,
      file_name: upload.originalFilename,
      file_size: upload.fileSize,
      tasks: upload.tasks.map(task => ({
        id: task.id,
        spec: task.spec,
        background: task.background,
        status: task.status,
        created_at: task.createdAt,
        completed_at: task.completedAt
      }))
    }));
    
    res.status(200).json({
      code: 200,
      data: {
        history
      }
    });
  } catch (error) {
    console.error('获取用户历史记录错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

module.exports = {
  getUser,
  updateUser,
  getUserHistory
}; 