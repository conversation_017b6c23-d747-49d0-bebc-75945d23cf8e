import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import taskService from '@/services/taskService'

// 异步actions
export const createGenerationTask = createAsyncThunk(
  'task/createGenerationTask',
  async (taskData, { rejectWithValue }) => {
    try {
      const response = await taskService.createGenerationTask(taskData)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '创建生成任务失败' })
    }
  }
)

export const getTaskStatus = createAsyncThunk(
  'task/getTaskStatus',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await taskService.getTaskStatus(taskId)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '获取任务状态失败' })
    }
  }
)

export const getTaskDetail = createAsyncThunk(
  'task/getTaskDetail',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await taskService.getTaskDetail(taskId)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '获取任务详情失败' })
    }
  }
)

export const getUserTasks = createAsyncThunk(
  'task/getUserTasks',
  async (params, { rejectWithValue }) => {
    try {
      const response = await taskService.getUserTasks(params)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '获取用户任务列表失败' })
    }
  }
)

export const deleteTask = createAsyncThunk(
  'task/deleteTask',
  async (taskId, { rejectWithValue }) => {
    try {
      await taskService.deleteTask(taskId)
      return taskId
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '删除任务失败' })
    }
  }
)

export const downloadPhoto = createAsyncThunk(
  'task/downloadPhoto',
  async (photoId, { rejectWithValue }) => {
    try {
      const response = await taskService.downloadPhoto(photoId)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '下载照片失败' })
    }
  }
)

export const downloadTaskPhotos = createAsyncThunk(
  'task/downloadTaskPhotos',
  async (taskId, { rejectWithValue }) => {
    try {
      const response = await taskService.downloadTaskPhotos(taskId)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '批量下载失败' })
    }
  }
)

// 初始状态
const initialState = {
  tasks: [],
  currentTask: null,
  taskDetail: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10
  },
  loading: false,
  creating: false,
  downloading: false,
  error: null,
  pollingTaskId: null // 用于轮询的任务ID
}

// 创建slice
const taskSlice = createSlice({
  name: 'task',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearTasks: (state) => {
      state.tasks = []
      state.currentTask = null
      state.taskDetail = null
    },
    setPollingTaskId: (state, action) => {
      state.pollingTaskId = action.payload
    },
    clearPollingTaskId: (state) => {
      state.pollingTaskId = null
    },
    updateTaskInList: (state, action) => {
      const { taskId, updates } = action.payload
      const taskIndex = state.tasks.findIndex(task => task.taskId === taskId)
      if (taskIndex >= 0) {
        state.tasks[taskIndex] = { ...state.tasks[taskIndex], ...updates }
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // 创建生成任务
      .addCase(createGenerationTask.pending, (state) => {
        state.creating = true
        state.error = null
      })
      .addCase(createGenerationTask.fulfilled, (state, action) => {
        state.creating = false
        state.currentTask = action.payload
        state.error = null
      })
      .addCase(createGenerationTask.rejected, (state, action) => {
        state.creating = false
        state.error = action.payload
      })
      
      // 获取任务状态
      .addCase(getTaskStatus.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getTaskStatus.fulfilled, (state, action) => {
        state.loading = false
        state.currentTask = action.payload
        state.error = null
        
        // 更新任务列表中的对应任务
        const taskIndex = state.tasks.findIndex(
          task => task.taskId === action.payload.taskId
        )
        if (taskIndex >= 0) {
          state.tasks[taskIndex] = { ...state.tasks[taskIndex], ...action.payload }
        }
      })
      .addCase(getTaskStatus.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // 获取任务详情
      .addCase(getTaskDetail.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getTaskDetail.fulfilled, (state, action) => {
        state.loading = false
        state.taskDetail = action.payload
        state.error = null
      })
      .addCase(getTaskDetail.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // 获取用户任务列表
      .addCase(getUserTasks.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(getUserTasks.fulfilled, (state, action) => {
        state.loading = false
        state.tasks = action.payload.tasks
        state.pagination = action.payload.pagination
        state.error = null
      })
      .addCase(getUserTasks.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // 删除任务
      .addCase(deleteTask.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.loading = false
        state.tasks = state.tasks.filter(task => task.taskId !== action.payload)
        if (state.currentTask?.taskId === action.payload) {
          state.currentTask = null
        }
        if (state.taskDetail?.taskId === action.payload) {
          state.taskDetail = null
        }
        state.error = null
      })
      .addCase(deleteTask.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // 下载照片
      .addCase(downloadPhoto.pending, (state) => {
        state.downloading = true
        state.error = null
      })
      .addCase(downloadPhoto.fulfilled, (state) => {
        state.downloading = false
        state.error = null
      })
      .addCase(downloadPhoto.rejected, (state, action) => {
        state.downloading = false
        state.error = action.payload
      })
      
      // 批量下载
      .addCase(downloadTaskPhotos.pending, (state) => {
        state.downloading = true
        state.error = null
      })
      .addCase(downloadTaskPhotos.fulfilled, (state) => {
        state.downloading = false
        state.error = null
      })
      .addCase(downloadTaskPhotos.rejected, (state, action) => {
        state.downloading = false
        state.error = action.payload
      })
  }
})

export const { 
  clearError, 
  clearTasks, 
  setPollingTaskId, 
  clearPollingTaskId,
  updateTaskInList
} = taskSlice.actions

export default taskSlice.reducer
