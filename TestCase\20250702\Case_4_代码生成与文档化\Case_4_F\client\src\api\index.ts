import axios from 'axios';

const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? '/api' 
  : 'http://localhost:3000/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器 - 添加认证信息
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并重定向到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 上传相关API
export const uploadAPI = {
  // 上传照片
  uploadPhoto: (file: File, onProgress?: (progress: number) => void) => {
    const formData = new FormData();
    formData.append('file', file);
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      }
    });
  }
};

// 证件照生成相关API
export const photoAPI = {
  // 生成证件照
  generatePhotos: (uploadId: string, spec: string, background: string) => {
    return api.post('/generate', { 
      upload_id: uploadId, 
      spec, 
      background, 
      count: 5 
    });
  },
  
  // 获取任务状态
  getTaskStatus: (taskId: string) => {
    return api.get(`/task/${taskId}`);
  },
  
  // 下载证件照
  downloadPhotos: (photoIds: string[]) => {
    return api.post('/download', { photo_ids: photoIds }, { responseType: 'blob' });
  }
};

// 用户相关API
export const userAPI = {
  // 用户登录
  login: (email: string, password: string) => {
    return api.post('/auth/login', { email, password });
  },
  
  // 用户注册
  register: (username: string, email: string, password: string) => {
    return api.post('/auth/register', { username, email, password });
  },
  
  // 获取用户信息
  getUserInfo: () => {
    return api.get('/user/info');
  },
  
  // 更新用户信息
  updateUserInfo: (userData: any) => {
    return api.put('/user/update', userData);
  }
};

export default api; 