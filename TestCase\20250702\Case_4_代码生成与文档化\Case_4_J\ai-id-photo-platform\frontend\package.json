{"name": "ai-id-photo-frontend", "version": "1.0.0", "description": "AI证件照生成平台前端应用", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "antd": "^5.8.4", "@ant-design/icons": "^5.2.5", "axios": "^1.4.0", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "redux-persist": "^6.0.0", "react-dropzone": "^14.2.3", "dayjs": "^1.11.9", "classnames": "^2.3.2", "lodash": "^4.17.21", "file-saver": "^2.0.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/lodash": "^4.14.195", "@types/file-saver": "^2.0.5", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "typescript": "^5.0.2", "sass": "^1.64.1"}, "engines": {"node": ">=16.0.0"}}