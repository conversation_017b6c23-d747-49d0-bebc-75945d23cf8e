# AI证件照生成平台部署文档

## 概述

本文档详细说明了AI证件照生成平台的部署流程，包括环境要求、配置说明、部署步骤等。

## 系统要求

### 硬件要求

**最低配置:**
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 50GB 可用空间
- 网络: 100Mbps

**推荐配置:**
- CPU: 4核心或更多
- 内存: 8GB RAM或更多
- 存储: 100GB SSD
- 网络: 1Gbps

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+ (可选，用于反向代理)
- **Docker**: 20.10+ (可选，用于容器化部署)

## 环境准备

### 1. 安装Node.js

```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 安装MySQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# CentOS/RHEL
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 3. 安装Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### 4. 安装Nginx (可选)

```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 项目部署

### 1. 获取项目代码

```bash
# 克隆项目
git clone https://github.com/your-repo/ai-id-photo-platform.git
cd ai-id-photo-platform

# 或者下载并解压项目包
wget https://github.com/your-repo/ai-id-photo-platform/archive/main.zip
unzip main.zip
cd ai-id-photo-platform-main
```

### 2. 后端部署

```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
nano .env
```

**环境配置说明 (.env):**

```bash
# 服务器配置
NODE_ENV=production
PORT=3000
FRONTEND_URL=http://your-domain.com

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ai_photo_platform
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=30d

# AI模型配置
DEFAULT_AI_ENDPOINT=https://your-ai-api.com/generate
DEFAULT_AI_API_KEY=your-ai-api-key
CUSTOM_AI_ENDPOINT=https://custom-ai-api.com/process
CUSTOM_AI_TOKEN=your-custom-ai-token

# 生产环境配置
USE_MOCK_AI=false
LOG_LEVEL=info
```

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ai_photo_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'ai_photo_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ai_photo_platform.* TO 'ai_photo_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 初始化数据库
mysql -u ai_photo_user -p ai_photo_platform < ../database/init.sql

# 创建必要目录
mkdir -p uploads/temp uploads/original uploads/generated logs

# 设置目录权限
chmod 755 uploads
chmod 755 logs

# 启动后端服务
npm start

# 或使用PM2管理进程
npm install -g pm2
pm2 start src/app.js --name "ai-photo-backend"
pm2 save
pm2 startup
```

### 3. 前端部署

```bash
# 进入前端目录
cd ../frontend

# 安装依赖
npm install

# 创建环境配置文件
echo "VITE_API_BASE_URL=http://your-domain.com/api" > .env.production

# 构建生产版本
npm run build

# 部署到Web服务器
sudo cp -r dist/* /var/www/html/

# 或者使用Nginx配置
sudo cp -r dist /var/www/ai-photo-platform
```

### 4. Nginx配置

创建Nginx配置文件 `/etc/nginx/sites-available/ai-photo-platform`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/ai-photo-platform;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 文件上传大小限制
        client_max_body_size 10M;
    }

    # 上传文件访问
    location /uploads/ {
        proxy_pass http://localhost:3000/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

启用配置:

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/ai-photo-platform /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## Docker部署

### 1. 使用Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ai-photo-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_photo_platform
      MYSQL_USER: ai_photo_user
      MYSQL_PASSWORD: userpassword
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped

  redis:
    image: redis:6.2-alpine
    container_name: ai-photo-redis
    command: redis-server --requirepass redispassword
    ports:
      - "6379:6379"
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-photo-backend
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_USER: ai_photo_user
      DB_PASSWORD: userpassword
      REDIS_HOST: redis
      REDIS_PASSWORD: redispassword
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "3000:3000"
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-photo-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
```

### 2. 创建Dockerfile

**后端Dockerfile** (`backend/Dockerfile`):

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 创建必要目录
RUN mkdir -p uploads/temp uploads/original uploads/generated logs

# 设置权限
RUN chown -R node:node /app
USER node

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "start"]
```

**前端Dockerfile** (`frontend/Dockerfile`):

```dockerfile
# 构建阶段
FROM node:18-alpine as builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
```

### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

## SSL证书配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 日志管理

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/ai-photo-platform

/var/log/ai-photo-platform/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 2. 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 使用PM2监控
pm2 monit

# 设置告警
pm2 install pm2-server-monit
```

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
cat > /home/<USER>/mysql_backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/mysql"
mkdir -p $BACKUP_DIR

mysqldump -u ai_photo_user -p'userpassword' ai_photo_platform > $BACKUP_DIR/ai_photo_platform_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
EOF

chmod +x /home/<USER>/mysql_backup.sh

# 设置定时备份
crontab -e
# 添加：每天凌晨2点备份
0 2 * * * /home/<USER>/mysql_backup.sh
```

### 2. 文件备份

```bash
# 创建文件备份脚本
cat > /home/<USER>/files_backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/files"
mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz /path/to/uploads

# 保留最近7天的文件备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /home/<USER>/files_backup.sh
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo netstat -tulpn | grep :3000
   sudo kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   sudo systemctl status mysql
   
   # 检查防火墙
   sudo ufw status
   ```

3. **文件上传失败**
   ```bash
   # 检查目录权限
   ls -la uploads/
   sudo chown -R www-data:www-data uploads/
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 添加交换空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 性能优化

1. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_upload_time ON uploads(upload_time);
   CREATE INDEX idx_task_status ON generation_tasks(status);
   
   -- 优化配置
   -- 编辑 /etc/mysql/mysql.conf.d/mysqld.cnf
   innodb_buffer_pool_size = 1G
   query_cache_size = 256M
   ```

2. **Redis优化**
   ```bash
   # 编辑 /etc/redis/redis.conf
   maxmemory 512mb
   maxmemory-policy allkeys-lru
   ```

3. **Nginx优化**
   ```nginx
   # 启用gzip压缩
   gzip on;
   gzip_types text/plain text/css application/json application/javascript;
   
   # 启用缓存
   location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 安全建议

1. **防火墙配置**
   ```bash
   sudo ufw enable
   sudo ufw allow ssh
   sudo ufw allow 80
   sudo ufw allow 443
   ```

2. **定期更新**
   ```bash
   sudo apt update && sudo apt upgrade
   npm audit fix
   ```

3. **访问控制**
   - 使用强密码
   - 定期更换密钥
   - 限制管理员访问
   - 启用二次验证

4. **数据加密**
   - 使用HTTPS
   - 加密敏感数据
   - 定期备份验证
