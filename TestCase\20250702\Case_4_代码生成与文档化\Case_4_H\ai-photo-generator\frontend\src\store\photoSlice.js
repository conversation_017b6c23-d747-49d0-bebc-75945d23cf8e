import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { message } from 'antd';
import api from '../api';
import { API_ENDPOINTS, PAGINATION, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

/**
 * 初始状态
 */
const initialState = {
  photos: [],
  photo: null,
  favorites: [],
  generatedPhoto: null,
  loading: false,
  generating: false,
  error: null,
  pagination: {
    current: PAGINATION.DEFAULT_PAGE,
    pageSize: PAGINATION.DEFAULT_PAGE_SIZE,
    total: 0,
  },
  filters: {
    search: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    status: null,
  },
};

/**
 * 获取照片列表异步action
 */
export const fetchPhotos = createAsyncThunk(
  'photo/fetchPhotos',
  async (params, { rejectWithValue, getState }) => {
    try {
      const { pagination, filters } = getState().photo;
      const queryParams = {
        page: params?.page || pagination.current,
        limit: params?.limit || pagination.pageSize,
        search: params?.search || filters.search,
        sortBy: params?.sortBy || filters.sortBy,
        sortOrder: params?.sortOrder || filters.sortOrder,
        status: params?.status || filters.status,
      };
      
      const response = await api.get(API_ENDPOINTS.PHOTOS.LIST, { params: queryParams });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 获取照片详情异步action
 */
export const fetchPhotoDetail = createAsyncThunk(
  'photo/fetchPhotoDetail',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.get(API_ENDPOINTS.PHOTOS.DETAIL.replace(':id', id));
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 创建照片异步action
 */
export const createPhoto = createAsyncThunk(
  'photo/createPhoto',
  async (photoData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.PHOTOS.CREATE, photoData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 更新照片异步action
 */
export const updatePhoto = createAsyncThunk(
  'photo/updatePhoto',
  async ({ id, photoData }, { rejectWithValue }) => {
    try {
      const response = await api.put(API_ENDPOINTS.PHOTOS.UPDATE.replace(':id', id), photoData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 删除照片异步action
 */
export const deletePhoto = createAsyncThunk(
  'photo/deletePhoto',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(API_ENDPOINTS.PHOTOS.DELETE.replace(':id', id));
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 生成照片异步action
 */
export const generatePhoto = createAsyncThunk(
  'photo/generatePhoto',
  async (generationData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.PHOTOS.GENERATE, generationData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 获取收藏照片列表异步action
 */
export const fetchFavorites = createAsyncThunk(
  'photo/fetchFavorites',
  async (params, { rejectWithValue }) => {
    try {
      const queryParams = {
        page: params?.page || PAGINATION.DEFAULT_PAGE,
        limit: params?.limit || PAGINATION.DEFAULT_PAGE_SIZE,
      };
      
      const response = await api.get(API_ENDPOINTS.PHOTOS.FAVORITES, { params: queryParams });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 添加收藏异步action
 */
export const addFavorite = createAsyncThunk(
  'photo/addFavorite',
  async (id, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.PHOTOS.ADD_FAVORITE.replace(':id', id));
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 移除收藏异步action
 */
export const removeFavorite = createAsyncThunk(
  'photo/removeFavorite',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(API_ENDPOINTS.PHOTOS.REMOVE_FAVORITE.replace(':id', id));
      return id;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 照片slice
 */
const photoSlice = createSlice({
  name: 'photo',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    resetFilters: (state) => {
      state.filters = {
        search: '',
        sortBy: 'createdAt',
        sortOrder: 'desc',
        status: null,
      };
    },
    clearGeneratedPhoto: (state) => {
      state.generatedPhoto = null;
    },
    clearPhotoDetail: (state) => {
      state.photo = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取照片列表
      .addCase(fetchPhotos.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPhotos.fulfilled, (state, action) => {
        state.loading = false;
        state.photos = action.payload.photos;
        state.pagination = {
          current: action.payload.page,
          pageSize: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchPhotos.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 获取照片详情
      .addCase(fetchPhotoDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPhotoDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.photo = action.payload.photo;
      })
      .addCase(fetchPhotoDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 创建照片
      .addCase(createPhoto.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPhoto.fulfilled, (state, action) => {
        state.loading = false;
        state.photos = [action.payload.photo, ...state.photos];
        message.success(SUCCESS_MESSAGES.PHOTO_CREATE_SUCCESS);
      })
      .addCase(createPhoto.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 更新照片
      .addCase(updatePhoto.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePhoto.fulfilled, (state, action) => {
        state.loading = false;
        state.photo = action.payload.photo;
        state.photos = state.photos.map(photo => 
          photo.id === action.payload.photo.id ? action.payload.photo : photo
        );
        message.success(SUCCESS_MESSAGES.PHOTO_UPDATE_SUCCESS);
      })
      .addCase(updatePhoto.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 删除照片
      .addCase(deletePhoto.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePhoto.fulfilled, (state, action) => {
        state.loading = false;
        state.photos = state.photos.filter(photo => photo.id !== action.payload);
        message.success(SUCCESS_MESSAGES.PHOTO_DELETE_SUCCESS);
      })
      .addCase(deletePhoto.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 生成照片
      .addCase(generatePhoto.pending, (state) => {
        state.generating = true;
        state.error = null;
      })
      .addCase(generatePhoto.fulfilled, (state, action) => {
        state.generating = false;
        state.generatedPhoto = action.payload.photo;
      })
      .addCase(generatePhoto.rejected, (state, action) => {
        state.generating = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 获取收藏照片列表
      .addCase(fetchFavorites.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFavorites.fulfilled, (state, action) => {
        state.loading = false;
        state.favorites = action.payload.photos;
      })
      .addCase(fetchFavorites.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 添加收藏
      .addCase(addFavorite.fulfilled, (state, action) => {
        // 更新照片详情中的收藏状态
        if (state.photo && state.photo.id === action.payload.photo.id) {
          state.photo = {
            ...state.photo,
            isFavorite: true,
          };
        }
        
        // 更新照片列表中的收藏状态
        state.photos = state.photos.map(photo => 
          photo.id === action.payload.photo.id ? { ...photo, isFavorite: true } : photo
        );
        
        message.success('已添加到收藏');
      })
      .addCase(addFavorite.rejected, (state, action) => {
        message.error(action.payload);
      })
      
      // 移除收藏
      .addCase(removeFavorite.fulfilled, (state, action) => {
        // 更新照片详情中的收藏状态
        if (state.photo && state.photo.id === action.payload) {
          state.photo = {
            ...state.photo,
            isFavorite: false,
          };
        }
        
        // 更新照片列表中的收藏状态
        state.photos = state.photos.map(photo => 
          photo.id === action.payload ? { ...photo, isFavorite: false } : photo
        );
        
        // 从收藏列表中移除
        state.favorites = state.favorites.filter(photo => photo.id !== action.payload);
        
        message.success('已从收藏中移除');
      })
      .addCase(removeFavorite.rejected, (state, action) => {
        message.error(action.payload);
      });
  },
});

export const { 
  clearError, 
  setFilters, 
  resetFilters, 
  clearGeneratedPhoto,
  clearPhotoDetail,
} = photoSlice.actions;

export default photoSlice.reducer;