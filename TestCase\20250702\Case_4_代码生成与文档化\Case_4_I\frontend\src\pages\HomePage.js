import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Row, Col, Card, Button, Typography, Steps, Carousel } from 'antd';
import { UploadOutlined, RobotOutlined, PictureOutlined, DownloadOutlined } from '@ant-design/icons';
import './HomePage.scss';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const HomePage = () => {
  const navigate = useNavigate();

  // 使用步骤数据
  const steps = [
    {
      title: '上传照片',
      description: '上传您的个人照片，支持JPG、PNG、JPEG格式',
      icon: <UploadOutlined />
    },
    {
      title: 'AI处理',
      description: '我们的AI技术自动处理您的照片，生成标准证件照',
      icon: <RobotOutlined />
    },
    {
      title: '预览选择',
      description: '预览多个生成结果，选择您最满意的照片',
      icon: <PictureOutlined />
    },
    {
      title: '下载使用',
      description: '下载高清证件照，可直接用于各类证件申请',
      icon: <DownloadOutlined />
    }
  ];

  // 特色功能数据
  const features = [
    {
      title: '智能人脸识别',
      description: '自动检测人脸位置，确保证件照符合标准要求',
      icon: <RobotOutlined style={{ fontSize: 32, color: '#1890ff' }} />
    },
    {
      title: '多种规格支持',
      description: '支持1寸、2寸等多种证件照规格，满足不同场景需求',
      icon: <PictureOutlined style={{ fontSize: 32, color: '#1890ff' }} />
    },
    {
      title: '高清照片输出',
      description: '生成高清晰度证件照，确保打印效果清晰自然',
      icon: <DownloadOutlined style={{ fontSize: 32, color: '#1890ff' }} />
    }
  ];

  // 用户案例数据（实际项目中可以从后端获取）
  const userCases = [
    {
      name: '张先生',
      usage: '求职简历',
      comment: '非常方便，几分钟就生成了专业的证件照，节省了去照相馆的时间。'
    },
    {
      name: '李女士',
      usage: '护照申请',
      comment: '生成的照片完全符合护照要求，使用后顺利通过了审核。'
    },
    {
      name: '王同学',
      usage: '学生证',
      comment: '作为学生党，这个平台太实用了，价格也很亲民。'
    }
  ];

  return (
    <div className="home-page">
      {/* 英雄区域 */}
      <div className="hero-section">
        <div className="container">
          <Row gutter={[24, 24]} align="middle">
            <Col xs={24} md={12}>
              <div className="hero-content">
                <Title level={1}>AI证件照生成平台</Title>
                <Paragraph className="hero-description">
                  只需上传一张照片，即可快速生成符合标准的高质量证件照。
                  支持多种规格，满足各类证件需求。
                </Paragraph>
                <Button 
                  type="primary" 
                  size="large" 
                  icon={<UploadOutlined />}
                  onClick={() => navigate('/upload')}
                >
                  立即体验
                </Button>
              </div>
            </Col>
            <Col xs={24} md={12}>
              <div className="hero-image">
                <img src="/images/hero-image.svg" alt="AI证件照生成" />
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* 使用步骤 */}
      <div className="section steps-section">
        <div className="container">
          <Title level={2} className="section-title text-center">使用流程</Title>
          <Steps current={-1}>
            {steps.map((step, index) => (
              <Step 
                key={index} 
                title={step.title} 
                description={step.description} 
                icon={step.icon}
              />
            ))}
          </Steps>
        </div>
      </div>

      {/* 特色功能 */}
      <div className="section features-section">
        <div className="container">
          <Title level={2} className="section-title text-center">特色功能</Title>
          <Row gutter={[24, 24]}>
            {features.map((feature, index) => (
              <Col xs={24} sm={8} key={index}>
                <Card className="feature-card">
                  <div className="feature-icon">{feature.icon}</div>
                  <Title level={4}>{feature.title}</Title>
                  <Paragraph>{feature.description}</Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 用户案例 */}
      <div className="section cases-section">
        <div className="container">
          <Title level={2} className="section-title text-center">用户案例</Title>
          <Carousel autoplay>
            {userCases.map((userCase, index) => (
              <div key={index}>
                <Card className="case-card">
                  <Paragraph className="case-comment">"{userCase.comment}"</Paragraph>
                  <div className="case-info">
                    <div className="case-name">{userCase.name}</div>
                    <div className="case-usage">{userCase.usage}</div>
                  </div>
                </Card>
              </div>
            ))}
          </Carousel>
        </div>
      </div>

      {/* 立即开始 */}
      <div className="section cta-section">
        <div className="container text-center">
          <Title level={2}>立即开始生成您的证件照</Title>
          <Paragraph className="cta-description">
            只需简单几步，即可获得专业证件照，省时省力省钱！
          </Paragraph>
          <Button 
            type="primary" 
            size="large" 
            icon={<UploadOutlined />}
            onClick={() => navigate('/upload')}
          >
            立即上传照片
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HomePage;