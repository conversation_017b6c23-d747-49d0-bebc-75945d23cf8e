import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

/**
 * 加载中组件
 * 显示加载状态的旋转图标
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.tip - 提示文字
 * @param {number} props.size - 图标大小
 * @param {string} props.color - 图标颜色
 * @returns {React.ReactNode} 渲染的组件
 */
const LoadingSpinner = ({ tip = '加载中...', size = 40, color = '#1890ff' }) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size, color }} spin />;

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        height: '100%',
        minHeight: 200,
      }}
    >
      <Spin indicator={antIcon} tip={tip} />
    </div>
  );
};

export default LoadingSpinner;