import React from 'react';
import { Card, Button, Dropdown, Menu, Tooltip, Tag } from 'antd';
import {
  DownloadOutlined,
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  EyeOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { formatDate } from '../../utils/utils';
import { ROUTES } from '../../utils/constants';

const { Meta } = Card;

const PhotoCard = ({
  photo,
  onDelete,
  onDownload,
  onToggleFavorite,
  showActions = true,
}) => {
  const navigate = useNavigate();

  const handleView = () => {
    navigate(`${ROUTES.RESULT}/${photo.id}`);
  };

  const handleEdit = () => {
    navigate(`${ROUTES.EDIT}/${photo.id}`);
  };

  const moreMenu = (
    <Menu>
      <Menu.Item key="share" icon={<ShareAltOutlined />} onClick={() => {}}>
        分享
      </Menu.Item>
      <Menu.Item key="delete" icon={<DeleteOutlined />} onClick={() => onDelete(photo.id)}>
        删除
      </Menu.Item>
    </Menu>
  );

  const renderPhotoType = () => {
    let color;
    switch (photo.type) {
      case 'id_photo':
        color = 'blue';
        break;
      case 'passport_photo':
        color = 'green';
        break;
      case 'visa_photo':
        color = 'purple';
        break;
      case 'driver_license':
        color = 'orange';
        break;
      default:
        color = 'default';
    }

    return (
      <Tag color={color} className="photo-type-tag">
        {photo.typeName || photo.type}
      </Tag>
    );
  };

  return (
    <Card
      hoverable
      cover={
        <div className="photo-card-cover" onClick={handleView}>
          <img alt={photo.name} src={photo.thumbnailUrl || photo.url} />
          {renderPhotoType()}
        </div>
      }
      actions={
        showActions
          ? [
              <Tooltip title={photo.isFavorite ? '取消收藏' : '收藏'}>
                {photo.isFavorite ? (
                  <StarFilled
                    className="favorite-icon active"
                    onClick={() => onToggleFavorite(photo.id)}
                  />
                ) : (
                  <StarOutlined onClick={() => onToggleFavorite(photo.id)} />
                )}
              </Tooltip>,
              <Tooltip title="查看">
                <EyeOutlined onClick={handleView} />
              </Tooltip>,
              <Tooltip title="编辑">
                <EditOutlined onClick={handleEdit} />
              </Tooltip>,
              <Tooltip title="下载">
                <DownloadOutlined onClick={() => onDownload(photo.id)} />
              </Tooltip>,
              <Dropdown overlay={moreMenu} trigger={['click']}>
                <EllipsisOutlined />
              </Dropdown>,
            ]
          : []
      }
      className="photo-card"
    >
      <Meta
        title={photo.name}
        description={
          <div className="photo-card-meta">
            <span className="photo-date">{formatDate(photo.createdAt, 'YYYY-MM-DD')}</span>
            <span className="photo-size">{photo.size}</span>
          </div>
        }
      />
    </Card>
  );
};

export default PhotoCard;