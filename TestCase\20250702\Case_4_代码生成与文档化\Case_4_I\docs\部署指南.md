# AI证件照生成平台 部署指南

## 系统要求

### 硬件要求

- **最低配置**：
  - CPU: 2核
  - 内存: 4GB RAM
  - 存储: 20GB SSD

- **推荐配置**：
  - CPU: 4核
  - 内存: 8GB RAM
  - 存储: 50GB SSD

### 软件要求

- **操作系统**：
  - Linux (Ubuntu 20.04+, CentOS 8+)
  - Windows Server 2019+
  - macOS 11+

- **运行环境**：
  - Node.js 14.0+
  - MongoDB 4.4+
  - Nginx 1.18+ (用于反向代理)

## 开发环境部署

### 1. 克隆代码仓库

```bash
git clone <repository-url>
cd ai-id-photo-generator
```

### 2. 安装依赖

#### 后端依赖

```bash
cd backend
npm install
```

#### 前端依赖

```bash
cd ../frontend
npm install
```

### 3. 配置环境变量

#### 后端环境变量

创建 `backend/.env` 文件：

```
NODE_ENV=development
PORT=5000
MONGO_URI=mongodb://localhost:27017/ai-id-photo
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
```

#### 前端环境变量

创建 `frontend/.env` 文件：

```
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_NAME=AI证件照生成平台
```

### 4. 启动MongoDB

```bash
# Ubuntu/Debian
sudo systemctl start mongod

# CentOS/RHEL
sudo service mongod start

# Windows (以管理员身份运行PowerShell)
Net start MongoDB

# macOS
brew services start mongodb-community
```

### 5. 启动开发服务器

#### 后端服务器

```bash
cd backend
npm run dev
```

#### 前端服务器

```bash
cd frontend
npm start
```

## 生产环境部署

### 1. 构建前端应用

```bash
cd frontend
npm run build
```

### 2. 配置Nginx

创建Nginx配置文件 `/etc/nginx/sites-available/ai-id-photo.conf`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/frontend/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 上传文件访问
    location /uploads {
        alias /path/to/backend/uploads;
    }
}
```

启用配置：

```bash
sudo ln -s /etc/nginx/sites-available/ai-id-photo.conf /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 3. 配置PM2管理后端服务

安装PM2：

```bash
npm install -g pm2
```

创建PM2配置文件 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: "ai-id-photo-api",
    script: "./backend/server.js",
    instances: "max",
    exec_mode: "cluster",
    env: {
      NODE_ENV: "production",
      PORT: 5000
    }
  }]
};
```

启动服务：

```bash
pm2 start ecosystem.config.js
```

### 4. 配置MongoDB生产环境

#### 启用MongoDB认证

创建管理员用户：

```javascript
use admin
db.createUser(
  {
    user: "adminUser",
    pwd: "securePassword",
    roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
  }
)
```

创建应用数据库用户：

```javascript
use ai-id-photo
db.createUser(
  {
    user: "appUser",
    pwd: "appPassword",
    roles: [ { role: "readWrite", db: "ai-id-photo" } ]
  }
)
```

修改MongoDB配置启用认证：

```yaml
security:
  authorization: enabled
```

更新后端环境变量：

```
MONGO_URI=*********************************************************
```

### 5. 配置HTTPS

使用Let's Encrypt获取SSL证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 6. 设置防火墙

```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 数据备份

### 设置MongoDB备份

创建备份脚本 `backup.sh`：

```bash
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATETIME=$(date +"%Y%m%d_%H%M%S")

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 执行备份
mongodump --uri="*********************************************************" --out="$BACKUP_DIR/$DATETIME"

# 压缩备份
tar -zcvf "$BACKUP_DIR/$DATETIME.tar.gz" "$BACKUP_DIR/$DATETIME"

# 删除原始备份文件
rm -rf "$BACKUP_DIR/$DATETIME"

# 保留最近30天的备份
find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +30 -delete
```

设置定时任务：

```bash
chmod +x backup.sh
crontab -e
```

添加以下内容：

```
0 2 * * * /path/to/backup.sh
```

## 监控与日志

### 配置日志

安装Winston：

```bash
cd backend
npm install winston
```

### 设置监控

使用PM2监控：

```bash
pm2 monit
```

设置PM2日志轮转：

```bash
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
```

## 故障排除

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否运行
   - 验证连接字符串和认证信息
   - 检查网络防火墙设置

2. **上传文件失败**
   - 检查上传目录权限
   - 验证文件大小限制配置
   - 检查磁盘空间

3. **前端无法连接API**
   - 检查API服务是否运行
   - 验证Nginx配置
   - 检查CORS设置

### 日志位置

- **后端日志**：`~/.pm2/logs/`
- **Nginx访问日志**：`/var/log/nginx/access.log`
- **Nginx错误日志**：`/var/log/nginx/error.log`
- **MongoDB日志**：`/var/log/mongodb/mongod.log`

## 扩展与优化

### 性能优化

1. **启用Nginx缓存**

```nginx
proxy_cache_path /path/to/cache levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m;

location /api {
    proxy_cache api_cache;
    proxy_cache_valid 200 10m;
    proxy_cache_methods GET HEAD;
    proxy_pass http://localhost:5000;
    # 其他设置...
}
```

2. **MongoDB索引优化**

```javascript
// 在常用查询字段上创建索引
db.users.createIndex({ "email": 1 });
db.photoTasks.createIndex({ "user": 1, "status": 1 });
```

### 水平扩展

1. **负载均衡**

使用Nginx作为负载均衡器：

```nginx
upstream api_servers {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

location /api {
    proxy_pass http://api_servers;
    # 其他设置...
}
```

2. **MongoDB复制集**

设置MongoDB复制集以提高可用性和读取性能。