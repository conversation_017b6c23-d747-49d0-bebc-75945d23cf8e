const User = require('./User');
const Upload = require('./Upload');
const GenerationTask = require('./GenerationTask');
const GeneratedPhoto = require('./GeneratedPhoto');

// 定义模型关联关系

// User 与 Upload 的关联
User.hasMany(Upload, {
  foreignKey: 'userId',
  as: 'uploads'
});
Upload.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

// Upload 与 GenerationTask 的关联
Upload.hasMany(GenerationTask, {
  foreignKey: 'uploadId',
  as: 'generationTasks'
});
GenerationTask.belongsTo(Upload, {
  foreignKey: 'uploadId',
  as: 'upload'
});

// GenerationTask 与 GeneratedPhoto 的关联
GenerationTask.hasMany(GeneratedPhoto, {
  foreignKey: 'taskId',
  as: 'generatedPhotos'
});
GeneratedPhoto.belongsTo(GenerationTask, {
  foreignKey: 'taskId',
  as: 'task'
});

// 通过关联获取用户的所有生成照片
User.hasMany(GeneratedPhoto, {
  through: {
    model: Upload,
    through: GenerationTask
  },
  foreignKey: 'userId',
  as: 'allGeneratedPhotos'
});

module.exports = {
  User,
  Upload,
  GenerationTask,
  GeneratedPhoto
};
