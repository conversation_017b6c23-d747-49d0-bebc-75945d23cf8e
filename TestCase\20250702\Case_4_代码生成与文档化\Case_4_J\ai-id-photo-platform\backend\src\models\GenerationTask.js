const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const GenerationTask = sequelize.define('GenerationTask', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  uploadId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'upload_id',
    references: {
      model: 'uploads',
      key: 'id'
    }
  },
  spec: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['1inch', '2inch', 'small2inch', 'big1inch', 'custom']]
    }
  },
  background: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['blue', 'red', 'white', 'lightblue', 'lightgray']]
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    defaultValue: 'pending'
  },
  progress: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  modelName: {
    type: DataTypes.STRING(50),
    allowNull: true,
    field: 'model_name',
    comment: '使用的AI模型名称'
  },
  modelConfig: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'model_config',
    comment: '模型配置参数'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    field: 'error_message'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'completed_at'
  },
  customSpec: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'custom_spec',
    comment: '自定义规格参数，当spec为custom时使用'
  }
}, {
  tableName: 'generation_tasks',
  timestamps: false
});

module.exports = GenerationTask;
