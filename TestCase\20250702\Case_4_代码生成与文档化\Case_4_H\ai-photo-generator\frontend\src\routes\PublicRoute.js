import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { ROUTES, STORAGE_KEYS } from '../utils/constants';

/**
 * 公共路由守卫
 * 
 * 用于处理公共路由，如登录、注册等页面
 * 如果用户已登录，重定向到仪表盘页面
 * 
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 子组件
 * @param {boolean} props.restricted - 是否限制已登录用户访问
 * @returns {JSX.Element} 路由组件
 */
const PublicRoute = ({ children, restricted = true }) => {
  const location = useLocation();
  const { isAuthenticated } = useSelector((state) => state.auth);
  const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
  
  // 获取重定向路径，如果有的话
  const from = location.state?.from?.pathname || ROUTES.DASHBOARD;

  // 如果用户已登录且路由受限，重定向到仪表盘或来源页面
  if ((isAuthenticated || token) && restricted) {
    return <Navigate to={from} replace />;
  }

  // 如果用户未登录或路由不受限，渲染子组件
  return children;
};

export default PublicRoute;