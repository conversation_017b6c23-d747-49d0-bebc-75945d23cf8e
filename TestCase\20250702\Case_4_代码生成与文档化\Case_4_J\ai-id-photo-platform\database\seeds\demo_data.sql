-- 演示数据种子脚本
-- 用于开发和测试环境

USE ai_photo_platform;

-- 清空现有数据（仅在开发环境使用）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE generated_photos;
TRUNCATE TABLE generation_tasks;
TRUNCATE TABLE uploads;
TRUNCATE TABLE user_logs;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试用户
INSERT INTO users (id, username, email, password_hash, credits, is_active, created_at) VALUES
(1, 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', 100, TRUE, '2024-01-01 10:00:00'),
(2, 'testuser', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', 50, TRUE, '2024-01-02 10:00:00'),
(3, 'demouser', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', 20, TRUE, '2024-01-03 10:00:00'),
(4, 'inactive', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', 0, FALSE, '2024-01-04 10:00:00');

-- 插入测试上传记录
INSERT INTO uploads (id, user_id, original_filename, file_path, file_size, file_type, upload_time, metadata, status) VALUES
('upload_001', 1, 'portrait1.jpg', '/uploads/original/original_001.jpg', 2048576, 'image/jpeg', '2024-01-10 14:30:00', '{"width": 800, "height": 1200, "format": "jpeg"}', 'uploaded'),
('upload_002', 1, 'selfie.png', '/uploads/original/original_002.png', 1536000, 'image/png', '2024-01-11 09:15:00', '{"width": 600, "height": 800, "format": "png"}', 'uploaded'),
('upload_003', 2, 'photo.jpg', '/uploads/original/original_003.jpg', 3072000, 'image/jpeg', '2024-01-12 16:45:00', '{"width": 1000, "height": 1500, "format": "jpeg"}', 'uploaded'),
('upload_004', 2, 'headshot.jpg', '/uploads/original/original_004.jpg', 2560000, 'image/jpeg', '2024-01-13 11:20:00', '{"width": 900, "height": 1200, "format": "jpeg"}', 'uploaded'),
('upload_005', 3, 'avatar.png', '/uploads/original/original_005.png', 1024000, 'image/png', '2024-01-14 13:10:00', '{"width": 500, "height": 700, "format": "png"}', 'uploaded');

-- 插入测试生成任务
INSERT INTO generation_tasks (id, upload_id, spec, background, status, progress, model_name, created_at, completed_at) VALUES
('task_001', 'upload_001', '1inch', 'blue', 'completed', 100, 'default_model', '2024-01-10 14:35:00', '2024-01-10 14:37:00'),
('task_002', 'upload_001', '2inch', 'white', 'completed', 100, 'default_model', '2024-01-10 15:00:00', '2024-01-10 15:02:00'),
('task_003', 'upload_002', '1inch', 'red', 'completed', 100, 'custom_model_v1', '2024-01-11 09:20:00', '2024-01-11 09:23:00'),
('task_004', 'upload_003', '2inch', 'blue', 'processing', 75, 'default_model', '2024-01-12 16:50:00', NULL),
('task_005', 'upload_004', 'small2inch', 'lightblue', 'pending', 0, 'default_model', '2024-01-13 11:25:00', NULL),
('task_006', 'upload_005', '1inch', 'white', 'failed', 0, 'default_model', '2024-01-14 13:15:00', NULL);

-- 更新失败任务的错误信息
UPDATE generation_tasks SET error_message = 'AI服务暂时不可用，请稍后重试' WHERE id = 'task_006';

-- 插入测试生成照片
INSERT INTO generated_photos (id, task_id, file_path, quality_score, file_size, width, height, format, download_count, created_at) VALUES
-- task_001 的生成照片
('photo_001_1', 'task_001', '/uploads/generated/generated_001_1.jpg', 0.92, 156800, 295, 413, 'jpeg', 3, '2024-01-10 14:37:00'),
('photo_001_2', 'task_001', '/uploads/generated/generated_001_2.jpg', 0.89, 158400, 295, 413, 'jpeg', 1, '2024-01-10 14:37:00'),
('photo_001_3', 'task_001', '/uploads/generated/generated_001_3.jpg', 0.95, 155200, 295, 413, 'jpeg', 5, '2024-01-10 14:37:00'),
('photo_001_4', 'task_001', '/uploads/generated/generated_001_4.jpg', 0.87, 159600, 295, 413, 'jpeg', 0, '2024-01-10 14:37:00'),
('photo_001_5', 'task_001', '/uploads/generated/generated_001_5.jpg', 0.91, 157600, 295, 413, 'jpeg', 2, '2024-01-10 14:37:00'),

-- task_002 的生成照片
('photo_002_1', 'task_002', '/uploads/generated/generated_002_1.jpg', 0.88, 224000, 413, 579, 'jpeg', 2, '2024-01-10 15:02:00'),
('photo_002_2', 'task_002', '/uploads/generated/generated_002_2.jpg', 0.93, 221600, 413, 579, 'jpeg', 4, '2024-01-10 15:02:00'),
('photo_002_3', 'task_002', '/uploads/generated/generated_002_3.jpg', 0.90, 226400, 413, 579, 'jpeg', 1, '2024-01-10 15:02:00'),
('photo_002_4', 'task_002', '/uploads/generated/generated_002_4.jpg', 0.85, 228800, 413, 579, 'jpeg', 0, '2024-01-10 15:02:00'),
('photo_002_5', 'task_002', '/uploads/generated/generated_002_5.jpg', 0.94, 220800, 413, 579, 'jpeg', 3, '2024-01-10 15:02:00'),

-- task_003 的生成照片
('photo_003_1', 'task_003', '/uploads/generated/generated_003_1.jpg', 0.96, 154400, 295, 413, 'jpeg', 6, '2024-01-11 09:23:00'),
('photo_003_2', 'task_003', '/uploads/generated/generated_003_2.jpg', 0.91, 156000, 295, 413, 'jpeg', 2, '2024-01-11 09:23:00'),
('photo_003_3', 'task_003', '/uploads/generated/generated_003_3.jpg', 0.89, 158800, 295, 413, 'jpeg', 1, '2024-01-11 09:23:00'),
('photo_003_4', 'task_003', '/uploads/generated/generated_003_4.jpg', 0.93, 155600, 295, 413, 'jpeg', 4, '2024-01-11 09:23:00'),
('photo_003_5', 'task_003', '/uploads/generated/generated_003_5.jpg', 0.87, 159200, 295, 413, 'jpeg', 0, '2024-01-11 09:23:00');

-- 插入用户操作日志
INSERT INTO user_logs (user_id, action, resource_type, resource_id, ip_address, user_agent, details, created_at) VALUES
(1, 'login', 'user', '1', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '{"login_method": "email"}', '2024-01-10 14:00:00'),
(1, 'upload', 'upload', 'upload_001', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '{"file_size": 2048576, "file_type": "image/jpeg"}', '2024-01-10 14:30:00'),
(1, 'generate', 'task', 'task_001', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '{"spec": "1inch", "background": "blue"}', '2024-01-10 14:35:00'),
(1, 'download', 'photo', 'photo_001_3', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '{"download_type": "single"}', '2024-01-10 14:40:00'),
(2, 'register', 'user', '2', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '{"registration_method": "email"}', '2024-01-02 10:00:00'),
(2, 'login', 'user', '2', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '{"login_method": "email"}', '2024-01-12 16:00:00'),
(2, 'upload', 'upload', 'upload_003', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '{"file_size": 3072000, "file_type": "image/jpeg"}', '2024-01-12 16:45:00'),
(3, 'register', 'user', '3', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', '{"registration_method": "email"}', '2024-01-03 10:00:00'),
(3, 'login', 'user', '3', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', '{"login_method": "email"}', '2024-01-14 13:00:00');

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('max_file_size', '10485760', 'number', '最大文件上传大小(字节)', TRUE),
('max_files_count', '5', 'number', '最大文件上传数量', TRUE),
('supported_formats', '["image/jpeg", "image/jpg", "image/png"]', 'json', '支持的文件格式', TRUE),
('default_credits', '10', 'number', '新用户默认积分', FALSE),
('photo_specs', '{"1inch": {"width": 295, "height": 413, "name": "1寸证件照"}, "2inch": {"width": 413, "height": 579, "name": "2寸证件照"}, "small2inch": {"width": 390, "height": 567, "name": "小2寸证件照"}, "big1inch": {"width": 390, "height": 531, "name": "大1寸证件照"}}', 'json', '证件照规格配置', TRUE),
('background_colors', '{"blue": "#438EDB", "red": "#FF0000", "white": "#FFFFFF", "lightblue": "#87CEEB", "lightgray": "#D3D3D3"}', 'json', '背景颜色配置', TRUE),
('ai_models', '{"default_model": {"name": "default_model", "endpoint": "https://api.example.com/generate", "timeout": 30000}, "custom_model_v1": {"name": "custom_model_v1", "endpoint": "https://custom-api.example.com/process", "timeout": 45000}}', 'json', 'AI模型配置', FALSE),
('site_title', 'AI证件照生成平台', 'string', '网站标题', TRUE),
('site_description', '利用先进的AI技术，快速生成高质量标准证件照', 'string', '网站描述', TRUE),
('contact_email', '<EMAIL>', 'string', '联系邮箱', TRUE),
('maintenance_mode', 'false', 'boolean', '维护模式', FALSE);

-- 更新用户最后登录时间
UPDATE users SET last_login_at = '2024-01-15 10:30:00' WHERE id = 1;
UPDATE users SET last_login_at = '2024-01-14 16:45:00' WHERE id = 2;
UPDATE users SET last_login_at = '2024-01-14 13:00:00' WHERE id = 3;
