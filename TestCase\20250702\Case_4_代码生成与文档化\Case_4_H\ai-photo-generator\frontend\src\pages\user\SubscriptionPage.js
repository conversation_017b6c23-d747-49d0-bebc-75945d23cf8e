import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Typo<PERSON>, 
  Card, 
  Button, 
  Row, 
  Col, 
  Tabs, 
  List, 
  Tag, 
  Divider, 
  Alert, 
  Spin, 
  Modal,
  Radio,
  Space,
  Steps,
  Result,
  message
} from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  CrownOutlined, 
  DollarOutlined,
  CreditCardOutlined,
  HistoryOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { 
  fetchSubscriptionPlans, 
  fetchUserSubscription, 
  subscribeToplan, 
  cancelSubscription,
  selectSubscriptionPlans, 
  selectUserSubscription, 
  selectSubscriptionLoading, 
  selectSubscriptionError 
} from '../../store/subscriptionSlice';
import { selectUser } from '../../store/authSlice';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Step } = Steps;
const { confirm } = Modal;

/**
 * 订阅页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const SubscriptionPage = () => {
  const dispatch = useDispatch();
  const plans = useSelector(selectSubscriptionPlans);
  const userSubscription = useSelector(selectUserSubscription);
  const loading = useSelector(selectSubscriptionLoading);
  const error = useSelector(selectSubscriptionError);
  const user = useSelector(selectUser);
  
  const [activeTab, setActiveTab] = useState('plans');
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [currentStep, setCurrentStep] = useState(0);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  
  // 获取订阅计划和用户订阅信息
  useEffect(() => {
    dispatch(fetchSubscriptionPlans());
    dispatch(fetchUserSubscription());
  }, [dispatch]);
  
  /**
   * 处理选择计划
   * 
   * @param {Object} plan - 订阅计划
   */
  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);
    setPaymentModalVisible(true);
    setCurrentStep(0);
    setPaymentSuccess(false);
  };
  
  /**
   * 处理支付方式变更
   * 
   * @param {Event} e - 事件对象
   */
  const handlePaymentMethodChange = (e) => {
    setPaymentMethod(e.target.value);
  };
  
  /**
   * 处理下一步
   */
  const handleNextStep = () => {
    if (currentStep === 0) {
      // 验证支付方式
      if (!paymentMethod) {
        message.error('请选择支付方式');
        return;
      }
      setCurrentStep(1);
    } else if (currentStep === 1) {
      // 模拟支付处理
      handlePayment();
    }
  };
  
  /**
   * 处理支付
   */
  const handlePayment = () => {
    // 模拟支付处理
    setCurrentStep(2);
    
    // 模拟API调用
    setTimeout(() => {
      dispatch(subscribeToplan({
        planId: selectedPlan.id,
        paymentMethod
      }))
        .unwrap()
        .then(() => {
          setPaymentSuccess(true);
          message.success('订阅成功');
          // 重新获取用户订阅信息
          dispatch(fetchUserSubscription());
        })
        .catch((err) => {
          message.error('订阅失败: ' + err.message);
        });
    }, 2000);
  };
  
  /**
   * 处理取消订阅
   */
  const handleCancelSubscription = () => {
    confirm({
      title: '确认取消订阅',
      content: '您确定要取消当前订阅吗？取消后，您将失去高级会员的所有权益，但可以继续使用到当前订阅期结束。',
      okText: '确认取消',
      okType: 'danger',
      cancelText: '返回',
      onOk: async () => {
        try {
          await dispatch(cancelSubscription()).unwrap();
          message.success('订阅已成功取消');
          // 重新获取用户订阅信息
          dispatch(fetchUserSubscription());
        } catch (err) {
          message.error('取消失败: ' + err.message);
        }
      },
    });
  };
  
  /**
   * 渲染计划特性
   * 
   * @param {Array} features - 特性列表
   * @param {boolean} isPremium - 是否为高级计划
   * @returns {React.ReactNode} 渲染的组件
   */
  const renderPlanFeatures = (features, isPremium) => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={features}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                item.included ? 
                <CheckCircleOutlined style={{ color: isPremium ? '#52c41a' : '#1890ff' }} /> : 
                <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
              }
              title={item.name}
              description={item.description}
            />
          </List.Item>
        )}
      />
    );
  };
  
  /**
   * 渲染支付步骤
   * 
   * @returns {React.ReactNode} 渲染的组件
   */
  const renderPaymentSteps = () => {
    return (
      <div className="payment-steps">
        <Steps current={currentStep} style={{ marginBottom: 24 }}>
          <Step title="选择支付方式" />
          <Step title="确认订单" />
          <Step title="完成支付" />
        </Steps>
        
        <div className="step-content">
          {currentStep === 0 && (
            <div className="payment-methods">
              <Title level={5}>选择支付方式</Title>
              <Radio.Group onChange={handlePaymentMethodChange} value={paymentMethod}>
                <Space direction="vertical">
                  <Radio value="credit_card">
                    <Space>
                      <CreditCardOutlined />
                      信用卡支付
                    </Space>
                  </Radio>
                  <Radio value="alipay">
                    <Space>
                      <img src="/images/alipay-icon.svg" alt="支付宝" style={{ width: 16, height: 16 }} />
                      支付宝
                    </Space>
                  </Radio>
                  <Radio value="wechat">
                    <Space>
                      <img src="/images/wechat-icon.svg" alt="微信支付" style={{ width: 16, height: 16 }} />
                      微信支付
                    </Space>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>
          )}
          
          {currentStep === 1 && (
            <div className="order-confirmation">
              <Title level={5}>确认订单</Title>
              <Card>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                  <Text>订阅计划</Text>
                  <Text strong>{selectedPlan?.name}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                  <Text>订阅周期</Text>
                  <Text>{selectedPlan?.billingCycle}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                  <Text>支付方式</Text>
                  <Text>
                    {paymentMethod === 'credit_card' && '信用卡支付'}
                    {paymentMethod === 'alipay' && '支付宝'}
                    {paymentMethod === 'wechat' && '微信支付'}
                  </Text>
                </div>
                <Divider />
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text>小计</Text>
                  <Text>¥{selectedPlan?.price}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
                  <Text>优惠</Text>
                  <Text>-¥0</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>总计</Text>
                  <Text strong style={{ fontSize: 18, color: '#f5222d' }}>¥{selectedPlan?.price}</Text>
                </div>
              </Card>
            </div>
          )}
          
          {currentStep === 2 && (
            <div className="payment-processing">
              {!paymentSuccess ? (
                <div style={{ textAlign: 'center', padding: 24 }}>
                  <Spin size="large" />
                  <div style={{ marginTop: 16 }}>
                    <Text>正在处理您的支付，请稍候...</Text>
                  </div>
                </div>
              ) : (
                <Result
                  status="success"
                  title="支付成功！"
                  subTitle={`您已成功订阅${selectedPlan?.name}，立即开始享受高级功能吧！`}
                  extra={[
                    <Button 
                      type="primary" 
                      key="console" 
                      onClick={() => setPaymentModalVisible(false)}
                      className="btn-primary"
                    >
                      完成
                    </Button>,
                  ]}
                />
              )}
            </div>
          )}
        </div>
        
        <div className="steps-action" style={{ marginTop: 24, textAlign: 'right' }}>
          {currentStep < 2 && (
            <Button 
              type="primary" 
              onClick={handleNextStep}
              className="btn-primary"
            >
              {currentStep === 0 ? '下一步' : '确认支付'}
            </Button>
          )}
          {currentStep > 0 && currentStep < 2 && (
            <Button 
              style={{ marginRight: 8 }} 
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              上一步
            </Button>
          )}
        </div>
      </div>
    );
  };
  
  // 如果正在加载，显示加载状态
  if (loading && !plans.length) {
    return (
      <div className="subscription-page page-container">
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </div>
    );
  }
  
  return (
    <div className="subscription-page page-container">
      <div className="page-title">
        <Title level={2}>会员订阅</Title>
      </div>
      
      {error && (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="订阅计划" key="plans">
            <div className="subscription-plans">
              {userSubscription && userSubscription.status === 'active' && (
                <Alert
                  message="您当前已订阅"
                  description={`您当前已订阅${userSubscription.plan.name}，有效期至${new Date(userSubscription.expiryDate).toLocaleDateString()}`}
                  type="info"
                  showIcon
                  style={{ marginBottom: 24 }}
                />
              )}
              
              <Row gutter={[24, 24]}>
                {plans.map((plan) => (
                  <Col xs={24} md={12} lg={8} key={plan.id}>
                    <Card
                      className={`plan-card ${plan.type === 'premium' ? 'premium-plan' : ''}`}
                      title={
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ marginBottom: 8 }}>
                            {plan.type === 'premium' && <CrownOutlined style={{ color: '#faad14', marginRight: 8 }} />}
                            <Text strong style={{ fontSize: 18 }}>{plan.name}</Text>
                          </div>
                          {plan.popular && <Tag color="red">最受欢迎</Tag>}
                        </div>
                      }
                      style={{ 
                        height: '100%',
                        borderColor: plan.type === 'premium' ? '#faad14' : undefined,
                      }}
                    >
                      <div className="plan-price" style={{ textAlign: 'center', marginBottom: 24 }}>
                        <Text style={{ fontSize: 32, fontWeight: 'bold' }}>¥{plan.price}</Text>
                        <Text type="secondary">/{plan.billingCycle}</Text>
                      </div>
                      
                      <div className="plan-description" style={{ marginBottom: 24 }}>
                        <Paragraph>{plan.description}</Paragraph>
                      </div>
                      
                      <div className="plan-features">
                        {renderPlanFeatures(plan.features, plan.type === 'premium')}
                      </div>
                      
                      <div style={{ textAlign: 'center', marginTop: 24 }}>
                        <Button 
                          type={plan.type === 'premium' ? 'primary' : 'default'} 
                          size="large"
                          block
                          onClick={() => handleSelectPlan(plan)}
                          className={plan.type === 'premium' ? 'btn-primary' : ''}
                          disabled={
                            userSubscription && 
                            userSubscription.status === 'active' && 
                            userSubscription.plan.id === plan.id
                          }
                        >
                          {userSubscription && 
                           userSubscription.status === 'active' && 
                           userSubscription.plan.id === plan.id ? 
                            '当前订阅' : '立即订阅'}
                        </Button>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
              
              <div style={{ textAlign: 'center', marginTop: 32 }}>
                <Paragraph>
                  <QuestionCircleOutlined style={{ marginRight: 8 }} />
                  有任何疑问？请查看我们的<a href="/faq">常见问题</a>或<a href="/contact">联系客服</a>
                </Paragraph>
              </div>
            </div>
          </TabPane>
          
          <TabPane tab="订阅管理" key="management">
            <div className="subscription-management">
              {!userSubscription || userSubscription.status !== 'active' ? (
                <div className="no-subscription">
                  <Result
                    status="info"
                    title="您当前没有活跃的订阅"
                    subTitle="订阅我们的会员计划，享受更多高级功能"
                    extra={
                      <Button type="primary" onClick={() => setActiveTab('plans')} className="btn-primary">
                        查看订阅计划
                      </Button>
                    }
                  />
                </div>
              ) : (
                <div className="active-subscription">
                  <Card title="当前订阅" bordered={false}>
                    <Row gutter={[24, 24]}>
                      <Col xs={24} md={16}>
                        <div className="subscription-details">
                          <div style={{ marginBottom: 16 }}>
                            <Text type="secondary">订阅计划</Text>
                            <div>
                              <Text strong style={{ fontSize: 18 }}>
                                {userSubscription.plan.name}
                                {userSubscription.plan.type === 'premium' && (
                                  <CrownOutlined style={{ color: '#faad14', marginLeft: 8 }} />
                                )}
                              </Text>
                            </div>
                          </div>
                          
                          <Row gutter={16}>
                            <Col span={8}>
                              <Text type="secondary">订阅状态</Text>
                              <div>
                                <Tag color={userSubscription.status === 'active' ? 'success' : 'default'}>
                                  {userSubscription.status === 'active' ? '活跃' : '已取消'}
                                </Tag>
                              </div>
                            </Col>
                            <Col span={8}>
                              <Text type="secondary">开始日期</Text>
                              <div>
                                <Text>{new Date(userSubscription.startDate).toLocaleDateString()}</Text>
                              </div>
                            </Col>
                            <Col span={8}>
                              <Text type="secondary">到期日期</Text>
                              <div>
                                <Text>{new Date(userSubscription.expiryDate).toLocaleDateString()}</Text>
                              </div>
                            </Col>
                          </Row>
                          
                          <Divider />
                          
                          <div style={{ marginBottom: 16 }}>
                            <Text type="secondary">订阅权益</Text>
                            <List
                              itemLayout="horizontal"
                              dataSource={userSubscription.plan.features.filter(f => f.included)}
                              renderItem={(item) => (
                                <List.Item>
                                  <List.Item.Meta
                                    avatar={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                                    title={item.name}
                                  />
                                </List.Item>
                              )}
                            />
                          </div>
                          
                          <div style={{ marginTop: 24 }}>
                            <Button danger onClick={handleCancelSubscription}>
                              取消订阅
                            </Button>
                          </div>
                        </div>
                      </Col>
                      
                      <Col xs={24} md={8}>
                        <Card title="账单信息" size="small">
                          <div style={{ marginBottom: 16 }}>
                            <Text type="secondary">支付方式</Text>
                            <div>
                              <Text>{userSubscription.paymentMethod || '信用卡'}</Text>
                            </div>
                          </div>
                          
                          <div style={{ marginBottom: 16 }}>
                            <Text type="secondary">账单周期</Text>
                            <div>
                              <Text>{userSubscription.plan.billingCycle}</Text>
                            </div>
                          </div>
                          
                          <div>
                            <Text type="secondary">下次扣款日期</Text>
                            <div>
                              <Text>{new Date(userSubscription.nextBillingDate).toLocaleDateString()}</Text>
                            </div>
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </div>
              )}
            </div>
          </TabPane>
          
          <TabPane tab="交易历史" key="history">
            <div className="transaction-history">
              {!user ? (
                <div className="loading-container">
                  <Spin />
                </div>
              ) : user.transactions && user.transactions.length > 0 ? (
                <List
                  itemLayout="horizontal"
                  dataSource={user.transactions}
                  renderItem={(transaction) => (
                    <List.Item
                      actions={[
                        <Button type="link" key="view">查看详情</Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<DollarOutlined />}
                        title={`订单 #${transaction.id}`}
                        description={`${transaction.description} - ${new Date(transaction.date).toLocaleDateString()}`}
                      />
                      <div>
                        <Text style={{ fontSize: 16 }}>¥{transaction.amount}</Text>
                        <div>
                          <Tag color={transaction.status === 'completed' ? 'success' : 'processing'}>
                            {transaction.status === 'completed' ? '已完成' : '处理中'}
                          </Tag>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              ) : (
                <Result
                  icon={<HistoryOutlined />}
                  title="暂无交易记录"
                  subTitle="您还没有任何交易记录"
                />
              )}
            </div>
          </TabPane>
        </Tabs>
      </Card>
      
      <Modal
        title={`订阅 ${selectedPlan?.name}`}
        open={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        footer={null}
        width={700}
      >
        {renderPaymentSteps()}
      </Modal>
    </div>
  );
};

export default SubscriptionPage;