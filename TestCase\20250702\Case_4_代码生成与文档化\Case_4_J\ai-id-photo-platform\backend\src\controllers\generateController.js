const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const { Upload, GenerationTask, GeneratedPhoto } = require('../models');
const aiService = require('../services/ai/aiService');
const { 
  validatePhotoSpec, 
  validateBackgroundColor,
  aiModels 
} = require('../config/aiModels');
const { 
  generateUniqueFileName,
  ensureDirectoryExists,
  getFileSize 
} = require('../utils/fileUtils');
const { generatedDir } = require('../middleware/upload');
const logger = require('../utils/logger');

class GenerateController {
  /**
   * 创建生成任务
   */
  async createGenerationTask(req, res, next) {
    try {
      const {
        uploadId,
        spec,
        background,
        count = 5,
        modelConfig = {},
        customSpec
      } = req.body;

      // 验证上传记录是否存在
      const upload = await Upload.findByPk(uploadId);
      if (!upload) {
        return res.status(404).json({
          code: 404,
          message: '上传记录不存在',
          errors: ['指定的上传ID无效']
        });
      }

      // 检查权限
      if (req.user && upload.userId && upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权访问此上传记录',
          errors: ['权限不足']
        });
      }

      // 验证证件照规格
      if (spec !== 'custom' && !validatePhotoSpec(spec)) {
        return res.status(400).json({
          code: 400,
          message: '无效的证件照规格',
          errors: ['请选择有效的证件照规格']
        });
      }

      // 验证背景颜色
      if (!validateBackgroundColor(background)) {
        return res.status(400).json({
          code: 400,
          message: '无效的背景颜色',
          errors: ['请选择有效的背景颜色']
        });
      }

      // 验证自定义规格
      if (spec === 'custom') {
        if (!customSpec || !customSpec.width || !customSpec.height) {
          return res.status(400).json({
            code: 400,
            message: '自定义规格参数不完整',
            errors: ['请提供完整的自定义规格参数']
          });
        }
      }

      // 生成任务ID
      const taskId = uuidv4();

      // 创建生成任务
      const task = await GenerationTask.create({
        id: taskId,
        uploadId,
        spec,
        background,
        status: 'pending',
        progress: 0,
        modelName: modelConfig.modelName || 'default_model',
        modelConfig,
        customSpec: spec === 'custom' ? customSpec : null
      });

      logger.info('生成任务创建成功', {
        taskId,
        uploadId,
        spec,
        background,
        userId: req.user?.id
      });

      // 异步开始生成过程
      this._processGenerationTask(task, upload).catch(error => {
        logger.error('生成任务处理失败', {
          taskId,
          error: error.message
        });
      });

      res.json({
        code: 200,
        message: '生成任务创建成功',
        data: {
          taskId: task.id,
          status: task.status,
          progress: task.progress,
          spec: task.spec,
          background: task.background,
          createdAt: task.createdAt
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 处理生成任务（异步）
   * @private
   */
  async _processGenerationTask(task, upload) {
    try {
      // 更新任务状态为处理中
      await task.update({
        status: 'processing',
        progress: 10
      });

      // 准备生成参数
      const generateParams = {
        imagePath: upload.filePath,
        spec: task.spec,
        background: task.background,
        count: task.modelConfig?.count || 5,
        modelConfig: task.modelConfig
      };

      // 如果是自定义规格，添加自定义参数
      if (task.spec === 'custom' && task.customSpec) {
        generateParams.customSpec = task.customSpec;
      }

      // 更新进度
      await task.update({ progress: 30 });

      // 调用AI服务生成证件照
      let generatedResults;
      if (process.env.NODE_ENV === 'development' || process.env.USE_MOCK_AI === 'true') {
        // 开发环境使用模拟生成
        generatedResults = await aiService.mockGenerate(generateParams);
      } else {
        // 生产环境使用真实AI服务
        generatedResults = await aiService.generateIDPhoto(generateParams);
      }

      // 更新进度
      await task.update({ progress: 70 });

      // 确保生成目录存在
      await ensureDirectoryExists(generatedDir);

      // 保存生成的照片
      const savedPhotos = [];
      for (let i = 0; i < generatedResults.length; i++) {
        const result = generatedResults[i];
        
        try {
          // 生成照片文件名
          const photoFileName = generateUniqueFileName(`generated_${task.id}_${i}.jpg`, '');
          const photoPath = path.join(generatedDir, photoFileName);
          
          // 这里应该下载或保存AI生成的图片
          // 在实际实现中，需要从AI服务返回的URL下载图片
          // 目前使用模拟数据
          const mockImageData = Buffer.from('mock image data');
          await fs.writeFile(photoPath, mockImageData);
          
          // 获取文件大小
          const fileSize = await getFileSize(photoPath);
          
          // 创建生成照片记录
          const photo = await GeneratedPhoto.create({
            id: result.id,
            taskId: task.id,
            filePath: photoPath,
            qualityScore: result.qualityScore,
            fileSize: fileSize,
            width: task.customSpec?.width || aiModels.photoSpecs[task.spec]?.width || 295,
            height: task.customSpec?.height || aiModels.photoSpecs[task.spec]?.height || 413,
            format: 'jpeg',
            metadata: result.metadata
          });
          
          savedPhotos.push({
            id: photo.id,
            url: `/uploads/generated/${photoFileName}`,
            qualityScore: photo.qualityScore,
            width: photo.width,
            height: photo.height,
            fileSize: photo.fileSize
          });
        } catch (error) {
          logger.error('保存生成照片失败', {
            taskId: task.id,
            photoIndex: i,
            error: error.message
          });
        }
      }

      // 更新任务状态为完成
      await task.update({
        status: 'completed',
        progress: 100,
        completedAt: new Date()
      });

      logger.info('生成任务完成', {
        taskId: task.id,
        generatedCount: savedPhotos.length
      });

    } catch (error) {
      // 更新任务状态为失败
      await task.update({
        status: 'failed',
        errorMessage: error.message
      });

      logger.error('生成任务失败', {
        taskId: task.id,
        error: error.message
      });
    }
  }

  /**
   * 获取生成任务状态
   */
  async getTaskStatus(req, res, next) {
    try {
      const { taskId } = req.params;

      const task = await GenerationTask.findByPk(taskId, {
        include: [
          {
            model: GeneratedPhoto,
            as: 'generatedPhotos'
          },
          {
            model: Upload,
            as: 'upload'
          }
        ]
      });

      if (!task) {
        return res.status(404).json({
          code: 404,
          message: '生成任务不存在',
          errors: ['指定的任务ID无效']
        });
      }

      // 检查权限
      if (req.user && task.upload.userId && task.upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权访问此生成任务',
          errors: ['权限不足']
        });
      }

      // 构建响应数据
      const responseData = {
        taskId: task.id,
        status: task.status,
        progress: task.progress,
        spec: task.spec,
        background: task.background,
        createdAt: task.createdAt,
        completedAt: task.completedAt,
        errorMessage: task.errorMessage
      };

      // 如果任务完成，包含生成的照片信息
      if (task.status === 'completed' && task.generatedPhotos) {
        responseData.generatedPhotos = task.generatedPhotos.map(photo => ({
          id: photo.id,
          url: `/uploads/generated/${path.basename(photo.filePath)}`,
          qualityScore: photo.qualityScore,
          width: photo.width,
          height: photo.height,
          fileSize: photo.fileSize,
          downloadCount: photo.downloadCount
        }));
      }

      res.json({
        code: 200,
        message: '获取任务状态成功',
        data: responseData
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户的生成任务列表
   */
  async getUserTasks(req, res, next) {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '请先登录',
          errors: ['需要用户认证']
        });
      }

      const { page = 1, limit = 10, status } = req.query;
      const offset = (page - 1) * limit;

      const whereClause = {};
      if (status) {
        whereClause.status = status;
      }

      const tasks = await GenerationTask.findAndCountAll({
        include: [
          {
            model: Upload,
            as: 'upload',
            where: { userId: req.user.id },
            attributes: ['originalFilename', 'uploadTime']
          },
          {
            model: GeneratedPhoto,
            as: 'generatedPhotos',
            attributes: ['id', 'qualityScore']
          }
        ],
        where: whereClause,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: offset
      });

      res.json({
        code: 200,
        message: '获取任务列表成功',
        data: {
          tasks: tasks.rows.map(task => ({
            taskId: task.id,
            status: task.status,
            progress: task.progress,
            spec: task.spec,
            background: task.background,
            createdAt: task.createdAt,
            completedAt: task.completedAt,
            originalFilename: task.upload.originalFilename,
            generatedCount: task.generatedPhotos.length
          })),
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(tasks.count / limit),
            totalCount: tasks.count,
            limit: parseInt(limit)
          }
        }
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new GenerateController();
