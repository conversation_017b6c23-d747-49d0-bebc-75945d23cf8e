const multer = require('multer');
const { handleFileUpload } = require('../services/uploadService');

// Multer 配置 - 内存存储
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 只接受图片文件
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('只支持图片文件'));
    }
    
    cb(null, true);
  }
});

// 单文件上传控制器
const uploadPhoto = async (req, res) => {
  try {
    // multer 中间件会将文件添加到 req.file
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未提供文件'
      });
    }
    
    // 获取用户ID（如果已认证）
    const userId = req.user ? req.user.id : null;
    
    // 处理文件上传
    const result = await handleFileUpload(req.file, userId);
    
    if (!result.success) {
      return res.status(500).json({
        code: 500,
        message: '上传失败',
        error: result.error
      });
    }
    
    // 返回上传结果
    res.status(200).json({
      code: 200,
      message: '上传成功',
      data: {
        upload_id: result.upload.id,
        file_url: result.fileUrl
      }
    });
  } catch (error) {
    console.error('文件上传错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

module.exports = {
  uploadMiddleware: upload.single('file'),
  uploadPhoto
}; 