const express = require('express');
const { check } = require('express-validator');
const router = express.Router();
const {
  registerUser,
  loginUser,
  getCurrentUser,
  updateUserProfile,
  updatePassword
} = require('../controllers/userController');
const { protect } = require('../middleware/auth');

// 注册用户
router.post(
  '/register',
  [
    check('username', '用户名是必需的').not().isEmpty(),
    check('username', '用户名长度应在3-20个字符之间').isLength({ min: 3, max: 20 }),
    check('email', '请提供有效的邮箱').isEmail(),
    check('password', '密码长度至少为6个字符').isLength({ min: 6 })
  ],
  registerUser
);

// 用户登录
router.post(
  '/login',
  [
    check('email', '请提供有效的邮箱').isEmail(),
    check('password', '密码是必需的').exists()
  ],
  loginUser
);

// 获取当前用户信息
router.get('/me', protect, getCurrentUser);

// 更新用户信息
router.put(
  '/me',
  [
    protect,
    check('username', '用户名长度应在3-20个字符之间').optional().isLength({ min: 3, max: 20 })
  ],
  updateUserProfile
);

// 更新密码
router.put(
  '/password',
  [
    protect,
    check('currentPassword', '当前密码是必需的').not().isEmpty(),
    check('newPassword', '新密码长度至少为6个字符').isLength({ min: 6 })
  ],
  updatePassword
);

module.exports = router;