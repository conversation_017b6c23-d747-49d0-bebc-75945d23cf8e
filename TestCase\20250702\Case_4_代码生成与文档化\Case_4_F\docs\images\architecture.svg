<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="500" xmlns="http://www.w3.org/2000/svg">
    <!-- 背景 -->
    <rect width="800" height="500" fill="#f9f9f9"/>
    
    <!-- 标题 -->
    <text x="400" y="30" font-family="Arial" font-size="20" text-anchor="middle" font-weight="bold">AI证件照生成平台架构图</text>
    
    <!-- 前端部分 -->
    <rect x="50" y="70" width="180" height="100" rx="10" fill="#91d5ff" stroke="#1890ff" stroke-width="2"/>
    <text x="140" y="110" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">前端应用</text>
    <text x="140" y="135" font-family="Arial" font-size="12" text-anchor="middle">React + Redux + Ant Design</text>
    
    <!-- 后端部分 -->
    <rect x="310" y="70" width="180" height="100" rx="10" fill="#b7eb8f" stroke="#52c41a" stroke-width="2"/>
    <text x="400" y="110" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">后端API服务</text>
    <text x="400" y="135" font-family="Arial" font-size="12" text-anchor="middle">Node.js + Express</text>
    
    <!-- 数据库部分 -->
    <rect x="570" y="70" width="180" height="100" rx="10" fill="#ffd6e7" stroke="#eb2f96" stroke-width="2"/>
    <text x="660" y="110" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">数据库</text>
    <text x="660" y="135" font-family="Arial" font-size="12" text-anchor="middle">MySQL</text>
    
    <!-- 文件存储部分 -->
    <rect x="310" y="240" width="180" height="100" rx="10" fill="#ffe58f" stroke="#faad14" stroke-width="2"/>
    <text x="400" y="280" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">文件存储</text>
    <text x="400" y="305" font-family="Arial" font-size="12" text-anchor="middle">本地/阿里云OSS</text>
    
    <!-- AI模型部分 -->
    <rect x="310" y="380" width="180" height="100" rx="10" fill="#adc6ff" stroke="#2f54eb" stroke-width="2"/>
    <text x="400" y="420" font-family="Arial" font-size="16" text-anchor="middle" font-weight="bold">AI模型服务</text>
    <text x="400" y="445" font-family="Arial" font-size="12" text-anchor="middle">可插拔模型接口</text>
    
    <!-- 连接线 - 前端到后端 -->
    <line x1="230" y1="120" x2="310" y2="120" stroke="#1890ff" stroke-width="2"/>
    <polygon points="303,115 310,120 303,125" fill="#1890ff"/>
    <polygon points="237,115 230,120 237,125" fill="#1890ff"/>
    
    <!-- 连接线 - 后端到数据库 -->
    <line x1="490" y1="120" x2="570" y2="120" stroke="#52c41a" stroke-width="2"/>
    <polygon points="563,115 570,120 563,125" fill="#52c41a"/>
    <polygon points="497,115 490,120 497,125" fill="#52c41a"/>
    
    <!-- 连接线 - 后端到文件存储 -->
    <line x1="400" y1="170" x2="400" y2="240" stroke="#faad14" stroke-width="2"/>
    <polygon points="395,233 400,240 405,233" fill="#faad14"/>
    <polygon points="395,177 400,170 405,177" fill="#faad14"/>
    
    <!-- 连接线 - 文件存储到AI模型 -->
    <line x1="400" y1="340" x2="400" y2="380" stroke="#2f54eb" stroke-width="2"/>
    <polygon points="395,373 400,380 405,373" fill="#2f54eb"/>
    <polygon points="395,347 400,340 405,347" fill="#2f54eb"/>
    
    <!-- 图例 -->
    <rect x="620" y="400" width="15" height="15" fill="#91d5ff" stroke="#1890ff" stroke-width="1"/>
    <text x="640" y="413" font-family="Arial" font-size="12">前端应用</text>
    
    <rect x="620" y="425" width="15" height="15" fill="#b7eb8f" stroke="#52c41a" stroke-width="1"/>
    <text x="640" y="438" font-family="Arial" font-size="12">后端服务</text>
    
    <rect x="620" y="450" width="15" height="15" fill="#ffd6e7" stroke="#eb2f96" stroke-width="1"/>
    <text x="640" y="463" font-family="Arial" font-size="12">数据库</text>
    
    <rect x="700" y="400" width="15" height="15" fill="#ffe58f" stroke="#faad14" stroke-width="1"/>
    <text x="720" y="413" font-family="Arial" font-size="12">文件存储</text>
    
    <rect x="700" y="425" width="15" height="15" fill="#adc6ff" stroke="#2f54eb" stroke-width="1"/>
    <text x="720" y="438" font-family="Arial" font-size="12">AI模型服务</text>
</svg> 