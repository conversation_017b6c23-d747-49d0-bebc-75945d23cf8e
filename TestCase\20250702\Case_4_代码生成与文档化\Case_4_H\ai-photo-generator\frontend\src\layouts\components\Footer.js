import React from 'react';
import { Layout, Row, Col, Typography, Space, Divider } from 'antd';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  GithubOutlined, 
  TwitterOutlined, 
  InstagramOutlined, 
  FacebookOutlined,
  LinkedinOutlined
} from '@ant-design/icons';
import { ROUTES } from '../../utils/constants';

const { Footer: AntFooter } = Layout;
const { Title, Text } = Typography;

/**
 * 页脚样式
 */
const StyledFooter = styled(AntFooter)`
  background: #fff;
  padding: 24px;
  
  @media (max-width: 768px) {
    padding: 16px;
    text-align: center;
  }
`;

/**
 * Logo样式
 */
const Logo = styled.img`
  height: 32px;
  margin-bottom: 16px;
`;

/**
 * 页脚链接样式
 */
const FooterLink = styled(Link)`
  color: rgba(0, 0, 0, 0.65);
  
  &:hover {
    color: #1677ff;
  }
`;

/**
 * 外部链接样式
 */
const ExternalLink = styled.a`
  color: rgba(0, 0, 0, 0.65);
  
  &:hover {
    color: #1677ff;
  }
`;

/**
 * 社交图标样式
 */
const SocialIcon = styled.div`
  font-size: 20px;
  margin-right: 16px;
  
  @media (max-width: 768px) {
    margin: 0 8px;
  }
`;

/**
 * 版权信息样式
 */
const Copyright = styled(Text)`
  display: block;
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
`;

/**
 * 页脚组件
 */
const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <StyledFooter>
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={24} md={8} lg={8} xl={8}>
          <Logo src="/logo.png" alt="AI照片生成器" />
          <Text>AI照片生成器是一款智能证件照生成工具，可以快速生成各种规格的证件照，支持多种背景颜色和尺寸选择。</Text>
          <div style={{ marginTop: 16, display: 'flex' }}>
            <SocialIcon>
              <ExternalLink href="https://github.com" target="_blank" rel="noopener noreferrer">
                <GithubOutlined />
              </ExternalLink>
            </SocialIcon>
            <SocialIcon>
              <ExternalLink href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                <TwitterOutlined />
              </ExternalLink>
            </SocialIcon>
            <SocialIcon>
              <ExternalLink href="https://instagram.com" target="_blank" rel="noopener noreferrer">
                <InstagramOutlined />
              </ExternalLink>
            </SocialIcon>
            <SocialIcon>
              <ExternalLink href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                <FacebookOutlined />
              </ExternalLink>
            </SocialIcon>
            <SocialIcon>
              <ExternalLink href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
                <LinkedinOutlined />
              </ExternalLink>
            </SocialIcon>
          </div>
        </Col>
        
        <Col xs={24} sm={12} md={5} lg={5} xl={5}>
          <Title level={5}>产品</Title>
          <Space direction="vertical">
            <FooterLink to={ROUTES.HOME}>首页</FooterLink>
            <FooterLink to={ROUTES.DASHBOARD}>仪表盘</FooterLink>
            <FooterLink to={ROUTES.PHOTOS}>我的照片</FooterLink>
            <FooterLink to={ROUTES.CREATE_PHOTO}>创建照片</FooterLink>
            <FooterLink to={ROUTES.SUBSCRIPTION}>订阅计划</FooterLink>
          </Space>
        </Col>
        
        <Col xs={24} sm={12} md={5} lg={5} xl={5}>
          <Title level={5}>支持</Title>
          <Space direction="vertical">
            <FooterLink to={ROUTES.HELP}>帮助中心</FooterLink>
            <FooterLink to="/faq">常见问题</FooterLink>
            <FooterLink to="/contact">联系我们</FooterLink>
            <ExternalLink href="https://status.aiphotogenerator.com" target="_blank" rel="noopener noreferrer">
              系统状态
            </ExternalLink>
          </Space>
        </Col>
        
        <Col xs={24} sm={12} md={6} lg={6} xl={6}>
          <Title level={5}>法律</Title>
          <Space direction="vertical">
            <FooterLink to={ROUTES.TERMS}>服务条款</FooterLink>
            <FooterLink to={ROUTES.PRIVACY}>隐私政策</FooterLink>
            <FooterLink to="/cookies">Cookie 政策</FooterLink>
            <FooterLink to="/compliance">合规信息</FooterLink>
          </Space>
        </Col>
      </Row>
      
      <Divider style={{ margin: '24px 0 16px' }} />
      
      <Row justify="space-between" align="middle">
        <Col xs={24} sm={12}>
          <Copyright>© {currentYear} AI照片生成器. 保留所有权利.</Copyright>
        </Col>
        <Col xs={24} sm={12} style={{ textAlign: 'right' }}>
          <Space split={<Divider type="vertical" />} style={{ flexWrap: 'wrap' }}>
            <FooterLink to="/sitemap">网站地图</FooterLink>
            <FooterLink to="/accessibility">无障碍</FooterLink>
            <Text style={{ color: 'rgba(0, 0, 0, 0.45)' }}>版本 1.0.0</Text>
          </Space>
        </Col>
      </Row>
    </StyledFooter>
  );
};

export default Footer;