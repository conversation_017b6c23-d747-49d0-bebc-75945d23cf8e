import { theme } from 'antd';
import { THEME_MODES } from '../utils/constants';

/**
 * 获取主题配置
 * 
 * 根据主题模式返回对应的主题配置
 * 
 * @param {string} mode - 主题模式，可选值：'light', 'dark', 'system'
 * @returns {Object} 主题配置对象
 */
export const getThemeConfig = (mode) => {
  // 基础主题配置
  const baseTheme = {
    token: {
      fontFamily: '"Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      fontSize: 14,
      borderRadius: 6,
    },
    components: {
      Button: {
        borderRadius: 6,
        controlHeight: 40,
      },
      Input: {
        borderRadius: 6,
        controlHeight: 40,
      },
      Select: {
        borderRadius: 6,
        controlHeight: 40,
      },
      Card: {
        borderRadius: 8,
      },
      Modal: {
        borderRadius: 8,
      },
      Drawer: {
        borderRadius: 8,
      },
    },
  };
  
  // 根据主题模式返回对应的主题配置
  switch (mode) {
    case THEME_MODES.DARK:
      return {
        ...baseTheme,
        token: {
          ...baseTheme.token,
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          colorInfo: '#1677ff',
          colorTextBase: 'rgba(255, 255, 255, 0.85)',
          colorBgBase: '#141414',
        },
      };
    case THEME_MODES.LIGHT:
    default:
      return {
        ...baseTheme,
        token: {
          ...baseTheme.token,
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          colorInfo: '#1677ff',
          colorTextBase: 'rgba(0, 0, 0, 0.85)',
          colorBgBase: '#ffffff',
        },
      };
  }
};

/**
 * 创建主题算法
 * 
 * 根据主题模式返回对应的主题算法
 * 
 * @param {string} mode - 主题模式，可选值：'light', 'dark', 'system'
 * @returns {Function[]} 主题算法数组
 */
export const createThemeAlgorithm = (mode) => {
  switch (mode) {
    case THEME_MODES.DARK:
      return [theme.darkAlgorithm, theme.compactAlgorithm];
    case THEME_MODES.LIGHT:
    default:
      return [theme.defaultAlgorithm, theme.compactAlgorithm];
  }
};