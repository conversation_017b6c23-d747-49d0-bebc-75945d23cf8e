import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Row, 
  Col, 
  Card, 
  Typography, 
  Button, 
  Tag, 
  List, 
  Divider, 
  Spin, 
  Alert, 
  Modal, 
  Skeleton,
  Badge,
  Space,
  Tabs
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  CrownOutlined,
  RocketOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { 
  fetchSubscriptionPlans, 
  fetchUserSubscription,
  subscribeToPlan,
  cancelSubscription
} from '../store/subscriptionSlice';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

/**
 * 样式化组件
 */
const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
`;

const PlanCard = styled(StyledCard)`
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  }
  
  &.recommended {
    border: 2px solid ${({ theme }) => theme.colorPrimary};
  }
`;

const PlanHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
`;

const PlanPrice = styled.div`
  text-align: center;
  margin: 24px 0;
  
  .currency {
    font-size: 20px;
    vertical-align: super;
  }
  
  .amount {
    font-size: 48px;
    font-weight: bold;
    line-height: 1;
  }
  
  .period {
    font-size: 16px;
    color: ${({ theme }) => theme.colorTextSecondary};
  }
`;

const PlanFeature = styled(List.Item)`
  padding: 8px 0;
  
  .anticon-check {
    color: ${({ theme }) => theme.colorSuccess};
  }
  
  .anticon-close {
    color: ${({ theme }) => theme.colorError};
  }
`;

const CurrentPlanCard = styled(StyledCard)`
  margin-bottom: 24px;
`;

const CurrentPlanHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const UsageStats = styled.div`
  background-color: ${({ theme }) => theme.colorBgContainer};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid ${({ theme }) => theme.colorBorderSecondary};
`;

const ComparisonTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid ${({ theme }) => theme.colorBorderSecondary};
  }
  
  th {
    background-color: ${({ theme }) => theme.colorBgContainer};
    font-weight: 500;
  }
  
  tr:last-child td {
    border-bottom: none;
  }
  
  .feature-name {
    width: 40%;
  }
  
  .check-icon {
    color: ${({ theme }) => theme.colorSuccess};
  }
  
  .close-icon {
    color: ${({ theme }) => theme.colorError};
  }
`;

/**
 * 订阅页面组件
 * 
 * 显示不同的订阅计划选项，并允许用户升级到高级版
 * 
 * @returns {JSX.Element} 订阅页面组件
 */
const Subscription = () => {
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const { plans, userSubscription, loading, subscribing } = useSelector((state) => state.subscription);
  const { stats } = useSelector((state) => state.dashboard);
  
  // 本地状态
  const [selectedPlanId, setSelectedPlanId] = useState(null);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  
  // 获取订阅计划和用户当前订阅
  useEffect(() => {
    dispatch(fetchSubscriptionPlans());
    dispatch(fetchUserSubscription());
  }, [dispatch]);
  
  // 判断是否有活跃订阅
  const hasActiveSubscription = userSubscription && 
    userSubscription.status === 'active' && 
    userSubscription.planType !== 'free';
  
  // 处理订阅计划选择
  const handleSelectPlan = (planId) => {
    setSelectedPlanId(planId);
    setPaymentModalVisible(true);
  };
  
  // 处理订阅确认
  const handleConfirmSubscription = () => {
    dispatch(subscribeToPlan(selectedPlanId))
      .unwrap()
      .then(() => {
        setPaymentModalVisible(false);
      });
  };
  
  // 处理取消订阅
  const handleCancelSubscription = () => {
    confirm({
      title: '确定要取消订阅吗？',
      icon: <ExclamationCircleOutlined />,
      content: '取消后，您将在当前订阅周期结束后恢复为免费版用户',
      okText: '确定取消',
      okType: 'danger',
      cancelText: '返回',
      onOk() {
        dispatch(cancelSubscription());
      }
    });
  };
  
  // 获取当前计划
  const getCurrentPlan = () => {
    if (!userSubscription) return null;
    
    if (userSubscription.planType === 'free') {
      return { name: '免费版', price: 0 };
    }
    
    return plans.find(plan => plan.id === userSubscription.planId) || 
      { name: userSubscription.planType, price: userSubscription.price };
  };
  
  // 渲染当前订阅信息
  const renderCurrentSubscription = () => {
    if (loading || !userSubscription) {
      return <Skeleton active paragraph={{ rows: 4 }} />;
    }
    
    const currentPlan = getCurrentPlan();
    
    return (
      <CurrentPlanCard>
        <CurrentPlanHeader>
          <div>
            <Title level={4}>当前订阅</Title>
            <Space>
              <Text strong>{currentPlan?.name || '免费版'}</Text>
              {hasActiveSubscription && (
                <Tag color="green">已激活</Tag>
              )}
            </Space>
          </div>
          {hasActiveSubscription && (
            <Button 
              danger 
              onClick={handleCancelSubscription}
            >
              取消订阅
            </Button>
          )}
        </CurrentPlanHeader>
        
        {hasActiveSubscription ? (
          <>
            <Paragraph>
              <InfoCircleOutlined /> 您的订阅将于 {new Date(userSubscription.expiresAt).toLocaleDateString()} 续费
            </Paragraph>
            <Paragraph>
              <InfoCircleOutlined /> 剩余天数: {userSubscription.daysRemaining} 天
            </Paragraph>
          </>
        ) : (
          <Paragraph>
            <InfoCircleOutlined /> 您当前使用的是免费版本，升级到高级版以获取更多功能和更高的使用限制。
          </Paragraph>
        )}
        
        <UsageStats>
          <Title level={5}>本月使用情况</Title>
          <Paragraph>
            已使用 {stats?.usageCount || 0}/{stats?.usageLimit || 0} 张照片
            {hasActiveSubscription ? ' (无限制)' : ''}
          </Paragraph>
        </UsageStats>
      </CurrentPlanCard>
    );
  };
  
  // 渲染订阅计划
  const renderSubscriptionPlans = () => {
    if (loading || !plans || plans.length === 0) {
      return (
        <Row gutter={24}>
          {[1, 2, 3].map(i => (
            <Col xs={24} md={8} key={i} style={{ marginBottom: 24 }}>
              <StyledCard>
                <Skeleton active paragraph={{ rows: 6 }} />
              </StyledCard>
            </Col>
          ))}
        </Row>
      );
    }
    
    return (
      <Row gutter={24}>
        {plans.map(plan => {
          const isCurrentPlan = userSubscription && userSubscription.planId === plan.id;
          const isRecommended = plan.recommended;
          
          return (
            <Col xs={24} md={8} key={plan.id} style={{ marginBottom: 24 }}>
              <Badge.Ribbon 
                text="推荐" 
                color="gold" 
                style={{ display: isRecommended ? 'block' : 'none' }}
              >
                <PlanCard 
                  className={isRecommended ? 'recommended' : ''}
                  title={
                    <PlanHeader>
                      <Title level={3}>{plan.name}</Title>
                      <Text type="secondary">{plan.description}</Text>
                    </PlanHeader>
                  }
                >
                  <PlanPrice>
                    <span className="currency">¥</span>
                    <span className="amount">{plan.price}</span>
                    <span className="period">/{plan.interval}</span>
                  </PlanPrice>
                  
                  <List
                    dataSource={plan.features}
                    renderItem={feature => (
                      <PlanFeature>
                        <Space>
                          <CheckOutlined />
                          <span>{feature}</span>
                        </Space>
                      </PlanFeature>
                    )}
                  />
                  
                  <Divider />
                  
                  <div style={{ textAlign: 'center' }}>
                    {isCurrentPlan ? (
                      <Button type="default" disabled block>
                        当前方案
                      </Button>
                    ) : (
                      <Button 
                        type={isRecommended ? "primary" : "default"} 
                        block
                        onClick={() => handleSelectPlan(plan.id)}
                      >
                        {hasActiveSubscription ? '切换方案' : '选择方案'}
                      </Button>
                    )}
                  </div>
                </PlanCard>
              </Badge.Ribbon>
            </Col>
          );
        })}
      </Row>
    );
  };
  
  // 渲染计划比较表
  const renderPlanComparison = () => {
    const features = [
      { name: '每月照片生成数量', free: '20张', pro: '无限制', premium: '无限制' },
      { name: '高质量生成', free: false, pro: true, premium: true },
      { name: '参考图片上传', free: false, pro: true, premium: true },
      { name: '优先处理队列', free: false, pro: false, premium: true },
      { name: '高级风格选项', free: false, pro: true, premium: true },
      { name: '批量生成', free: false, pro: false, premium: true },
      { name: '无水印下载', free: false, pro: true, premium: true },
      { name: '高级编辑功能', free: false, pro: false, premium: true },
    ];
    
    return (
      <div style={{ overflowX: 'auto' }}>
        <ComparisonTable>
          <thead>
            <tr>
              <th className="feature-name">功能</th>
              <th>免费版</th>
              <th>专业版</th>
              <th>高级版</th>
            </tr>
          </thead>
          <tbody>
            {features.map((feature, index) => (
              <tr key={index}>
                <td className="feature-name">{feature.name}</td>
                <td>
                  {typeof feature.free === 'boolean' ? (
                    feature.free ? <CheckOutlined className="check-icon" /> : <CloseOutlined className="close-icon" />
                  ) : (
                    feature.free
                  )}
                </td>
                <td>
                  {typeof feature.pro === 'boolean' ? (
                    feature.pro ? <CheckOutlined className="check-icon" /> : <CloseOutlined className="close-icon" />
                  ) : (
                    feature.pro
                  )}
                </td>
                <td>
                  {typeof feature.premium === 'boolean' ? (
                    feature.premium ? <CheckOutlined className="check-icon" /> : <CloseOutlined className="close-icon" />
                  ) : (
                    feature.premium
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </ComparisonTable>
      </div>
    );
  };
  
  return (
    <div>
      <Title level={2}>订阅计划</Title>
      <Paragraph>选择适合您需求的订阅计划，随时升级或降级。</Paragraph>
      
      {/* 当前订阅信息 */}
      {renderCurrentSubscription()}
      
      {/* 订阅计划选项卡 */}
      <Tabs defaultActiveKey="plans">
        <TabPane 
          tab={
            <span>
              <CrownOutlined /> 订阅方案
            </span>
          } 
          key="plans"
        >
          {renderSubscriptionPlans()}
        </TabPane>
        <TabPane 
          tab={
            <span>
              <RocketOutlined /> 功能比较
            </span>
          } 
          key="comparison"
        >
          {renderPlanComparison()}
        </TabPane>
      </Tabs>
      
      {/* 常见问题 */}
      <div style={{ marginTop: 48 }}>
        <Title level={3}>常见问题</Title>
        <List
          itemLayout="vertical"
          dataSource={[
            {
              question: '如何更改我的订阅计划？',
              answer: '您可以随时在此页面选择新的订阅计划。如果您升级，新计划将立即生效，并按比例收取费用。如果您降级，新计划将在当前订阅周期结束