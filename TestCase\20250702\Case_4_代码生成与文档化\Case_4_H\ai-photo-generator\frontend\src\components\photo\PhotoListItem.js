import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { 
  List, 
  Typography, 
  Space, 
  Tag, 
  Button, 
  Dropdown, 
  Checkbox, 
  Avatar,
  Tooltip,
  Modal,
  message
} from 'antd';
import { 
  EditOutlined, 
  DownloadOutlined, 
  EllipsisOutlined, 
  DeleteOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { downloadPhoto, deletePhoto, toggleFavorite } from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS } from '../../utils/constants';
import ShareModal from './ShareModal';

const { Text } = Typography;
const { confirm } = Modal;

/**
 * 照片列表项组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.photo - 照片对象
 * @param {boolean} props.selectable - 是否可选择
 * @param {boolean} props.selected - 是否已选择
 * @param {Function} props.onSelect - 选择回调
 * @param {Function} props.onDelete - 删除回调
 * @returns {React.ReactNode} 渲染的组件
 */
const PhotoListItem = ({ photo, selectable = false, selected = false, onSelect, onDelete }) => {
  const dispatch = useDispatch();
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  if (!photo) return null;
  
  // 获取照片规格信息
  const specInfo = PHOTO_SPECS[photo.spec] || { name: '未知规格', width: '-', height: '-' };
  
  /**
   * 处理下载照片
   * 
   * @param {Event} e - 事件对象
   */
  const handleDownload = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    setDownloadLoading(true);
    try {
      await dispatch(downloadPhoto(photo.id)).unwrap();
      message.success('照片下载成功');
    } catch (err) {
      message.error('下载失败: ' + err.message);
    } finally {
      setDownloadLoading(false);
    }
  };
  
  /**
   * 处理删除照片
   * 
   * @param {Event} e - 事件对象
   */
  const handleDelete = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这张照片吗？此操作无法撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deletePhoto(photo.id)).unwrap();
          message.success('照片删除成功');
          if (onDelete) {
            onDelete(photo.id);
          }
        } catch (err) {
          message.error('删除失败: ' + err.message);
        }
      },
    });
  };
  
  /**
   * 处理收藏/取消收藏
   * 
   * @param {Event} e - 事件对象
   */
  const handleToggleFavorite = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      await dispatch(toggleFavorite(photo.id)).unwrap();
      message.success(photo.isFavorite ? '已取消收藏' : '已添加到收藏');
    } catch (err) {
      message.error('操作失败: ' + err.message);
    }
  };
  
  /**
   * 处理分享
   * 
   * @param {Event} e - 事件对象
   */
  const handleShare = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShareModalVisible(true);
  };
  
  /**
   * 处理选择
   * 
   * @param {Event} e - 事件对象
   */
  const handleSelect = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onSelect) {
      onSelect(!selected);
    }
  };
  
  // 下拉菜单项
  const dropdownItems = [
    {
      key: 'edit',
      label: (
        <Link to={`${ROUTES.EDIT_PHOTO}/${photo.id}`}>
          <EditOutlined /> 编辑照片
        </Link>
      ),
    },
    {
      key: 'download',
      label: (
        <a onClick={handleDownload}>
          <DownloadOutlined /> 下载照片
        </a>
      ),
    },
    {
      key: 'share',
      label: (
        <a onClick={handleShare}>
          <ShareAltOutlined /> 分享照片
        </a>
      ),
    },
    {
      key: 'favorite',
      label: (
        <a onClick={handleToggleFavorite}>
          {photo.isFavorite ? (
            <>
              <StarFilled style={{ color: '#faad14' }} /> 取消收藏
            </>
          ) : (
            <>
              <StarOutlined /> 添加收藏
            </>
          )}
        </a>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      danger: true,
      label: (
        <a onClick={handleDelete}>
          <DeleteOutlined /> 删除照片
        </a>
      ),
    },
  ];
  
  return (
    <List.Item
      className={`photo-list-item ${selected ? 'selected' : ''}`}
      actions={[
        <Tooltip title="编辑" key="edit">
          <Button 
            type="text" 
            icon={<EditOutlined />}
            onClick={(e) => {
              e.preventDefault();
              window.location.href = `${ROUTES.EDIT_PHOTO}/${photo.id}`;
            }}
          />
        </Tooltip>,
        <Tooltip title="下载" key="download">
          <Button 
            type="text" 
            icon={<DownloadOutlined />} 
            onClick={handleDownload}
            loading={downloadLoading}
          />
        </Tooltip>,
        <Tooltip title={photo.isFavorite ? "取消收藏" : "收藏"} key="favorite">
          <Button 
            type="text" 
            icon={photo.isFavorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />} 
            onClick={handleToggleFavorite}
          />
        </Tooltip>,
        <Dropdown menu={{ items: dropdownItems }} placement="bottomRight" key="more">
          <Button type="text" icon={<EllipsisOutlined />} />
        </Dropdown>,
      ]}
    >
      <div className="photo-list-item-content" style={{ display: 'flex', alignItems: 'center' }}>
        {selectable && (
          <div className="photo-list-item-checkbox" onClick={handleSelect} style={{ marginRight: 16 }}>
            <Checkbox checked={selected} onChange={(e) => onSelect(e.target.checked)} />
          </div>
        )}
        
        <div className="photo-list-item-avatar" style={{ marginRight: 16 }}>
          <Link to={`${ROUTES.PHOTO_RESULT}/${photo.id}`}>
            <Avatar 
              shape="square" 
              size={64} 
              src={photo.thumbnailUrl || photo.resultUrl} 
              alt={photo.name || '证件照'}
            />
          </Link>
        </div>
        
        <div className="photo-list-item-details" style={{ flex: 1 }}>
          <div className="photo-list-item-title">
            <Link to={`${ROUTES.PHOTO_RESULT}/${photo.id}`}>
              <Text strong>{photo.name || '未命名照片'}</Text>
            </Link>
          </div>
          
          <div className="photo-list-item-info" style={{ marginTop: 4 }}>
            <Space size={[0, 4]} wrap>
              <Tag>{specInfo.name}</Tag>
              <Tag color={photo.status === 'completed' ? 'success' : 'processing'}>
                {photo.status === 'completed' ? '已完成' : '处理中'}
              </Tag>
              <Text type="secondary">
                {new Date(photo.createdAt).toLocaleDateString()}
              </Text>
            </Space>
          </div>
          
          <div className="photo-list-item-specs" style={{ marginTop: 4 }}>
            <Text type="secondary">
              {specInfo.width} × {specInfo.height} mm | {photo.backgroundColor || '白色'} 背景
            </Text>
          </div>
        </div>
      </div>
      
      {/* 分享模态框 */}
      <ShareModal
        visible={shareModalVisible}
        photo={photo}
        onClose={() => setShareModalVisible(false)}
      />
    </List.Item>
  );
};

PhotoListItem.propTypes = {
  photo: PropTypes.object.isRequired,
  selectable: PropTypes.bool,
  selected: PropTypes.bool,
  onSelect: PropTypes.func,
  onDelete: PropTypes.func,
};

export default PhotoListItem;