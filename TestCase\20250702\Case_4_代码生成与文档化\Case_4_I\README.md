# AI证件照生成平台

基于AI技术的在线证件照生成平台，用户只需上传个人照片，即可通过大模型技术自动生成符合标准的证件照。本平台采用前后端分离架构，提供用户友好的界面和高效的证件照生成服务。

## 项目结构

```
├── frontend/                # 前端React应用
│   ├── public/              # 静态资源
│   └── src/                 # 源代码
│       ├── components/      # 组件
│       │   ├── AppHeader/   # 应用头部组件
│       │   ├── AppFooter/   # 应用底部组件
│       │   ├── PhotoUploader/ # 照片上传组件
│       │   └── PhotoPreview/ # 照片预览组件
│       ├── pages/           # 页面
│       │   ├── HomePage/    # 首页
│       │   ├── UploadPage/  # 上传页面
│       │   ├── ResultPage/  # 结果页面
│       │   ├── LoginPage/   # 登录页面
│       │   ├── RegisterPage/ # 注册页面
│       │   └── UserProfilePage/ # 用户资料页面
│       ├── services/        # Redux服务
│       │   ├── userSlice.js # 用户状态管理
│       │   ├── uploadSlice.js # 上传状态管理
│       │   └── generationSlice.js # 生成状态管理
│       ├── utils/           # 工具函数
│       │   └── api.js       # API请求封装
│       ├── App.js           # 主应用
│       ├── App.scss         # 应用样式
│       ├── index.js         # 应用入口文件
│       └── store.js         # Redux存储配置
├── backend/                 # 后端Express应用
│   ├── config/              # 配置文件
│   │   └── db.js           # 数据库配置
│   ├── controllers/         # 控制器
│   │   ├── userController.js # 用户控制器
│   │   ├── uploadController.js # 上传控制器
│   │   └── generationController.js # 生成控制器
│   ├── middleware/          # 中间件
│   │   ├── auth.js         # 认证中间件
│   │   └── upload.js       # 上传中间件
│   ├── models/              # 数据模型
│   │   ├── User.js         # 用户模型
│   │   └── PhotoTask.js    # 照片任务模型
│   ├── routes/              # 路由
│   │   ├── userRoutes.js   # 用户路由
│   │   ├── uploadRoutes.js # 上传路由
│   │   └── generationRoutes.js # 生成路由
│   ├── utils/               # 工具函数
│   │   └── errorHandler.js # 错误处理
│   ├── uploads/             # 上传文件存储
│   ├── .env                 # 环境变量
│   └── server.js            # 服务器入口
├── docs/                    # 文档
│   ├── API文档.md           # API接口文档
│   ├── 数据库设计.md        # 数据库设计文档
│   ├── 部署指南.md          # 部署指南
│   ├── 用户使用手册.md      # 用户使用手册
│   └── 技术架构.md          # 技术架构文档
└── README.md                # 项目说明
```

## 技术栈

### 前端
- React 18
- Redux Toolkit
- React Router 6
- Ant Design 5
- Axios
- SCSS

### 后端
- Node.js 14+ (Express 4)
- MongoDB 4.4+
- Mongoose 7
- JWT认证
- Multer (文件上传)
- Sharp (图像处理)

## 功能特点

- **用户认证**：注册、登录、个人信息管理
- **照片上传**：支持JPG、PNG、JPEG格式，单张照片大小限制10MB以内，支持单张和批量上传
- **AI证件照生成**：自动人脸检测和定位，背景替换，头部位置和比例调整
- **多种证件照规格**：支持1寸、2寸、小2寸、大1寸等多种规格
- **照片预览和选择**：网格布局展示多张生成照片，支持预览和选择
- **照片下载**：支持单张下载和批量下载，提供高清版本
- **积分系统**：基于积分的使用限制和计费

## 安装与运行

### 前提条件

- Node.js 14+
- MongoDB 4.4+
- npm 或 yarn

### 前端

```bash
cd frontend
npm install
npm start
```

### 后端

```bash
cd backend
npm install
# 配置.env文件
npm run dev
```

## 数据库设置

本项目使用MongoDB作为数据库，无需手动创建表结构。系统会自动创建以下集合：

- `users` - 存储用户信息
- `phototasks` - 存储照片处理任务

详细的数据库设计请参考[数据库设计](./docs/数据库设计.md)文档。

## 文档

- [API文档](./docs/API文档.md) - API接口详细说明
- [数据库设计](./docs/数据库设计.md) - 数据库结构和关系
- [部署指南](./docs/部署指南.md) - 生产环境部署步骤
- [用户使用手册](./docs/用户使用手册.md) - 平台使用说明
- [技术架构](./docs/技术架构.md) - 系统技术架构

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

MIT