# AI证件照生成平台 API文档

## 目录

1. [API概述](#1-api概述)
2. [认证机制](#2-认证机制)
3. [API端点](#3-api端点)
   - [认证API](#31-认证api)
   - [上传API](#32-上传api)
   - [照片处理API](#33-照片处理api)
   - [用户API](#34-用户api)
4. [错误处理](#4-错误处理)
5. [数据模型](#5-数据模型)
6. [API限流](#6-api限流)
7. [示例代码](#7-示例代码)

## 1. API概述

AI证件照生成平台API是一组RESTful接口，允许开发者集成证件照生成功能到自己的应用中。API使用JSON格式进行数据交换，使用JWT（JSON Web Token）进行认证。

### 基本信息

- **基础URL**: `https://api.example.com`
- **API版本**: v1
- **内容类型**: `application/json`（除非特别说明）

## 2. 认证机制

API使用基于JWT的认证机制。客户端需要在请求头中包含有效的JWT令牌。

### 获取令牌

通过登录API获取JWT令牌：

```
POST /api/auth/login
```

### 使用令牌

在后续请求中，将令牌添加到请求头：

```
x-auth-token: <your_jwt_token>
```

### 令牌有效期

令牌默认有效期为24小时。过期后需要重新登录获取新令牌。

## 3. API端点

### 3.1 认证API

#### 3.1.1 注册用户

创建新用户账户。

- **URL**: `/api/auth/register`
- **方法**: `POST`
- **认证**: 不需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                 |
|-----------|--------|------|----------------------|
| username  | string | 是   | 用户名（3-50个字符） |
| email     | string | 是   | 电子邮箱地址         |
| password  | string | 是   | 密码（至少6个字符）  |
| phone     | string | 是   | 手机号码             |

**请求示例**:

```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "13800138000"
}
```

**成功响应** (201 Created):

```json
{
  "message": "注册成功",
  "user": {
    "id": 1,
    "username": "user123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "credits": 10
  }
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `409 Conflict`: 用户名或邮箱已存在

#### 3.1.2 用户登录

用户登录并获取JWT令牌。

- **URL**: `/api/auth/login`
- **方法**: `POST`
- **认证**: 不需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名    | 类型   | 必填 | 描述               |
|-----------|--------|------|-------------------|
| username  | string | 是   | 用户名或电子邮箱   |
| password  | string | 是   | 密码              |

**请求示例**:

```json
{
  "username": "user123",
  "password": "password123"
}
```

**成功响应** (200 OK):

```json
{
  "message": "登录成功",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "user123",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "credits": 10
  }
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 用户名或密码错误

### 3.2 上传API

#### 3.2.1 上传单张照片

上传单张照片用于生成证件照。

- **URL**: `/api/upload`
- **方法**: `POST`
- **认证**: 需要
- **内容类型**: `multipart/form-data`

**请求参数**:

| 参数名 | 类型   | 必填 | 描述                                |
|--------|--------|------|-------------------------------------|
| file   | file   | 是   | 照片文件（JPG、PNG、JPEG，最大10MB） |

**成功响应** (200 OK):

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "upload_id": "unique_upload_id",
    "file_url": "https://api.example.com/uploads/original/xxx.jpg",
    "original_filename": "photo.jpg",
    "file_size": 1024000,
    "file_type": "image/jpeg"
  }
}
```

**错误响应**:

- `400 Bad Request`: 文件格式不支持或文件大小超限
- `401 Unauthorized`: 未授权
- `500 Internal Server Error`: 服务器错误

#### 3.2.2 批量上传照片

批量上传多张照片（最多5张）。

- **URL**: `/api/upload/batch`
- **方法**: `POST`
- **认证**: 需要
- **内容类型**: `multipart/form-data`

**请求参数**:

| 参数名 | 类型       | 必填 | 描述                                |
|--------|------------|------|-------------------------------------|
| files  | file array | 是   | 照片文件数组（最多5张，每张最大10MB） |

**成功响应** (200 OK):

```json
{
  "code": 200,
  "message": "批量上传成功",
  "data": {
    "upload_id": "unique_upload_id",
    "files": [
      {
        "file_url": "https://api.example.com/uploads/original/xxx1.jpg",
        "original_filename": "photo1.jpg",
        "file_size": 1024000,
        "file_type": "image/jpeg"
      },
      {
        "file_url": "https://api.example.com/uploads/original/xxx2.jpg",
        "original_filename": "photo2.jpg",
        "file_size": 1048576,
        "file_type": "image/jpeg"
      }
    ],
    "total_count": 2
  }
}
```

**错误响应**:

- `400 Bad Request`: 文件格式不支持、文件大小超限或文件数量超限
- `401 Unauthorized`: 未授权
- `500 Internal Server Error`: 服务器错误

### 3.3 照片处理API

#### 3.3.1 生成证件照

根据上传的照片生成证件照。

- **URL**: `/api/photo/generate`
- **方法**: `POST`
- **认证**: 需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名       | 类型   | 必填 | 描述                                |
|--------------|--------|------|-------------------------------------|
| upload_id    | string | 是   | 上传ID                              |
| spec         | string | 是   | 证件照规格（1inch, 2inch, small2inch, large1inch） |
| background   | string | 是   | 背景颜色（blue, red, white）        |
| count        | number | 否   | 生成数量，默认5                     |
| model_config | object | 否   | 模型配置参数                        |

**请求示例**:

```json
{
  "upload_id": "unique_upload_id",
  "spec": "1inch",
  "background": "blue",
  "count": 5,
  "model_config": {
    "model_name": "default_model",
    "parameters": {
      "quality": "high",
      "style": "formal"
    }
  }
}
```

**成功响应** (200 OK):

```json
{
  "code": 200,
  "message": "生成任务已创建",
  "data": {
    "task_id": "unique_task_id",
    "status": "processing",
    "generated_photos": []
  }
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未授权
- `404 Not Found`: 上传ID不存在
- `500 Internal Server Error`: 服务器错误

#### 3.3.2 获取任务状态

获取证件照生成任务的状态和结果。

- **URL**: `/api/photo/task/:taskId`
- **方法**: `GET`
- **认证**: 需要

**路径参数**:

| 参数名  | 类型   | 描述    |
|---------|--------|---------|
| taskId  | string | 任务ID  |

**成功响应** (200 OK):

```json
{
  "code": 200,
  "data": {
    "task_id": "unique_task_id",
    "status": "completed",
    "progress": 100,
    "spec": "1inch",
    "background": "blue",
    "created_at": "2024-07-01T10:30:00Z",
    "completed_at": "2024-07-01T10:30:30Z",
    "generated_photos": [
      {
        "id": "photo_1",
        "url": "/uploads/generated/photo_1.jpg",
        "quality_score": 0.95
      },
      {
        "id": "photo_2",
        "url": "/uploads/generated/photo_2.jpg",
        "quality_score": 0.92
      }
    ]
  }
}
```

**错误响应**:

- `401 Unauthorized`: 未授权
- `404 Not Found`: 任务ID不存在
- `500 Internal Server Error`: 服务器错误

#### 3.3.3 下载照片

请求下载选中的照片。

- **URL**: `/api/photo/download`
- **方法**: `POST`
- **认证**: 需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名     | 类型     | 必填 | 描述           |
|------------|----------|------|----------------|
| photo_ids  | string[] | 是   | 照片ID数组     |

**请求示例**:

```json
{
  "photo_ids": ["photo_1", "photo_2"]
}
```

**成功响应** (200 OK):

```json
{
  "code": 200,
  "message": "下载请求成功",
  "data": {
    "download_id": "unique_download_id",
    "photos": [
      {
        "id": "photo_1",
        "url": "/uploads/generated/photo_1.jpg",
        "download_url": "/api/photo/download/photo_1"
      },
      {
        "id": "photo_2",
        "url": "/uploads/generated/photo_2.jpg",
        "download_url": "/api/photo/download/photo_2"
      }
    ],
    "total_count": 2
  }
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未授权
- `404 Not Found`: 照片ID不存在
- `500 Internal Server Error`: 服务器错误

#### 3.3.4 下载单张照片

下载单张照片。

- **URL**: `/api/photo/download/:photoId`
- **方法**: `GET`
- **认证**: 需要

**路径参数**:

| 参数名   | 类型   | 描述    |
|----------|--------|---------|
| photoId  | string | 照片ID  |

**成功响应**:

- 照片文件流（实际项目中）
- 或JSON响应（当前模拟实现）:

```json
{
  "code": 200,
  "message": "照片下载成功",
  "data": {
    "photo_id": "photo_1",
    "url": "/uploads/generated/photo_1.jpg"
  }
}
```

**错误响应**:

- `401 Unauthorized`: 未授权
- `404 Not Found`: 照片ID不存在
- `500 Internal Server Error`: 服务器错误

### 3.4 用户API

#### 3.4.1 获取用户个人资料

获取当前登录用户的个人资料。

- **URL**: `/api/user/profile`
- **方法**: `GET`
- **认证**: 需要

**成功响应** (200 OK):

```json
{
  "id": 1,
  "username": "user123",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "credits": 10,
  "createdAt": "2024-01-15T08:30:00Z",
  "lastLogin": "2024-07-01T14:30:22Z"
}
```

**错误响应**:

- `401 Unauthorized`: 未授权
- `404 Not Found`: 用户不存在
- `500 Internal Server Error`: 服务器错误

#### 3.4.2 更新用户个人资料

更新当前登录用户的个人资料。

- **URL**: `/api/user/profile`
- **方法**: `PUT`
- **认证**: 需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                 |
|-----------|--------|------|----------------------|
| username  | string | 否   | 新用户名             |
| email     | string | 否   | 新电子邮箱地址       |
| phone     | string | 否   | 新手机号码           |

**请求示例**:

```json
{
  "username": "new_username",
  "email": "<EMAIL>",
  "phone": "13900139000"
}
```

**成功响应** (200 OK):

```json
{
  "message": "个人资料更新成功",
  "user": {
    "id": 1,
    "username": "new_username",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "credits": 10
  }
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未授权
- `404 Not Found`: 用户不存在
- `500 Internal Server Error`: 服务器错误

#### 3.4.3 修改密码

修改当前登录用户的密码。

- **URL**: `/api/user/password`
- **方法**: `PUT`
- **认证**: 需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名           | 类型   | 必填 | 描述                 |
|------------------|--------|------|----------------------|
| currentPassword  | string | 是   | 当前密码             |
| newPassword      | string | 是   | 新密码（至少6个字符）|

**请求示例**:

```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

**成功响应** (200 OK):

```json
{
  "message": "密码修改成功"
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效或当前密码错误
- `401 Unauthorized`: 未授权
- `404 Not Found`: 用户不存在
- `500 Internal Server Error`: 服务器错误

#### 3.4.4 获取用户历史记录

获取当前登录用户的使用历史记录。

- **URL**: `/api/user/history`
- **方法**: `GET`
- **认证**: 需要

**成功响应** (200 OK):

```json
{
  "history": [
    {
      "id": "history_1",
      "taskId": "task_1",
      "spec": "1inch",
      "background": "blue",
      "status": "completed",
      "createdAt": "2024-07-01T10:30:00Z",
      "completedAt": "2024-07-01T10:30:30Z",
      "photoCount": 5
    },
    {
      "id": "history_2",
      "taskId": "task_2",
      "spec": "2inch",
      "background": "red",
      "status": "completed",
      "createdAt": "2024-06-30T15:20:00Z",
      "completedAt": "2024-06-30T15:20:45Z",
      "photoCount": 3
    }
  ]
}
```

**错误响应**:

- `401 Unauthorized`: 未授权
- `500 Internal Server Error`: 服务器错误

#### 3.4.5 购买积分

购买积分用于生成证件照。

- **URL**: `/api/user/credits/purchase`
- **方法**: `POST`
- **认证**: 需要
- **内容类型**: `application/json`

**请求参数**:

| 参数名 | 类型   | 必填 | 描述           |
|--------|--------|------|----------------|
| amount | number | 是   | 购买积分数量   |

**请求示例**:

```json
{
  "amount": 50
}
```

**成功响应** (200 OK):

```json
{
  "message": "积分购买成功",
  "credits": 60
}
```

**错误响应**:

- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未授权
- `404 Not Found`: 用户不存在
- `500 Internal Server Error`: 服务器错误

## 4. 错误处理

API使用标准HTTP状态码表示请求结果。常见的状态码包括：

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数无效
- `401 Unauthorized`: 未授权（未提供令牌或令牌无效）
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突（如用户名已存在）
- `429 Too Many Requests`: 请求过于频繁
- `500 Internal Server Error`: 服务器错误

错误响应格式：

```json
{
  "message": "错误描述",
  "errors": [
    {
      "field": "字段名",
      "message": "具体错误信息"
    }
  ]
}
```

## 5. 数据模型

### 5.1 用户模型

```json
{
  "id": 1,
  "username": "user123",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "credits": 10,
  "createdAt": "2024-01-15T08:30:00Z",
  "lastLogin": "2024-07-01T14:30:22Z"
}
```

### 5.2 上传模型

```json
{
  "upload_id": "unique_upload_id",
  "user_id": 1,
  "original_filename": "photo.jpg",
  "file_path": "/uploads/original/xxx.jpg",
  "file_size": 1024000,
  "file_type": "image/jpeg",
  "upload_time": "2024-07-01T10:30:00Z"
}
```

### 5.3 任务模型

```json
{
  "id": "unique_task_id",
  "upload_id": "unique_upload_id",
  "spec": "1inch",
  "background": "blue",
  "status": "completed",
  "created_at": "2024-07-01T10:30:00Z",
  "completed_at": "2024-07-01T10:30:30Z"
}
```

### 5.4 照片模型

```json
{
  "id": "photo_1",
  "task_id": "unique_task_id",
  "file_path": "/uploads/generated/photo_1.jpg",
  "quality_score": 0.95,
  "created_at": "2024-07-01T10:30:30Z"
}
```

## 6. API限流

为了防止滥用，API实施了请求限制：

- 认证API: 每IP每分钟10次请求
- 上传API: 每用户每分钟5次请求
- 照片处理API: 每用户每分钟10次请求
- 用户API: 每用户每分钟20次请求

超过限制将返回`429 Too Many Requests`状态码。

## 7. 示例代码

### 7.1 JavaScript (Axios)

```javascript
// 登录示例
async function login(username, password) {
  try {
    const response = await axios.post('https://api.example.com/api/auth/login', {
      username,
      password
    });
    
    // 保存令牌
    localStorage.setItem('token', response.data.token);
    
    return response.data.user;
  } catch (error) {
    console.error('登录失败:', error.response?.data || error.message);
    throw error;
  }
}

// 上传照片示例
async function uploadPhoto(file) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post('https://api.example.com/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'x-auth-token': localStorage.getItem('token')
      }
    });
    
    return response.data.data;
  } catch (error) {
    console.error('上传失败:', error.response?.data || error.message);
    throw error;
  }
}

// 生成证件照示例
async function generatePhoto(uploadId, spec, background) {
  try {
    const response = await axios.post('https://api.example.com/api/photo/generate', {
      upload_id: uploadId,
      spec,
      background,
      count: 5
    }, {
      headers: {
        'x-auth-token': localStorage.getItem('token')
      }
    });
    
    return response.data.data;
  } catch (error) {
    console.error('生成失败:', error.response?.data || error.message);
    throw error;
  }
}

// 获取任务状态示例
async function getTaskStatus(taskId) {
  try {
    const response = await axios.get(`https://api.example.com/api/photo/task/${taskId}`, {
      headers: {
        'x-auth-token': localStorage.getItem('token')
      }
    });
    
    return response.data.data;
  } catch (error) {
    console.error('获取任务状态失败:', error.response?.data || error.message);
    throw error;
  }
}
```

### 7.2 Python (Requests)

```python
import requests
import json

# 基础URL
BASE_URL = 'https://api.example.com/api'
token = None

# 登录示例
def login(username, password):
    global token
    try:
        response = requests.post(f'{BASE_URL}/auth/login', json={
            'username': username,
            'password': password
        })
        response.raise_for_status()
        data = response.json()
        token = data['token']
        return data['user']
    except requests.exceptions.RequestException as e:
        print(f'登录失败: {e}')
        raise

# 上传照片示例
def upload_photo(file_path):
    try:
        with open(file_path, 'rb') as f:
            files = {'file': f}
            headers = {'x-auth-token': token} if token else {}
            response = requests.post(f'{BASE_URL}/upload', files=files, headers=headers)
        response.raise_for_status()
        return response.json()['data']
    except requests.exceptions.RequestException as e:
        print(f'上传失败: {e}')
        raise

# 生成证件照示例
def generate_photo(upload_id, spec, background):
    try:
        headers = {'x-auth-token': token} if token else {}
        response = requests.post(f'{BASE_URL}/photo/generate', json={
            'upload_id': upload_id,
            'spec': spec,
            'background': background,
            'count': 5
        }, headers=headers)
        response.raise_for_status()
        return response.json()['data']
    except requests.exceptions.RequestException as e:
        print(f'生成失败: {e}')
        raise

# 获取任务状态示例
def get_task_status(task_id):
    try:
        headers = {'x-auth-token': token} if token else {}
        response = requests.get(f'{BASE_URL}/photo/task/{task_id}', headers=headers)
        response.raise_for_status()
        return response.json()['data']
    except requests.exceptions.RequestException as e:
        print(f'获取任务状态失败: {e}')
        raise
```

---

本文档详细描述了AI证件照生成平台的API接口。如有任何问题或需要进一步的帮助，请联系API支持团队：<EMAIL>。

**文档版本**: v1.0  
**最后更新**: 2024年7月