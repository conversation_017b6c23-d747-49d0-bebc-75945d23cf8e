.app-footer {
  background-color: #f5f5f5;
  padding: 40px 24px 24px;
  margin-top: 40px;
  
  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .footer-title {
    margin-bottom: 20px;
  }
  
  .footer-description {
    opacity: 0.7;
    display: block;
    margin-bottom: 16px;
  }
  
  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      margin-bottom: 12px;
      
      a {
        color: rgba(0, 0, 0, 0.65);
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
  
  .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    
    .ant-space-split {
      .ant-divider {
        background-color: rgba(0, 0, 0, 0.2);
      }
    }
    
    a {
      color: rgba(0, 0, 0, 0.65);
      
      &:hover {
        color: #1890ff;
      }
    }
    
    .copyright {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .app-footer {
    padding: 24px 16px 16px;
    
    .footer-bottom {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
} 