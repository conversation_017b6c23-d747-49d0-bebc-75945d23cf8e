import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Tabs,
  Empty,
  Spin,
  message,
  Pagination,
  Select,
  Input,
  Dropdown,
  Menu,
  Modal,
  Tag,
} from 'antd';
import {
  PlusOutlined,
  AppstoreOutlined,
  BarsOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { getUserPhotos, deletePhoto, toggleFavorite, batchDeletePhotos } from '../services/photoService';
import { setCurrentPhoto } from '../store/photoSlice';
import { addNotification } from '../store/uiSlice';
import { ROUTES, PHOTO_TYPES } from '../utils/constants';
import PhotoCard from '../components/gallery/PhotoCard';
import PhotoListItem from '../components/gallery/PhotoListItem';
import LoadingSpinner from '../components/common/LoadingSpinner';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search } = Input;

const Gallery = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // 本地状态
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' 或 'list'
  const [activeTab, setActiveTab] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
  });
  const [filters, setFilters] = useState({
    type: 'all',
    search: '',
    sort: 'newest',
  });
  const [selectedPhotos, setSelectedPhotos] = useState([]);
  const [selectMode, setSelectMode] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [photoToDelete, setPhotoToDelete] = useState(null);
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false);
  
  // 加载照片数据
  useEffect(() => {
    fetchPhotos();
  }, [activeTab, pagination.current, filters]);
  
  // 获取照片列表
  const fetchPhotos = async () => {
    try {
      setLoading(true);
      
      // 构建查询参数
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        type: filters.type !== 'all' ? filters.type : undefined,
        search: filters.search || undefined,
        sort: filters.sort,
        favorite: activeTab === 'favorites' ? true : undefined,
      };
      
      // 调用API获取照片列表
      const response = await getUserPhotos(params);
      
      setPhotos(response.photos);
      setPagination({
        ...pagination,
        total: response.total,
      });
    } catch (error) {
      console.error('获取照片失败:', error);
      message.error('获取照片失败，请重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setPagination({ ...pagination, current: 1 });
    setSelectedPhotos([]);
    setSelectMode(false);
  };
  
  // 处理页码变化
  const handlePageChange = (page, pageSize) => {
    setPagination({ ...pagination, current: page, pageSize });
    setSelectedPhotos([]);
    setSelectMode(false);
  };
  
  // 处理视图模式切换
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };
  
  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    setFilters({ ...filters, [key]: value });
    setPagination({ ...pagination, current: 1 });
    setSelectedPhotos([]);
    setSelectMode(false);
  };
  
  // 处理搜索
  const handleSearch = (value) => {
    handleFilterChange('search', value);
  };
  
  // 处理照片点击
  const handlePhotoClick = (photo) => {
    if (selectMode) {
      togglePhotoSelection(photo.id);
    } else {
      dispatch(setCurrentPhoto(photo));
      navigate(`${ROUTES.RESULT}/${photo.id}`);
    }
  };
  
  // 处理照片选择
  const togglePhotoSelection = (photoId) => {
    if (selectedPhotos.includes(photoId)) {
      setSelectedPhotos(selectedPhotos.filter(id => id !== photoId));
    } else {
      setSelectedPhotos([...selectedPhotos, photoId]);
    }
  };
  
  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedPhotos.length === photos.length) {
      setSelectedPhotos([]);
    } else {
      setSelectedPhotos(photos.map(photo => photo.id));
    }
  };
  
  // 进入/退出选择模式
  const toggleSelectMode = () => {
    setSelectMode(!selectMode);
    if (selectMode) {
      setSelectedPhotos([]);
    }
  };
  
  // 处理收藏切换
  const handleToggleFavorite = async (photoId) => {
    try {
      const updatedPhoto = await toggleFavorite(photoId);
      
      // 更新本地状态
      setPhotos(photos.map(photo => 
        photo.id === photoId ? { ...photo, isFavorite: updatedPhoto.isFavorite } : photo
      ));
      
      const message = updatedPhoto.isFavorite ? '已添加到收藏' : '已从收藏中移除';
      dispatch(addNotification({
        type: 'success',
        title: '收藏状态更新',
        message,
      }));
    } catch (error) {
      console.error('更新收藏状态失败:', error);
      message.error('操作失败，请重试');
    }
  };
  
  // 处理删除单张照片
  const handleDeletePhoto = async () => {
    if (!photoToDelete) return;
    
    try {
      await deletePhoto(photoToDelete);
      
      // 更新本地状态
      setPhotos(photos.filter(photo => photo.id !== photoToDelete));
      setPagination({
        ...pagination,
        total: pagination.total - 1,
      });
      
      dispatch(addNotification({
        type: 'success',
        title: '删除成功',
        message: '照片已成功删除',
      }));
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    } finally {
      setDeleteModalVisible(false);
      setPhotoToDelete(null);
    }
  };
  
  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedPhotos.length === 0) return;
    
    try {
      await batchDeletePhotos(selectedPhotos);
      
      // 更新本地状态
      setPhotos(photos.filter(photo => !selectedPhotos.includes(photo.id)));
      setPagination({
        ...pagination,
        total: pagination.total - selectedPhotos.length,
      });
      
      dispatch(addNotification({
        type: 'success',
        title: '批量删除成功',
        message: `已成功删除 ${selectedPhotos.length} 张照片`,
      }));
      
      // 重置选择状态
      setSelectedPhotos([]);
      setSelectMode(false);
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败，请重试');
    } finally {
      setBatchDeleteModalVisible(false);
    }
  };
  
  // 渲染工具栏
  const renderToolbar = () => (
    <div className="gallery-toolbar">
      <div className="toolbar-left">
        {selectMode ? (
          <Space>
            <Button 
              onClick={handleSelectAll}
              type={selectedPhotos.length === photos.length ? 'primary' : 'default'}
            >
              {selectedPhotos.length === photos.length ? '取消全选' : '全选'}
            </Button>
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => setBatchDeleteModalVisible(true)}
              disabled={selectedPhotos.length === 0}
            >
              删除所选 ({selectedPhotos.length})
            </Button>
            <Button onClick={toggleSelectMode}>取消</Button>
          </Space>
        ) : (
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => navigate(ROUTES.UPLOAD)}
            >
              上传照片
            </Button>
            <Button onClick={toggleSelectMode}>选择</Button>
          </Space>
        )}
      </div>
      
      <div className="toolbar-right">
        <Space>
          <Search
            placeholder="搜索照片"
            allowClear
            onSearch={handleSearch}
            style={{ width: 200 }}
          />
          
          <Select
            value={filters.type}
            onChange={(value) => handleFilterChange('type', value)}
            style={{ width: 120 }}
          >
            <Option value="all">所有类型</Option>
            {PHOTO_TYPES.map(type => (
              <Option key={type.value} value={type.value}>
                {type.label}
              </Option>
            ))}
          </Select>
          
          <Select
            value={filters.sort}
            onChange={(value) => handleFilterChange('sort', value)}
            style={{ width: 120 }}
          >
            <Option value="newest">最新上传</Option>
            <Option value="oldest">最早上传</Option>
            <Option value="name_asc">名称 A-Z</Option>
            <Option value="name_desc">名称 Z-A</Option>
          </Select>
          
          <div className="view-mode-toggle">
            <Button.Group>
              <Button
                type={viewMode === 'grid' ? 'primary' : 'default'}
                icon={<AppstoreOutlined />}
                onClick={() => handleViewModeChange('grid')}
              />
              <Button
                type={viewMode === 'list' ? 'primary' : 'default'}
                icon={<BarsOutlined />}
                onClick={() => handleViewModeChange('list')}
              />
            </Button.Group>
          </div>
        </Space>
      </div>
    </div>
  );
  
  // 渲染照片列表
  const renderPhotoList = () => {
    if (loading) {
      return <LoadingSpinner tip="正在加载照片..." />;
    }
    
    if (photos.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <p>暂无照片</p>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => navigate(ROUTES.UPLOAD)}
              >
                上传照片
              </Button>
            </div>
          }
        />
      );
    }
    
    if (viewMode === 'grid') {
      return (
        <Row gutter={[16, 16]} className="photo-grid">
          {photos.map(photo => (
            <Col xs={24} sm={12} md={8} lg={6} key={photo.id}>
              <PhotoCard
                photo={photo}
                selected={selectedPhotos.includes(photo.id)}
                selectMode={selectMode}
                onClick={() => handlePhotoClick(photo)}
                onToggleFavorite={() => handleToggleFavorite(photo.id)}
                onDelete={() => {
                  setPhotoToDelete(photo.id);
                  setDeleteModalVisible(true);
                }}
                onEdit={() => navigate(`${ROUTES.EDIT}/${photo.id}`)}
              />
            </Col>
          ))}
        </Row>
      );
    } else {
      return (
        <div className="photo-list">
          {photos.map(photo => (
            <PhotoListItem
              key={photo.id}
              photo={photo}
              selected={selectedPhotos.includes(photo.id)}
              selectMode={selectMode}
              onClick={() => handlePhotoClick(photo)}
              onToggleFavorite={() => handleToggleFavorite(photo.id)}
              onDelete={() => {
                setPhotoToDelete(photo.id);
                setDeleteModalVisible(true);
              }}
              onEdit={() => navigate(`${ROUTES.EDIT}/${photo.id}`)}
            />
          ))}
        </div>
      );
    }
  };
  
  return (
    <div className="gallery-page">
      <div className="gallery-header">
        <Title level={2}>我的照片库</Title>
        <Paragraph>管理您的所有证件照</Paragraph>
      </div>
      
      <Card className="gallery-card">
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="所有照片" key="all" />
          <TabPane tab="收藏" key="favorites" />
        </Tabs>
        
        {renderToolbar()}
        
        <div className="gallery-content">
          {renderPhotoList()}
        </div>
        
        {photos.length > 0 && (
          <div className="gallery-pagination">
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onChange={handlePageChange}
              showSizeChanger
              showTotal={(total) => `共 ${total} 张照片`}
            />
          </div>
        )}
      </Card>
      
      {/* 删除确认模态框 */}
      <Modal
        title="确认删除"
        visible={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        onOk={handleDeletePhoto}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除这张照片吗？此操作无法撤销。</p>
      </Modal>
      
      {/* 批量删除确认模态框 */}
      <Modal
        title="确认批量删除"
        visible={batchDeleteModalVisible}
        onCancel={() => setBatchDeleteModalVisible(false)}
        onOk={handleBatchDelete}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除选中的 {selectedPhotos.length} 张照片吗？此操作无法撤销。</p>
      </Modal>
    </div>
  );
};

export default Gallery;