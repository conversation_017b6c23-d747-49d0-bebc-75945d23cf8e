import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Form, 
  Input, 
  Button, 
  Checkbox, 
  Typography, 
  Divider, 
  message, 
  Space 
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  GoogleOutlined, 
  FacebookOutlined 
} from '@ant-design/icons';
import styled from 'styled-components';
import { login } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const { Title, Text } = Typography;

/**
 * 样式化组件
 */
const FormHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
`;

const SocialLoginButtons = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const SocialButton = styled(Button)`
  width: 48%;
`;

const FormFooter = styled.div`
  text-align: center;
  margin-top: 24px;
`;

/**
 * 登录页面组件
 * 
 * @returns {JSX.Element} 登录页面组件
 */
const Login = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从Redux获取状态
  const { loading } = useSelector((state) => state.auth);
  
  // 本地状态
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(true);
  
  // 获取重定向路径，如果有的话
  const from = location.state?.from?.pathname || ROUTES.DASHBOARD;
  
  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      await dispatch(login({ 
        email: values.email, 
        password: values.password,
        rememberMe
      })).unwrap();
      
      message.success('登录成功');
      navigate(from, { replace: true });
    } catch (error) {
      message.error(error?.message || '登录失败，请检查您的凭据');
    }
  };
  
  // 处理社交登录
  const handleSocialLogin = (provider) => {
    message.info(`${provider}登录功能即将推出`);
  };
  
  return (
    <>
      <FormHeader>
        <Title level={2}>欢迎回来</Title>
        <Text type="secondary">登录您的账户以继续</Text>
      </FormHeader>
      
      <SocialLoginButtons>
        <SocialButton 
          icon={<GoogleOutlined />} 
          onClick={() => handleSocialLogin('Google')}
        >
          Google登录
        </SocialButton>
        <SocialButton 
          icon={<FacebookOutlined />} 
          onClick={() => handleSocialLogin('Facebook')}
        >
          Facebook登录
        </SocialButton>
      </SocialLoginButtons>
      
      <Divider plain>或使用邮箱登录</Divider>
      
      <Form
        form={form}
        name="login"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="email"
          rules={[
            { required: true, message: '请输入您的邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Input 
            prefix={<UserOutlined />} 
            placeholder="邮箱" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入您的密码' },
            { min: 6, message: '密码长度不能少于6个字符' }
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />} 
            placeholder="密码" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Checkbox 
              checked={rememberMe} 
              onChange={(e) => setRememberMe(e.target.checked)}
            >
              记住我
            </Checkbox>
            <Link to={ROUTES.FORGOT_PASSWORD}>忘记密码？</Link>
          </div>
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            size="large" 
            block 
            loading={loading}
          >
            登录
          </Button>
        </Form.Item>
      </Form>
      
      <FormFooter>
        <Space>
          <Text type="secondary">还没有账户？</Text>
          <Link to={ROUTES.REGISTER}>立即注册</Link>
        </Space>
      </FormFooter>
    </>
  );
};

export default Login;