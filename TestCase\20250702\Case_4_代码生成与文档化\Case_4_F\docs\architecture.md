# AI证件照生成平台 架构设计文档

## 系统架构概述

AI证件照生成平台采用前后端分离的架构设计，由以下几个部分组成：

1. **前端应用** - 基于React.js的单页面应用
2. **后端服务** - 基于Node.js Express的RESTful API服务
3. **数据库** - MySQL关系型数据库
4. **文件存储** - 本地文件系统/阿里云OSS
5. **AI模型服务** - 可插拔的AI模型接口

## 系统架构图

```
+-------------------+      +-------------------+      +-------------------+
|                   |      |                   |      |                   |
|   Browser/Client  |<---->|   Frontend App    |<---->|    Backend API    |
|                   |      |   (React.js)      |      |    (Express)      |
|                   |      |                   |      |                   |
+-------------------+      +-------------------+      +--------+----------+
                                                               |
                                                               |
                                                               v
                              +-------------------+    +-------+----------+
                              |                   |    |                  |
                              |   File Storage    |<-->|     Database     |
                              | (Local/Ali OSS)   |    |     (MySQL)      |
                              |                   |    |                  |
                              +-------------------+    +------------------+
                                        ^
                                        |
                                        v
                              +-------------------+
                              |                   |
                              |    AI Model       |
                              |    Service        |
                              |                   |
                              +-------------------+
```

## 组件详细设计

### 1. 前端应用

前端应用是一个基于React.js的单页面应用，使用TypeScript开发，主要组件包括：

- **组件库**: 使用Ant Design组件库
- **状态管理**: 使用Redux管理全局状态
- **路由**: 使用React Router管理页面路由
- **API通信**: 使用Axios库进行HTTP请求

**主要模块**:

- **用户认证模块**: 管理用户登录、注册、权限验证
- **照片上传模块**: 处理照片上传、预览、规格选择
- **证件照生成模块**: 处理生成任务、结果展示、下载
- **用户中心模块**: 管理用户信息、使用记录

**页面结构**:

- 首页 - 展示产品介绍、功能说明
- 上传页面 - 照片上传、规格选择
- 结果页面 - 展示生成的证件照、下载
- 用户中心 - 用户信息、历史记录
- 登录/注册页面 - 用户认证

### 2. 后端服务

后端服务是一个基于Node.js Express框架的RESTful API服务，使用以下技术：

- **ORM**: 使用Sequelize进行数据库操作
- **认证**: 使用JWT (JSON Web Token) 进行身份验证
- **文件处理**: 使用Multer处理文件上传
- **验证**: 使用express-validator进行请求验证

**主要模块**:

- **认证模块**: 处理用户注册、登录、权限校验
- **上传模块**: 处理照片上传、存储
- **生成模块**: 处理证件照生成任务、结果管理
- **用户模块**: 处理用户信息、历史记录

**主要API端点**:

- /api/auth/* - 认证相关接口
- /api/upload - 照片上传接口
- /api/generate - 证件照生成接口
- /api/task/* - 任务状态查询接口
- /api/download - 照片下载接口
- /api/user/* - 用户信息接口

### 3. 数据库

系统使用MySQL关系型数据库存储结构化数据，主要表结构包括：

- **users** - 存储用户信息
- **uploads** - 存储上传的照片记录
- **generation_tasks** - 存储生成任务记录
- **generated_photos** - 存储生成的照片记录

详细数据库设计请参考 [数据库设计文档](database.md)。

### 4. 文件存储

系统支持两种文件存储模式：

- **本地存储** (开发环境):
  - 上传的原始照片存储在 `/public/uploads/` 目录
  - 生成的证件照存储在 `/public/generated/` 目录
  - 下载的ZIP包存储在 `/public/downloads/` 目录

- **阿里云OSS存储** (生产环境):
  - 使用阿里云对象存储服务
  - 按照不同的用途划分不同的目录
  - 通过签名URL进行访问控制

### 5. AI模型服务

系统设计了一个可插拔的AI模型接口，支持多种AI模型的集成：

- **模型接口定义**:
  ```typescript
  interface AIModelInterface {
    modelName: string;
    version: string;
    generatePhoto(input: {
      imageUrl: string;
      spec: PhotoSpec;
      background: BackgroundColor;
      options?: any;
    }): Promise<GeneratedPhoto[]>;
    healthCheck(): Promise<boolean>;
  }
  ```

- **支持多种模型配置**:
  ```json
  {
    "models": [
      {
        "name": "default_model",
        "endpoint": "https://api.example.com/generate",
        "api_key": "your_api_key"
      },
      {
        "name": "custom_model_v1",
        "endpoint": "https://custom-api.example.com/process"
      }
    ]
  }
  ```

## 安全性设计

1. **用户认证与授权**:
   - 使用JWT进行身份验证
   - 密码使用bcrypt进行哈希存储
   - 基于角色的权限控制

2. **API安全**:
   - 使用HTTPS加密传输
   - 实现请求限流和防爆破机制
   - 添加CSRF保护

3. **数据安全**:
   - 敏感数据加密存储
   - 定期数据备份
   - 数据访问权限控制

4. **文件安全**:
   - 上传文件类型验证和大小限制
   - 文件存储访问控制
   - 定期清理临时文件

## 扩展性设计

1. **微服务架构支持**:
   - 模块化设计，便于未来拆分为微服务
   - 接口标准化，支持服务间通信

2. **水平扩展**:
   - 无状态API设计，支持负载均衡
   - 使用Redis等支持分布式缓存
   - 数据库主从复制、读写分离

3. **AI模型扩展**:
   - 插件式AI模型接口
   - 支持多模型并行或级联处理
   - 支持模型的动态配置和切换

## 部署架构

### 开发环境

- 前端: `http://localhost:8080` (Vite开发服务器)
- 后端: `http://localhost:3000` (Node.js Express服务)
- 数据库: 本地MySQL实例
- 文件存储: 本地文件系统

### 生产环境

- 前端: 静态文件部署在CDN
- 后端: 部署在云服务器或容器平台
- 数据库: 云数据库服务
- 文件存储: 阿里云OSS
- 负载均衡: 使用Nginx或云服务商提供的负载均衡服务

```
                  +----------------+
                  |                |
                  |   CDN (前端)   |
                  |                |
                  +--------+-------+
                           |
                           v
+--------------+    +------+-------+    +---------------+
|              |    |              |    |               |
|  负载均衡器   +--->+  API服务集群  +--->+  数据库服务    |
|              |    |              |    |               |
+--------------+    +------+-------+    +---------------+
                           |
                           v
                    +------+-------+
                    |              |
                    |   对象存储    |
                    |              |
                    +--------------+
```

## 监控和日志

1. **应用监控**:
   - 使用PM2进行进程管理和监控
   - 实现健康检查端点
   - 监控API响应时间和错误率

2. **日志管理**:
   - 使用Winston进行日志记录
   - 按照不同级别分类日志
   - 定期日志轮转和归档

3. **告警机制**:
   - 关键错误实时告警
   - 性能指标阈值告警
   - 资源使用率监控 