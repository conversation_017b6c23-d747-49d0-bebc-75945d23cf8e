# AI证件照生成平台

<div align="center">

![AI证件照生成平台](https://img.shields.io/badge/AI%E8%AF%81%E4%BB%B6%E7%85%A7-v1.0.0-blue)
![Node.js](https://img.shields.io/badge/Node.js-16%2B-green)
![React](https://img.shields.io/badge/React-18-blue)
![MySQL](https://img.shields.io/badge/MySQL-8.0-orange)
![License](https://img.shields.io/badge/License-MIT-yellow)

**利用先进的AI技术，快速生成高质量标准证件照**

[在线演示](https://demo.ai-photo.com) · [API文档](docs/API.md) · [部署指南](docs/DEPLOYMENT.md) · [开发文档](docs/DEVELOPMENT.md)

</div>

## ✨ 特性

- 🤖 **AI智能处理** - 采用先进的AI技术，自动识别人脸，智能调整比例和光线
- ⚡ **快速生成** - 上传照片后60秒内即可生成多张高质量证件照
- 🔒 **隐私安全** - 照片加密存储，定期自动清理，保护用户隐私安全
- 📏 **标准规格** - 支持1寸、2寸等多种标准规格，满足各种证件需求
- 🎨 **多种背景** - 支持蓝色、红色、白色等多种背景颜色选择
- 📱 **响应式设计** - 完美适配桌面端和移动端设备
- 🔄 **批量处理** - 支持批量上传和处理多张照片
- 📊 **实时进度** - 实时显示处理进度和状态更新

## 🚀 快速开始

### 环境要求

- Node.js 16.0+
- MySQL 8.0+
- Redis 6.0+
- 可选：Docker & Docker Compose

### 一键启动（推荐）

```bash
# 克隆项目
git clone https://github.com/your-repo/ai-id-photo-platform.git
cd ai-id-photo-platform

# 使用启动脚本
chmod +x scripts/start.sh
./scripts/start.sh dev
```

### Docker 部署

```bash
# 复制环境配置
cp .env.example .env

# 编辑配置文件
nano .env

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 手动安装

<details>
<summary>点击展开手动安装步骤</summary>

#### 1. 安装依赖

```bash
# 后端依赖
cd backend
npm install

# 前端依赖
cd ../frontend
npm install
```

#### 2. 数据库配置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ai_photo_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 初始化数据库
mysql -u root -p ai_photo_platform < database/init.sql
```

#### 3. 环境配置

```bash
# 后端配置
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件

# 前端配置
echo "VITE_API_BASE_URL=http://localhost:3000/api" > frontend/.env.development
```

#### 4. 启动服务

```bash
# 启动后端 (终端1)
cd backend
npm run dev

# 启动前端 (终端2)
cd frontend
npm run dev
```

</details>

## 📖 使用说明

### 基本流程

1. **上传照片** - 选择一张清晰的个人照片上传
2. **选择规格** - 选择需要的证件照规格和背景颜色
3. **AI处理** - AI自动处理，生成标准证件照
4. **下载使用** - 预览选择满意的照片并下载

### 支持的规格

| 规格 | 尺寸 | 用途 |
|------|------|------|
| 1寸 | 25mm × 35mm | 身份证、学生证等 |
| 2寸 | 35mm × 49mm | 护照、驾驶证等 |
| 小2寸 | 33mm × 48mm | 港澳通行证等 |
| 大1寸 | 33mm × 45mm | 部分证件 |
| 自定义 | 用户定义 | 特殊需求 |

### 支持的背景色

- 🔵 蓝色 - 最常用的证件照背景
- 🔴 红色 - 部分证件要求
- ⚪ 白色 - 简洁清爽
- 🔷 浅蓝色 - 柔和选择
- ⬜ 浅灰色 - 中性背景

## 🏗️ 技术架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │   AI服务接口     │
│                 │    │                 │    │                 │
│ - React 18      │◄──►│ - Express       │◄──►│ - 自定义AI模型   │
│ - Ant Design    │    │ - Sequelize     │    │ - 第三方AI API   │
│ - Redux Toolkit │    │ - JWT Auth      │    │                 │
│ - Vite          │    │ - Multer        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   数据存储层     │
                       │                 │
                       │ - MySQL 8.0     │
                       │ - Redis 6.0     │
                       │ - 文件存储       │
                       └─────────────────┘
```

### 技术栈

**前端技术栈:**
- React 18 + TypeScript
- Ant Design UI组件库
- Redux Toolkit 状态管理
- Vite 构建工具
- React Router 路由管理

**后端技术栈:**
- Node.js + Express
- Sequelize ORM
- MySQL 数据库
- Redis 缓存
- JWT 身份认证
- Multer 文件上传

**DevOps:**
- Docker & Docker Compose
- Nginx 反向代理
- PM2 进程管理
- GitHub Actions CI/CD

## 📁 项目结构

```
ai-id-photo-platform/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由
│   │   ├── services/        # 业务服务
│   │   ├── middleware/      # 中间件
│   │   └── utils/           # 工具函数
│   ├── uploads/             # 上传文件
│   └── package.json
├── frontend/                # 前端应用
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── pages/           # 页面
│   │   ├── services/        # API服务
│   │   ├── store/           # 状态管理
│   │   └── utils/           # 工具函数
│   └── package.json
├── database/                # 数据库
│   ├── init.sql             # 初始化脚本
│   ├── migrations/          # 迁移脚本
│   └── seeds/               # 种子数据
├── docs/                    # 文档
│   ├── API.md               # API文档
│   ├── DEPLOYMENT.md        # 部署文档
│   └── DEVELOPMENT.md       # 开发文档
├── scripts/                 # 脚本
│   ├── start.sh             # 启动脚本
│   └── stop.sh              # 停止脚本
├── docker-compose.yml       # Docker编排
└── README.md                # 项目说明
```

## 🔧 配置说明

### 环境变量

主要环境变量配置：

```bash
# 基础配置
NODE_ENV=production
PORT=3000
FRONTEND_URL=http://localhost

# 数据库配置
DB_HOST=localhost
DB_NAME=ai_photo_platform
DB_USER=your_user
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=7d

# AI模型配置
DEFAULT_AI_ENDPOINT=https://api.example.com/generate
DEFAULT_AI_API_KEY=your-ai-api-key
```

完整配置请参考 [.env.example](.env.example) 文件。

## 📚 文档

- [API文档](docs/API.md) - 详细的API接口说明
- [部署文档](docs/DEPLOYMENT.md) - 生产环境部署指南
- [开发文档](docs/DEVELOPMENT.md) - 开发环境搭建和代码规范

## 🧪 测试

```bash
# 后端测试
cd backend
npm test

# 前端测试
cd frontend
npm test

# 测试覆盖率
npm run test:coverage
```

## 📈 性能

- **响应时间**: 平均处理时间 < 60秒
- **并发处理**: 支持100+并发用户
- **文件大小**: 支持最大10MB图片
- **存储优化**: 自动压缩和清理机制

## 🔐 安全

- JWT身份认证
- 文件类型验证
- 大小限制检查
- SQL注入防护
- XSS攻击防护
- HTTPS加密传输

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。

## 🙏 致谢

- [React](https://reactjs.org/) - 用户界面库
- [Ant Design](https://ant.design/) - 企业级UI设计语言
- [Express](https://expressjs.com/) - Web应用框架
- [Sequelize](https://sequelize.org/) - ORM框架
- [Sharp](https://sharp.pixelplumbing.com/) - 图像处理库

## 📞 联系我们

- 项目主页: [https://github.com/your-repo/ai-id-photo-platform](https://github.com/your-repo/ai-id-photo-platform)
- 问题反馈: [Issues](https://github.com/your-repo/ai-id-photo-platform/issues)
- 邮箱: <EMAIL>

---

<div align="center">

**如果这个项目对你有帮助，请给它一个 ⭐️**

Made with ❤️ by AI Photo Platform Team

</div>
