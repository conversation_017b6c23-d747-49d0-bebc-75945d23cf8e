import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Card, 
  Typography, 
  Button, 
  Statistic, 
  Progress, 
  List, 
  Avatar, 
  Empty, 
  Skeleton,
  Tag,
  Space
} from 'antd';
import {
  PictureOutlined,
  HeartOutlined,
  ClockCircleOutlined,
  PlusOutlined,
  CrownOutlined,
  RocketOutlined,
  FireOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { fetchDashboardData } from '../store/dashboardSlice';
import { fetchRecentPhotos } from '../store/photoSlice';
import { ROUTES } from '../utils/constants';

const { Title, Text, Paragraph } = Typography;

/**
 * 样式化组件
 */
const StyledCard = styled(Card)`
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  .ant-card-head {
    border-bottom: 1px solid ${({ theme }) => theme.colorBorderSecondary};
  }
`;

const WelcomeCard = styled(StyledCard)`
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  
  .ant-card-head {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .ant-card-head-title {
    color: white;
  }
  
  .ant-typography {
    color: white;
  }
`;

const StatsCard = styled(StyledCard)`
  text-align: center;
  
  .ant-statistic-title {
    font-size: 14px;
  }
  
  .ant-statistic-content {
    font-size: 24px;
  }
`;

const PhotoItem = styled.div`
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${({ theme }) => theme.colorBorderSecondary};
  
  &:last-child {
    border-bottom: none;
  }
`;

const PhotoImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
`;

const PhotoInfo = styled.div`
  flex: 1;
`;

const PhotoActions = styled.div`
  display: flex;
  align-items: center;
`;

const UsageProgressCard = styled(StyledCard)`
  .ant-progress-text {
    font-weight: 500;
  }
`;

/**
 * 仪表盘页面组件
 * 
 * 显示用户的使用统计和最近生成的照片等信息
 * 
 * @returns {JSX.Element} 仪表盘页面组件
 */
const Dashboard = () => {
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const { user } = useSelector((state) => state.auth);
  const { stats, loading: dashboardLoading } = useSelector((state) => state.dashboard);
  const { recentPhotos, loading: photosLoading } = useSelector((state) => state.photo);
  const { userSubscription } = useSelector((state) => state.subscription);
  
  // 获取仪表盘数据和最近照片
  useEffect(() => {
    dispatch(fetchDashboardData());
    dispatch(fetchRecentPhotos({ limit: 5 }));
  }, [dispatch]);
  
  // 判断是否有高级订阅
  const hasPremiumSubscription = userSubscription && 
    userSubscription.status === 'active' && 
    userSubscription.planType !== 'free';
  
  // 计算使用额度百分比
  const usagePercentage = stats?.usageLimit 
    ? Math.round((stats.usageCount / stats.usageLimit) * 100) 
    : 0;
  
  // 获取使用额度进度条状态
  const getProgressStatus = (percentage) => {
    if (percentage >= 90) return 'exception';
    if (percentage >= 70) return 'warning';
    return 'normal';
  };
  
  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* 欢迎卡片 */}
        <Col xs={24} lg={16}>
          <WelcomeCard bordered={false}>
            <Title level={2} style={{ color: 'white', marginBottom: 8 }}>
              欢迎回来，{user?.name || '用户'}！
            </Title>
            <Paragraph style={{ color: 'rgba(255, 255, 255, 0.85)', marginBottom: 24 }}>
              使用AI照片生成器创建令人惊叹的照片，展示您的创意。
            </Paragraph>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                size="large"
                ghost
                onClick={() => window.location.href = ROUTES.CREATE_PHOTO}
              >
                创建新照片
              </Button>
              {!hasPremiumSubscription && (
                <Button 
                  icon={<CrownOutlined />} 
                  size="large"
                  onClick={() => window.location.href = ROUTES.SUBSCRIPTION}
                >
                  升级到高级版
                </Button>
              )}
            </Space>
          </WelcomeCard>
        </Col>
        
        {/* 使用额度卡片 */}
        <Col xs={24} lg={8}>
          <UsageProgressCard 
            title="本月使用额度" 
            extra={hasPremiumSubscription ? <Tag color="gold">高级版</Tag> : null}
          >
            <Skeleton loading={dashboardLoading} active paragraph={{ rows: 2 }}>
              <Progress 
                percent={usagePercentage} 
                status={getProgressStatus(usagePercentage)}
                strokeWidth={10}
                style={{ marginBottom: 16 }}
              />
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text>已使用: {stats?.usageCount || 0}</Text>
                <Text>总额度: {stats?.usageLimit || 0}</Text>
              </div>
              {usagePercentage >= 80 && !hasPremiumSubscription && (
                <div style={{ marginTop: 16 }}>
                  <Button 
                    type="primary" 
                    icon={<CrownOutlined />} 
                    block
                    onClick={() => window.location.href = ROUTES.SUBSCRIPTION}
                  >
                    升级获取更多额度
                  </Button>
                </div>
              )}
            </Skeleton>
          </UsageProgressCard>
        </Col>
      </Row>
      
      {/* 统计数据 */}
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24} sm={8}>
          <StatsCard>
            <Skeleton loading={dashboardLoading} active paragraph={{ rows: 1 }}>
              <Statistic 
                title="已创建照片" 
                value={stats?.totalPhotos || 0} 
                prefix={<PictureOutlined />} 
              />
            </Skeleton>
          </StatsCard>
        </Col>
        <Col xs={24} sm={8}>
          <StatsCard>
            <Skeleton loading={dashboardLoading} active paragraph={{ rows: 1 }}>
              <Statistic 
                title="收藏照片" 
                value={stats?.favoritePhotos || 0} 
                prefix={<HeartOutlined />} 
              />
            </Skeleton>
          </StatsCard>
        </Col>
        <Col xs={24} sm={8}>
          <StatsCard>
            <Skeleton loading={dashboardLoading} active paragraph={{ rows: 1 }}>
              <Statistic 
                title="剩余天数" 
                value={userSubscription?.daysRemaining || 0} 
                prefix={<ClockCircleOutlined />} 
                suffix={hasPremiumSubscription ? "天" : "（免费版）"} 
              />
            </Skeleton>
          </StatsCard>
        </Col>
      </Row>
      
      {/* 最近照片和热门提示 */}
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        {/* 最近照片 */}
        <Col xs={24} lg={16}>
          <StyledCard 
            title="最近创建的照片" 
            extra={<Link to={ROUTES.PHOTOS}>查看全部</Link>}
          >
            <Skeleton loading={photosLoading} active paragraph={{ rows: 5 }}>
              {recentPhotos && recentPhotos.length > 0 ? (
                recentPhotos.map((photo) => (
                  <PhotoItem key={photo.id}>
                    <PhotoImage src={photo.thumbnailUrl} alt={photo.prompt} />
                    <PhotoInfo>
                      <Text strong>{photo.prompt}</Text>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          创建于 {new Date(photo.createdAt).toLocaleDateString()}
                        </Text>
                      </div>
                    </PhotoInfo>
                    <PhotoActions>
                      <Link to={`${ROUTES.PHOTOS}/${photo.id}`}>
                        <Button type="link">查看</Button>
                      </Link>
                    </PhotoActions>
                  </PhotoItem>
                ))
              ) : (
                <Empty 
                  description="暂无照片" 
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={() => window.location.href = ROUTES.CREATE_PHOTO}
                  >
                    创建第一张照片
                  </Button>
                </Empty>
              )}
            </Skeleton>
          </StyledCard>
        </Col>
        
        {/* 热门提示 */}
        <Col xs={24} lg={8}>
          <StyledCard title="热门提示">
            <Skeleton loading={dashboardLoading} active paragraph={{ rows: 5 }}>
              <List
                itemLayout="horizontal"
                dataSource={[
                  {
                    title: '专业人像',
                    description: '创建专业商务人像照片，适合LinkedIn或简历',
                    icon: <RocketOutlined style={{ color: '#1890ff' }} />,
                  },
                  {
                    title: '旅行照片',
                    description: '在世界著名景点的逼真旅行照片',
                    icon: <FireOutlined style={{ color: '#fa8c16' }} />,
                  },
                  {
                    title: '创意艺术照',
                    description: '独特风格的艺术照片，展现个性',
                    icon: <FireOutlined style={{ color: '#fa8c16' }} />,
                  },
                  {
                    title: '产品展示',
                    description: '专业的产品展示照片，适合电商使用',
                    icon: <RocketOutlined style={{ color: '#1890ff' }} />,
                  },
                ]}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={item.icon} />}
                      title={item.title}
                      description={item.description}
                    />
                    <Button 
                      type="link" 
                      onClick={() => window.location.href = `${ROUTES.CREATE_PHOTO}?prompt=${encodeURIComponent(item.title)}`}
                    >
                      使用
                    </Button>
                  </List.Item>
                )}
              />
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Link to={ROUTES.CREATE_PHOTO}>
                  <Button type="primary" ghost>
                    探索更多提示
                  </Button>
                </Link>
              </div>
            </Skeleton>
          </StyledCard>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;