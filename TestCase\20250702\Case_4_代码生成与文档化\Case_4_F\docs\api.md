# AI证件照生成平台 API文档

## API 概述

AI证件照生成平台API提供以下功能：
- 用户认证 (注册、登录、注销)
- 照片上传
- 证件照生成
- 任务状态查询
- 照片下载
- 用户信息管理

## 基础信息

- **基础URL**: `/api`
- **请求格式**: 大多数API使用JSON格式的请求体，文件上传使用`multipart/form-data`
- **响应格式**: 所有API响应均为JSON格式
- **认证方式**: 使用Bearer Token认证，需要在请求头中添加`Authorization: Bearer <token>`

### 通用响应结构

```json
{
  "code": 200,        // HTTP状态码
  "message": "成功",   // 状态描述
  "data": { ... }     // 响应数据，结构因API而异
}
```

### 错误响应结构

```json
{
  "code": 400,                // 错误状态码
  "message": "请求失败",       // 错误描述
  "error": "详细错误信息"      // 详细错误信息
}
```

## 认证API

### 注册

**请求**:
```
POST /api/auth/register
Content-Type: application/json

{
  "username": "用户名",
  "email": "用户邮箱",
  "password": "密码"
}
```

**响应**:
```json
{
  "code": 201,
  "message": "注册成功",
  "data": {
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "用户邮箱",
      "credits": 5
    },
    "token": "JWT令牌"
  }
}
```

### 登录

**请求**:
```
POST /api/auth/login
Content-Type: application/json

{
  "email": "用户邮箱",
  "password": "密码"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "用户邮箱",
      "avatar": "头像URL",
      "credits": 5
    },
    "token": "JWT令牌"
  }
}
```

### 注销

**请求**:
```
POST /api/auth/logout
Authorization: Bearer <token>
```

**响应**:
```json
{
  "code": 200,
  "message": "注销成功"
}
```

## 上传API

### 上传照片

**请求**:
```
POST /api/upload
Content-Type: multipart/form-data
Authorization: Bearer <token> (可选)

file: <文件数据>
```

**响应**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "upload_id": "上传ID",
    "file_url": "文件URL"
  }
}
```

## 证件照生成API

### 生成证件照

**请求**:
```
POST /api/generate
Content-Type: application/json
Authorization: Bearer <token> (可选)

{
  "upload_id": "上传ID",
  "spec": "1inch",
  "background": "blue",
  "count": 5
}
```

**响应**:
```json
{
  "code": 200,
  "message": "任务创建成功",
  "data": {
    "task_id": "任务ID",
    "status": "processing"
  }
}
```

### 查询任务状态

**请求**:
```
GET /api/task/{任务ID}
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "task_id": "任务ID",
    "status": "completed",
    "progress": 100,
    "generated_photos": [
      {
        "id": "照片ID",
        "url": "照片URL",
        "quality_score": 0.95
      },
      // 更多照片...
    ]
  }
}
```

### 下载照片

**请求**:
```
POST /api/download
Content-Type: application/json

{
  "photo_ids": ["照片ID1", "照片ID2", ...]
}
```

**响应**:
直接返回文件流 (照片或ZIP压缩包)

## 用户API

### 获取用户信息

**请求**:
```
GET /api/user/info
Authorization: Bearer <token>
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "user": {
      "id": "用户ID",
      "username": "用户名",
      "email": "用户邮箱",
      "avatar": "头像URL",
      "credits": 5
    }
  }
}
```

### 更新用户信息

**请求**:
```
PUT /api/user/update
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "新用户名",
  "avatar": "新头像URL"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "user": {
      "id": "用户ID",
      "username": "新用户名",
      "email": "用户邮箱",
      "avatar": "新头像URL",
      "credits": 5
    }
  }
}
```

### 获取使用历史

**请求**:
```
GET /api/user/history
Authorization: Bearer <token>
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "history": [
      {
        "id": "上传ID",
        "date": "上传时间",
        "file_name": "原始文件名",
        "file_size": 1024,
        "tasks": [
          {
            "id": "任务ID",
            "spec": "1inch",
            "background": "blue",
            "status": "completed",
            "created_at": "创建时间",
            "completed_at": "完成时间"
          }
        ]
      },
      // 更多历史记录...
    ]
  }
}
```

## 规格参数

### 证件照规格

| 参数值 | 描述 | 尺寸 |
|-------|------|------|
| `1inch` | 1寸证件照 | 25mm × 35mm |
| `2inch` | 2寸证件照 | 35mm × 49mm |
| `small2inch` | 小2寸证件照 | 33mm × 48mm |
| `large1inch` | 大1寸证件照 | 33mm × 45mm |

### 背景色

| 参数值 | 描述 |
|-------|------|
| `blue` | 蓝色背景 |
| `red` | 红色背景 |
| `white` | 白色背景 |

### 任务状态

| 状态值 | 描述 |
|-------|------|
| `pending` | 等待处理 |
| `processing` | 正在处理 |
| `completed` | 处理完成 |
| `failed` | 处理失败 | 