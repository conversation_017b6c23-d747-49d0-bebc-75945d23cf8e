const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const OSS = require('ali-oss');
const Upload = require('../models/Upload');

// 配置OSS客户端
const ossClient = new OSS({
  region: process.env.OSS_REGION,
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET,
});

// 上传文件到本地临时目录
const uploadToLocal = async (file) => {
  const uploadId = uuidv4();
  const uploadDir = path.join(__dirname, '../../public/uploads');
  
  // 确保上传目录存在
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  // 生成唯一文件名
  const fileName = `${uploadId}${path.extname(file.originalname)}`;
  const filePath = path.join(uploadDir, fileName);
  
  // 写入文件
  await fs.promises.writeFile(filePath, file.buffer);
  
  // 返回上传信息
  return {
    uploadId,
    fileName,
    filePath,
    fileSize: file.size,
    fileType: file.mimetype,
  };
};

// 上传文件到OSS
const uploadToOSS = async (file) => {
  const uploadId = uuidv4();
  const fileName = `${uploadId}${path.extname(file.originalname)}`;
  const ossPath = `uploads/${fileName}`;
  
  // 上传到OSS
  await ossClient.put(ossPath, file.buffer);
  
  // 获取文件URL
  const fileUrl = ossClient.signatureUrl(ossPath);
  
  // 返回上传信息
  return {
    uploadId,
    fileName,
    filePath: ossPath,
    fileUrl,
    fileSize: file.size,
    fileType: file.mimetype,
  };
};

// 保存上传记录到数据库
const saveUploadRecord = async (uploadInfo, userId = null) => {
  return await Upload.create({
    id: uploadInfo.uploadId,
    userId,
    originalFilename: uploadInfo.fileName,
    filePath: uploadInfo.filePath,
    fileSize: uploadInfo.fileSize,
    fileType: uploadInfo.fileType,
  });
};

// 处理文件上传
const handleFileUpload = async (file, userId = null) => {
  try {
    // 根据环境选择上传方式
    const uploadInfo = process.env.NODE_ENV === 'production' 
      ? await uploadToOSS(file)
      : await uploadToLocal(file);
    
    // 保存记录到数据库
    const uploadRecord = await saveUploadRecord(uploadInfo, userId);
    
    return {
      success: true,
      upload: uploadRecord,
      fileUrl: uploadInfo.fileUrl || `/public/uploads/${uploadInfo.fileName}`
    };
  } catch (error) {
    console.error('文件上传失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  handleFileUpload,
  uploadToLocal,
  uploadToOSS
}; 