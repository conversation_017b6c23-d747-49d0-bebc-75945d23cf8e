-- AI证件照生成平台数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_photo_platform 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE ai_photo_platform;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    avatar VARCHAR(500) NULL COMMENT '头像URL',
    credits INT DEFAULT 10 COMMENT '积分余额',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='用户表';

-- 上传记录表
CREATE TABLE uploads (
    id VARCHAR(50) PRIMARY KEY COMMENT '上传ID',
    user_id INT NULL COMMENT '用户ID',
    original_filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(20) NOT NULL COMMENT '文件类型',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    metadata JSON NULL COMMENT '图片元数据',
    status ENUM('uploaded', 'processing', 'completed', 'failed', 'deleted') DEFAULT 'uploaded' COMMENT '状态',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_upload_time (upload_time),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='上传记录表';

-- 生成任务表
CREATE TABLE generation_tasks (
    id VARCHAR(50) PRIMARY KEY COMMENT '任务ID',
    upload_id VARCHAR(50) NOT NULL COMMENT '上传ID',
    spec VARCHAR(20) NOT NULL COMMENT '证件照规格',
    background VARCHAR(20) NOT NULL COMMENT '背景颜色',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '进度百分比',
    model_name VARCHAR(50) NULL COMMENT '使用的AI模型名称',
    model_config JSON NULL COMMENT '模型配置参数',
    custom_spec JSON NULL COMMENT '自定义规格参数',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_spec (spec),
    INDEX idx_background (background)
) ENGINE=InnoDB COMMENT='生成任务表';

-- 生成照片表
CREATE TABLE generated_photos (
    id VARCHAR(50) PRIMARY KEY COMMENT '照片ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    quality_score DECIMAL(3,2) NULL COMMENT '质量评分',
    file_size INT NOT NULL COMMENT '文件大小(字节)',
    width INT NOT NULL COMMENT '图片宽度',
    height INT NOT NULL COMMENT '图片高度',
    format VARCHAR(10) DEFAULT 'jpeg' COMMENT '图片格式',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    metadata JSON NULL COMMENT '生成照片的元数据',
    
    FOREIGN KEY (task_id) REFERENCES generation_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_quality_score (quality_score),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='生成照片表';

-- 用户操作日志表
CREATE TABLE user_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id INT NULL COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(50) NULL COMMENT '资源类型',
    resource_id VARCHAR(50) NULL COMMENT '资源ID',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    details JSON NULL COMMENT '操作详情',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_resource (resource_type, resource_id)
) ENGINE=InnoDB COMMENT='用户操作日志表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(255) NULL COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('max_file_size', '10485760', 'number', '最大文件上传大小(字节)', TRUE),
('max_files_count', '5', 'number', '最大文件上传数量', TRUE),
('supported_formats', '["image/jpeg", "image/jpg", "image/png"]', 'json', '支持的文件格式', TRUE),
('default_credits', '10', 'number', '新用户默认积分', FALSE),
('photo_specs', '{"1inch": {"width": 295, "height": 413, "name": "1寸证件照"}, "2inch": {"width": 413, "height": 579, "name": "2寸证件照"}, "small2inch": {"width": 390, "height": 567, "name": "小2寸证件照"}, "big1inch": {"width": 390, "height": 531, "name": "大1寸证件照"}}', 'json', '证件照规格配置', TRUE),
('background_colors', '{"blue": "#438EDB", "red": "#FF0000", "white": "#FFFFFF", "lightblue": "#87CEEB", "lightgray": "#D3D3D3"}', 'json', '背景颜色配置', TRUE),
('ai_models', '{"default_model": {"name": "default_model", "endpoint": "https://api.example.com/generate", "timeout": 30000}, "custom_model_v1": {"name": "custom_model_v1", "endpoint": "https://custom-api.example.com/process", "timeout": 45000}}', 'json', 'AI模型配置', FALSE);

-- 创建视图：用户统计信息
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.credits,
    u.created_at,
    COUNT(DISTINCT up.id) as total_uploads,
    COUNT(DISTINCT gt.id) as total_tasks,
    COUNT(DISTINCT gp.id) as total_generated_photos,
    SUM(gp.download_count) as total_downloads,
    MAX(up.upload_time) as last_upload_time,
    MAX(gt.created_at) as last_task_time
FROM users u
LEFT JOIN uploads up ON u.id = up.user_id
LEFT JOIN generation_tasks gt ON up.id = gt.upload_id
LEFT JOIN generated_photos gp ON gt.id = gp.task_id
GROUP BY u.id, u.username, u.email, u.credits, u.created_at;

-- 创建视图：任务详情
CREATE VIEW task_details AS
SELECT 
    gt.id as task_id,
    gt.spec,
    gt.background,
    gt.status,
    gt.progress,
    gt.model_name,
    gt.created_at,
    gt.completed_at,
    up.original_filename,
    up.file_size as original_file_size,
    up.user_id,
    u.username,
    COUNT(gp.id) as generated_count,
    AVG(gp.quality_score) as avg_quality_score,
    SUM(gp.download_count) as total_downloads
FROM generation_tasks gt
JOIN uploads up ON gt.upload_id = up.id
LEFT JOIN users u ON up.user_id = u.id
LEFT JOIN generated_photos gp ON gt.id = gp.task_id
GROUP BY gt.id, gt.spec, gt.background, gt.status, gt.progress, gt.model_name, 
         gt.created_at, gt.completed_at, up.original_filename, up.file_size, 
         up.user_id, u.username;

-- 创建存储过程：清理过期数据
DELIMITER //
CREATE PROCEDURE CleanupExpiredData()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE upload_id_var VARCHAR(50);
    DECLARE file_path_var VARCHAR(500);
    
    -- 声明游标
    DECLARE cleanup_cursor CURSOR FOR 
        SELECT up.id, up.file_path 
        FROM uploads up 
        WHERE up.upload_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND up.status = 'uploaded'
        AND NOT EXISTS (
            SELECT 1 FROM generation_tasks gt 
            WHERE gt.upload_id = up.id 
            AND gt.status IN ('pending', 'processing')
        );
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 打开游标
    OPEN cleanup_cursor;
    
    cleanup_loop: LOOP
        FETCH cleanup_cursor INTO upload_id_var, file_path_var;
        IF done THEN
            LEAVE cleanup_loop;
        END IF;
        
        -- 删除相关的生成照片记录
        DELETE gp FROM generated_photos gp
        JOIN generation_tasks gt ON gp.task_id = gt.id
        WHERE gt.upload_id = upload_id_var;
        
        -- 删除生成任务记录
        DELETE FROM generation_tasks WHERE upload_id = upload_id_var;
        
        -- 删除上传记录
        DELETE FROM uploads WHERE id = upload_id_var;
        
        -- 记录清理日志
        INSERT INTO user_logs (action, resource_type, resource_id, details, created_at)
        VALUES ('cleanup', 'upload', upload_id_var, 
                JSON_OBJECT('file_path', file_path_var, 'reason', 'expired'), 
                NOW());
    END LOOP;
    
    -- 关闭游标
    CLOSE cleanup_cursor;
    
    -- 提交事务
    COMMIT;
END //
DELIMITER ;

-- 创建定时清理事件（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS cleanup_expired_data
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL CleanupExpiredData();
