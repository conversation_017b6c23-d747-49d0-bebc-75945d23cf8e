# AI证件照生成平台

## 项目概述

AI证件照生成平台是一个基于AI技术的在线服务，用户只需上传个人照片，即可通过大模型技术自动生成符合标准的证件照。本平台旨在降低用户获取证件照的时间成本和经济成本，提供便捷的在线证件照生成服务。

### 主要功能

- **照片上传**：支持单张或多张照片上传，支持拖拽和点击上传
- **AI证件照生成**：自动人脸检测、背景替换、光线均衡和美化处理
- **多种规格支持**：支持1寸、2寸、小2寸等多种标准证件照规格
- **照片预览和选择**：生成多个版本供用户选择，提供质量评分
- **照片下载**：支持单张下载和批量下载，提供高清版本

## 技术架构

### 前端技术栈

- **框架**：React.js
- **UI组件库**：Ant Design
- **状态管理**：React Hooks
- **路由**：React Router
- **HTTP客户端**：Axios
- **样式处理**：SCSS

### 后端技术栈

- **服务器框架**：Node.js (Express)
- **文件处理**：Multer
- **认证**：JWT (JSON Web Token)
- **数据验证**：Express Validator
- **密码加密**：bcryptjs
- **唯一ID生成**：UUID

## 项目结构

```
ai-id-photo-generator/
├── frontend/                # 前端代码
│   ├── public/              # 静态资源
│   └── src/                 # 源代码
│       ├── components/      # 组件
│       ├── pages/           # 页面
│       ├── services/        # API服务
│       ├── utils/           # 工具函数
│       ├── assets/          # 资源文件
│       └── styles/          # 样式文件
├── backend/                 # 后端代码
│   ├── controllers/         # 控制器
│   ├── models/              # 数据模型
│   ├── routes/              # 路由
│   ├── services/            # 服务
│   ├── config/              # 配置
│   ├── middlewares/         # 中间件
│   └── utils/               # 工具函数
└── docs/                    # 文档
```

## 安装与运行

### 前提条件

- Node.js (v14.0.0+)
- npm (v6.0.0+)

### 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd backend
npm install
```

### 配置环境变量

1. 在backend目录下，复制`.env.example`文件并重命名为`.env`
2. 根据需要修改`.env`文件中的配置

### 运行项目

```bash
# 运行后端服务
cd backend
npm run dev

# 运行前端开发服务器
cd frontend
npm start
```

## API文档

### 认证API

#### 注册用户

```
POST /api/auth/register
```

请求体:
```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "13800138000"
}
```

#### 用户登录

```
POST /api/auth/login
```

请求体:
```json
{
  "username": "user123",
  "password": "password123"
}
```

### 上传API

#### 上传单张照片

```
POST /api/upload
```

表单数据:
- `file`: 照片文件

#### 批量上传照片

```
POST /api/upload/batch
```

表单数据:
- `files`: 照片文件数组（最多5张）

### 照片处理API

#### 生成证件照

```
POST /api/photo/generate
```

请求体:
```json
{
  "upload_id": "unique_upload_id",
  "spec": "1inch",
  "background": "blue",
  "count": 5,
  "model_config": {
    "model_name": "default_model",
    "parameters": {
      "quality": "high",
      "style": "formal"
    }
  }
}
```

#### 获取任务状态

```
GET /api/photo/task/:taskId
```

#### 下载照片

```
POST /api/photo/download
```

请求体:
```json
{
  "photo_ids": ["photo_id_1", "photo_id_2"]
}
```

### 用户API

#### 获取用户个人资料

```
GET /api/user/profile
```

#### 更新用户个人资料

```
PUT /api/user/profile
```

请求体:
```json
{
  "username": "new_username",
  "email": "<EMAIL>",
  "phone": "13900139000"
}
```

#### 修改密码

```
PUT /api/user/password
```

请求体:
```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

#### 获取用户历史记录

```
GET /api/user/history
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

[MIT](LICENSE)

## 联系方式

项目维护者 - [<EMAIL>](mailto:<EMAIL>)

项目链接: [https://github.com/yourusername/ai-id-photo-generator](https://github.com/yourusername/ai-id-photo-generator)