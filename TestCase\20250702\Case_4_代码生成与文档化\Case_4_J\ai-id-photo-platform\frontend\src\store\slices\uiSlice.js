import { createSlice } from '@reduxjs/toolkit'

// 初始状态
const initialState = {
  theme: 'light',
  sidebarCollapsed: false,
  loading: false,
  notifications: [],
  modal: {
    visible: false,
    type: null,
    data: null
  },
  drawer: {
    visible: false,
    type: null,
    data: null
  }
}

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action) => {
      state.theme = action.payload
    },
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload
      }
      state.notifications.unshift(notification)
      
      // 限制通知数量
      if (state.notifications.length > 10) {
        state.notifications = state.notifications.slice(0, 10)
      }
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      )
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    showModal: (state, action) => {
      state.modal = {
        visible: true,
        type: action.payload.type,
        data: action.payload.data || null
      }
    },
    hideModal: (state) => {
      state.modal = {
        visible: false,
        type: null,
        data: null
      }
    },
    showDrawer: (state, action) => {
      state.drawer = {
        visible: true,
        type: action.payload.type,
        data: action.payload.data || null
      }
    },
    hideDrawer: (state) => {
      state.drawer = {
        visible: false,
        type: null,
        data: null
      }
    }
  }
})

export const {
  setTheme,
  toggleSidebar,
  setSidebarCollapsed,
  setLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  showModal,
  hideModal,
  showDrawer,
  hideDrawer
} = uiSlice.actions

export default uiSlice.reducer
