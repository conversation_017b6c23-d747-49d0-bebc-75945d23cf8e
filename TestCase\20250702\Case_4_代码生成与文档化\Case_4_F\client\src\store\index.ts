import { createStore, applyMiddleware, combineReducers } from 'redux';
import thunk from 'redux-thunk';

// Reducers
import uploadReducer from './reducers/uploadReducer';
import photoReducer from './reducers/photoReducer';
import userReducer from './reducers/userReducer';

// Root reducer
const rootReducer = combineReducers({
  upload: uploadReducer,
  photo: photoReducer,
  user: userReducer
});

// 创建Redux存储
const store = createStore(
  rootReducer,
  applyMiddleware(thunk)
);

// 类型定义
export type RootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof store.dispatch;

export default store; 