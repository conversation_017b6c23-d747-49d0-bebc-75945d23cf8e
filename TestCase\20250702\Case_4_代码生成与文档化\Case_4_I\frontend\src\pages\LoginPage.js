import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Checkbox, Card, Typography, Alert } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { login, clearError } from '../services/userSlice';
import './LoginPage.scss';

const { Title, Paragraph } = Typography;

const LoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useSelector((state) => state.user);
  
  const [form] = Form.useForm();
  
  // 如果已登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);
  
  // 清除错误信息
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);
  
  // 处理登录
  const handleLogin = async (values) => {
    try {
      await dispatch(login({
        email: values.email,
        password: values.password
      })).unwrap();
      navigate('/');
    } catch (error) {
      console.error('登录失败:', error);
    }
  };
  
  return (
    <div className="login-page">
      <div className="container">
        <Card className="login-card">
          <div className="login-header">
            <Title level={2}>登录</Title>
            <Paragraph>登录您的账户以使用更多功能</Paragraph>
          </div>
          
          {error && (
            <Alert 
              message="登录失败" 
              description={error} 
              type="error" 
              showIcon 
              className="login-alert"
            />
          )}
          
          <Form
            form={form}
            name="login"
            initialValues={{ remember: true }}
            onFinish={handleLogin}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入您的邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input prefix={<UserOutlined />} placeholder="请输入邮箱" />
            </Form.Item>
            
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入您的密码' }]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
            </Form.Item>
            
            <Form.Item>
              <div className="login-options">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>记住我</Checkbox>
                </Form.Item>
                
                <Link to="/forgot-password" className="forgot-password">
                  忘记密码？
                </Link>
              </div>
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                className="login-button" 
                loading={loading}
                block
              >
                登录
              </Button>
            </Form.Item>
            
            <div className="register-link">
              还没有账号？ <Link to="/register">立即注册</Link>
            </div>
          </Form>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;