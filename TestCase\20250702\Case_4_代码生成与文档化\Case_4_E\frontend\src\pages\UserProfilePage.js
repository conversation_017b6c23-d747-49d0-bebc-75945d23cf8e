import React, { useState, useEffect } from 'react';
import { 
  Typography, 
  Card, 
  Tabs, 
  Form, 
  Input, 
  Button, 
  Avatar, 
  Upload, 
  message, 
  List, 
  Tag, 
  Divider,
  Space,
  Descriptions,
  Badge
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined, 
  UploadOutlined, 
  HistoryOutlined,
  SettingOutlined,
  LockOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const UserProfilePage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [historyList, setHistoryList] = useState([]);
  
  // 模拟获取用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      setLoading(true);
      try {
        // 模拟API请求
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟用户数据
        setUserInfo({
          username: 'user123',
          email: '<EMAIL>',
          phone: '13800138000',
          avatar: 'https://via.placeholder.com/100',
          credits: 50,
          memberSince: '2023-01-15',
          lastLogin: '2024-07-01 14:30:22'
        });
        
        // 模拟历史记录
        setHistoryList(Array(5).fill().map((_, index) => ({
          id: `task_${index}`,
          date: new Date(Date.now() - index * 86400000).toLocaleDateString(),
          type: index % 2 === 0 ? '1寸证件照' : '2寸证件照',
          background: ['蓝色', '红色', '白色'][index % 3],
          status: ['已完成', '已完成', '已完成', '处理中', '失败'][index],
          photoCount: Math.floor(Math.random() * 4) + 1
        })));
      } catch (error) {
        console.error('获取用户信息失败:', error);
        message.error('获取用户信息失败，请重试！');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserInfo();
  }, []);
  
  // 处理个人信息更新
  const handleUpdateProfile = async (values) => {
    setLoading(true);
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 更新本地状态
      setUserInfo(prev => ({
        ...prev,
        ...values
      }));
      
      message.success('个人信息更新成功！');
    } catch (error) {
      console.error('更新失败:', error);
      message.error('更新失败，请重试！');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理密码修改
  const handleChangePassword = async (values) => {
    setLoading(true);
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      message.success('密码修改成功！');
    } catch (error) {
      console.error('密码修改失败:', error);
      message.error('密码修改失败，请重试！');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理头像上传
  const handleAvatarChange = (info) => {
    if (info.file.status === 'done') {
      // 模拟上传成功
      message.success('头像上传成功！');
      setUserInfo(prev => ({
        ...prev,
        avatar: URL.createObjectURL(info.file.originFileObj)
      }));
    } else if (info.file.status === 'error') {
      message.error('头像上传失败！');
    }
  };
  
  // 查看历史记录详情
  const handleViewHistory = (taskId) => {
    navigate(`/result/${taskId}`);
  };

  if (!userInfo) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Typography.Title level={3}>加载中...</Typography.Title>
      </div>
    );
  }

  return (
    <div className="profile-page">
      <Title level={2} style={{ textAlign: 'center', marginBottom: 30 }}>个人中心</Title>
      
      <Card style={{ maxWidth: 1000, margin: '0 auto' }}>
        <Tabs defaultActiveKey="profile">
          <TabPane 
            tab={
              <span>
                <UserOutlined />
                个人资料
              </span>
            } 
            key="profile"
          >
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginBottom: 30 }}>
              <Avatar 
                size={100} 
                src={userInfo.avatar} 
                icon={<UserOutlined />} 
                style={{ marginBottom: 16 }}
              />
              <Upload 
                showUploadList={false}
                customRequest={({ onSuccess }) => {
                  setTimeout(() => {
                    onSuccess("ok");
                  }, 0);
                }}
                onChange={handleAvatarChange}
              >
                <Button icon={<UploadOutlined />}>更换头像</Button>
              </Upload>
            </div>
            
            <Descriptions 
              title="账户信息" 
              bordered 
              column={{ xs: 1, sm: 2 }}
              style={{ marginBottom: 30 }}
            >
              <Descriptions.Item label="用户名">{userInfo.username}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{userInfo.email}</Descriptions.Item>
              <Descriptions.Item label="手机号">{userInfo.phone}</Descriptions.Item>
              <Descriptions.Item label="剩余积分">{userInfo.credits}</Descriptions.Item>
              <Descriptions.Item label="注册时间">{userInfo.memberSince}</Descriptions.Item>
              <Descriptions.Item label="最近登录">{userInfo.lastLogin}</Descriptions.Item>
            </Descriptions>
            
            <Divider orientation="left">修改个人信息</Divider>
            
            <Form
              layout="vertical"
              initialValues={{
                username: userInfo.username,
                email: userInfo.email,
                phone: userInfo.phone
              }}
              onFinish={handleUpdateProfile}
            >
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名!' }]}
              >
                <Input prefix={<UserOutlined />} />
              </Form.Item>
              
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱!' },
                  { type: 'email', message: '请输入有效的邮箱地址!' }
                ]}
              >
                <Input prefix={<MailOutlined />} />
              </Form.Item>
              
              <Form.Item
                name="phone"
                label="手机号"
                rules={[
                  { required: true, message: '请输入手机号!' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号!' }
                ]}
              >
                <Input prefix={<PhoneOutlined />} />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存修改
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <HistoryOutlined />
                使用记录
              </span>
            } 
            key="history"
          >
            <List
              itemLayout="horizontal"
              dataSource={historyList}
              renderItem={item => (
                <List.Item
                  actions={[
                    <Button 
                      type="link" 
                      onClick={() => handleViewHistory(item.id)}
                    >
                      查看详情
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={`任务ID: ${item.id}`}
                    description={
                      <Space direction="vertical">
                        <div>生成时间: {item.date}</div>
                        <div>
                          规格: {item.type} | 
                          背景: {item.background} | 
                          照片数量: {item.photoCount}
                        </div>
                      </Space>
                    }
                  />
                  <div>
                    状态: 
                    <Badge 
                      status={
                        item.status === '已完成' ? 'success' : 
                        item.status === '处理中' ? 'processing' : 'error'
                      } 
                      text={item.status} 
                    />
                  </div>
                </List.Item>
              )}
            />
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                账户安全
              </span>
            } 
            key="security"
          >
            <Divider orientation="left">修改密码</Divider>
            
            <Form
              layout="vertical"
              onFinish={handleChangePassword}
            >
              <Form.Item
                name="currentPassword"
                label="当前密码"
                rules={[{ required: true, message: '请输入当前密码!' }]}
              >
                <Input.Password prefix={<LockOutlined />} />
              </Form.Item>
              
              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码!' },
                  { min: 6, message: '密码至少6个字符!' }
                ]}
                hasFeedback
              >
                <Input.Password prefix={<LockOutlined />} />
              </Form.Item>
              
              <Form.Item
                name="confirmPassword"
                label="确认新密码"
                dependencies={['newPassword']}
                hasFeedback
                rules={[
                  { required: true, message: '请确认新密码!' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致!'));
                    },
                  }),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  修改密码
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default UserProfilePage;