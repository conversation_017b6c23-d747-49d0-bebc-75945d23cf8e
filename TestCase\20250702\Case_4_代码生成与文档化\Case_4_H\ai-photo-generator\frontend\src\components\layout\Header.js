import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Layout, 
  Button, 
  Avatar, 
  Dropdown, 
  Space, 
  Badge, 
  Input, 
  Divider,
  Typography
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  BellOutlined,
  SettingOutlined,
  LogoutOutlined,
  SearchOutlined,
  CrownOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import { logout } from '../../store/authSlice';
import { setFilters } from '../../store/photoSlice';
import { toggleMobileMenu } from '../../store/uiSlice';
import ThemeSwitch from '../common/ThemeSwitch';
import { ROUTES } from '../../utils/constants';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

/**
 * 样式化组件
 */
const StyledHeader = styled(AntHeader)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: ${({ theme }) => theme.colorBgContainer};
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 1;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
`;

const SearchContainer = styled.div`
  margin: 0 16px;
  width: 300px;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  margin-left: 16px;
  cursor: pointer;
  
  .ant-avatar {
    margin-right: 8px;
  }
  
  .username {
    margin-right: 8px;
    
    @media (max-width: 576px) {
      display: none;
    }
  }
`;

const MobileMenuButton = styled(Button)`
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const SubscriptionBadge = styled(Badge)`
  .ant-badge-count {
    background-color: #faad14;
    color: #fff;
    box-shadow: 0 0 0 1px #fff;
  }
`;

/**
 * 头部组件
 * 
 * 显示在应用的顶部，包含标题、用户信息和操作按钮等
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.collapsed - 侧边栏是否折叠
 * @param {Function} props.toggleCollapsed - 切换侧边栏折叠状态的函数
 * @returns {JSX.Element} 头部组件
 */
const Header = ({ collapsed, toggleCollapsed }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // 从Redux获取状态
  const { user } = useSelector((state) => state.auth);
  const { userSubscription } = useSelector((state) => state.subscription);
  const { filters } = useSelector((state) => state.photo);
  
  // 本地状态
  const [searchValue, setSearchValue] = useState(filters.search || '');
  
  // 处理搜索
  const handleSearch = (value) => {
    dispatch(setFilters({ search: value }));
  };
  
  // 处理搜索输入变化
  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };
  
  // 处理搜索按下回车
  const handleSearchPressEnter = () => {
    handleSearch(searchValue);
  };
  
  // 处理登出
  const handleLogout = () => {
    dispatch(logout());
    navigate(ROUTES.LOGIN);
  };
  
  // 用户下拉菜单项
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate(ROUTES.PROFILE),
    },
    {
      key: 'subscription',
      label: '我的订阅',
      icon: <CrownOutlined />,
      onClick: () => navigate(ROUTES.SUBSCRIPTION),
    },
    {
      key: 'settings',
      label: '设置',
      icon: <SettingOutlined />,
      onClick: () => navigate(ROUTES.SETTINGS),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];
  
  // 判断是否有高级订阅
  const hasPremiumSubscription = userSubscription && 
    userSubscription.status === 'active' && 
    userSubscription.planType !== 'free';
  
  return (
    <StyledHeader>
      <LeftSection>
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={toggleCollapsed}
          style={{ fontSize: '16px', width: 64, height: 64 }}
        />
        <MobileMenuButton
          type="text"
          icon={<MenuUnfoldOutlined />}
          onClick={() => dispatch(toggleMobileMenu())}
        />
        <SearchContainer>
          <Input
            placeholder="搜索照片..."
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={handleSearchChange}
            onPressEnter={handleSearchPressEnter}
            allowClear
          />
        </SearchContainer>
      </LeftSection>
      
      <RightSection>
        <ThemeSwitch />
        
        <Badge count={0} dot>
          <Button
            type="text"
            icon={<BellOutlined />}
            style={{ fontSize: '16px' }}
          />
        </Badge>
        
        <Dropdown menu={{ items: userMenuItems }} trigger={['click']}>
          <UserInfo>
            {hasPremiumSubscription ? (
              <SubscriptionBadge count="PRO">
                <Avatar 
                  src={user?.avatar} 
                  icon={!user?.avatar && <UserOutlined />} 
                />
              </SubscriptionBadge>
            ) : (
              <Avatar 
                src={user?.avatar} 
                icon={!user?.avatar && <UserOutlined />} 
              />
            )}
            <Space direction="vertical" size={0} className="username">
              <Text strong>{user?.name || '用户'}</Text>
              {hasPremiumSubscription && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  高级会员
                </Text>
              )}
            </Space>
          </UserInfo>
        </Dropdown>
      </RightSection>
    </StyledHeader>
  );
};

export default Header;