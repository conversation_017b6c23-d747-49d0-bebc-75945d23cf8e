import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { 
  Row, 
  Col, 
  Card, 
  Typography, 
  Steps, 
  Button, 
  Select, 
  Radio, 
  Divider, 
  message,
  Upload,
  Modal
} from 'antd';
import { 
  UploadOutlined, 
  CameraOutlined, 
  FileImageOutlined,
  InboxOutlined
} from '@ant-design/icons';
import { setCurrentPhoto } from '../store/photoSlice';
import { addNotification } from '../store/uiSlice';
import { uploadPhoto } from '../services/photoService';
import { PHOTO_TYPES, ROUTES, UPLOAD_CONFIG } from '../utils/constants';
import { validateFile } from '../utils/utils';
import UploadButton from '../components/common/UploadButton';
import LoadingSpinner from '../components/common/LoadingSpinner';

const { Title, Paragraph } = Typography;
const { Option } = Select;
const { Dragger } = Upload;
const { Step } = Steps;

const Upload = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // 状态
  const [currentStep, setCurrentStep] = useState(0);
  const [photoType, setPhotoType] = useState(PHOTO_TYPES[0].value);
  const [uploadMethod, setUploadMethod] = useState('file');
  const [file, setFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [cameraActive, setCameraActive] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  
  // 视频流引用
  const videoRef = React.useRef(null);
  const canvasRef = React.useRef(null);
  
  // 处理照片类型选择
  const handlePhotoTypeChange = (value) => {
    setPhotoType(value);
  };
  
  // 处理上传方式选择
  const handleUploadMethodChange = (e) => {
    const method = e.target.value;
    setUploadMethod(method);
    
    // 如果选择了摄像头，启动摄像头
    if (method === 'camera') {
      startCamera();
    } else if (cameraActive) {
      stopCamera();
    }
  };
  
  // 启动摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setCameraActive(true);
      }
    } catch (error) {
      console.error('无法访问摄像头:', error);
      message.error('无法访问摄像头，请检查权限设置或使用文件上传');
      setUploadMethod('file');
    }
  };
  
  // 停止摄像头
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setCameraActive(false);
    }
  };
  
  // 拍照
  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // 设置canvas尺寸与视频相同
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // 在canvas上绘制当前视频帧
      const context = canvas.getContext('2d');
      context.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // 将canvas内容转换为Blob
      canvas.toBlob((blob) => {
        const file = new File([blob], 'camera-photo.jpg', { type: 'image/jpeg' });
        handleFileSelected(file);
      }, 'image/jpeg', 0.95);
      
      // 停止摄像头
      stopCamera();
    }
  };
  
  // 处理文件选择
  const handleFileSelected = (file) => {
    // 验证文件
    if (!validateFile(file)) {
      return;
    }
    
    setFile(file);
    
    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    
    // 自动进入下一步
    setCurrentStep(1);
  };
  
  // 处理文件上传
  const handleUpload = async () => {
    if (!file) {
      message.error('请先选择或拍摄照片');
      return;
    }
    
    setLoading(true);
    
    try {
      // 调用上传API
      const response = await uploadPhoto(file, photoType);
      
      // 更新Redux状态
      dispatch(setCurrentPhoto(response.photo));
      
      // 显示成功消息
      dispatch(addNotification({
        type: 'success',
        title: '上传成功',
        message: '照片已成功上传，正在进入编辑页面',
      }));
      
      // 导航到结果页面
      navigate(`${ROUTES.RESULT}/${response.photo.id}`);
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败，请重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 预览图片
  const handlePreview = () => {
    setPreviewVisible(true);
  };
  
  // 关闭预览
  const handlePreviewCancel = () => {
    setPreviewVisible(false);
  };
  
  // 返回上一步
  const handlePrevStep = () => {
    setCurrentStep(0);
    setFile(null);
    setPreviewUrl('');
  };
  
  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="upload-step">
            <div className="photo-type-selection">
              <Title level={4}>选择证件照类型</Title>
              <Select
                value={photoType}
                onChange={handlePhotoTypeChange}
                style={{ width: '100%' }}
                size="large"
              >
                {PHOTO_TYPES.map((type) => (
                  <Option key={type.value} value={type.value}>
                    {type.label}
                  </Option>
                ))}
              </Select>
              <Paragraph className="type-description">
                {PHOTO_TYPES.find(t => t.value === photoType)?.description || ''}
              </Paragraph>
            </div>
            
            <Divider />
            
            <div className="upload-method">
              <Title level={4}>选择上传方式</Title>
              <Radio.Group 
                value={uploadMethod} 
                onChange={handleUploadMethodChange}
                className="upload-method-group"
              >
                <Radio.Button value="file">
                  <FileImageOutlined /> 上传文件
                </Radio.Button>
                <Radio.Button value="camera">
                  <CameraOutlined /> 使用摄像头
                </Radio.Button>
              </Radio.Group>
              
              {uploadMethod === 'file' ? (
                <div className="file-upload-area">
                  <Dragger
                    name="photo"
                    multiple={false}
                    showUploadList={false}
                    beforeUpload={(file) => {
                      handleFileSelected(file);
                      return false; // 阻止自动上传
                    }}
                    accept={UPLOAD_CONFIG.acceptedFormats.join(',')}
                  >
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                    <p className="ant-upload-hint">
                      支持单个文件上传，文件大小不超过{UPLOAD_CONFIG.maxSize}MB
                    </p>
                  </Dragger>
                </div>
              ) : (
                <div className="camera-area">
                  <div className="camera-container">
                    <video 
                      ref={videoRef} 
                      autoPlay 
                      playsInline 
                      className="camera-preview"
                    />
                    <canvas ref={canvasRef} style={{ display: 'none' }} />
                  </div>
                  <Button 
                    type="primary" 
                    icon={<CameraOutlined />} 
                    onClick={takePhoto}
                    disabled={!cameraActive}
                    className="take-photo-btn"
                  >
                    拍照
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
      
      case 1:
        return (
          <div className="confirm-step">
            <div className="preview-container">
              <img 
                src={previewUrl} 
                alt="预览" 
                className="photo-preview" 
                onClick={handlePreview}
              />
              <div className="preview-actions">
                <Button onClick={handlePreview}>查看大图</Button>
              </div>
            </div>
            
            <div className="confirm-info">
              <Title level={4}>确认信息</Title>
              <div className="info-item">
                <span className="info-label">照片类型:</span>
                <span className="info-value">
                  {PHOTO_TYPES.find(t => t.value === photoType)?.label || ''}
                </span>
              </div>
              <div className="info-item">
                <span className="info-label">文件名:</span>
                <span className="info-value">{file?.name}</span>
              </div>
              <div className="info-item">
                <span className="info-label">文件大小:</span>
                <span className="info-value">
                  {file ? `${(file.size / (1024 * 1024)).toFixed(2)} MB` : ''}
                </span>
              </div>
            </div>
            
            <div className="confirm-actions">
              <Button onClick={handlePrevStep}>返回修改</Button>
              <Button 
                type="primary" 
                onClick={handleUpload} 
                loading={loading}
              >
                确认上传
              </Button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <div className="upload-page">
      <Card className="upload-card">
        <Steps current={currentStep} className="upload-steps">
          <Step title="上传照片" description="选择照片类型并上传" />
          <Step title="确认信息" description="确认照片和信息" />
        </Steps>
        
        <div className="step-content">
          {loading ? <LoadingSpinner tip="正在上传..." /> : renderStepContent()}
        </div>
      </Card>
      
      <Modal
        visible={previewVisible}
        title="照片预览"
        footer={null}
        onCancel={handlePreviewCancel}
      >
        <img alt="预览" style={{ width: '100%' }} src={previewUrl} />
      </Modal>
    </div>
  );
};

export default Upload;