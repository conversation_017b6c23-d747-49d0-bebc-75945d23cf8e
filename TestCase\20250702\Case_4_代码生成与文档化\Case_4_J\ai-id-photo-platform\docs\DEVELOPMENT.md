# AI证件照生成平台开发文档

## 概述

本文档为开发人员提供项目的技术架构、开发环境搭建、代码规范等详细信息。

## 技术架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │   AI服务接口     │
│                 │    │                 │    │                 │
│ - React 18      │◄──►│ - Express       │◄──►│ - 自定义AI模型   │
│ - Ant Design    │    │ - Sequelize     │    │ - 第三方AI API   │
│ - Redux Toolkit │    │ - JWT Auth      │    │                 │
│ - Vite          │    │ - Multer        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   数据存储层     │
                       │                 │
                       │ - MySQL 8.0     │
                       │ - Redis 6.0     │
                       │ - 文件存储       │
                       └─────────────────┘
```

### 技术栈详情

**前端技术栈:**
- **React 18**: 用户界面框架
- **Ant Design**: UI组件库
- **Redux Toolkit**: 状态管理
- **React Router**: 路由管理
- **Vite**: 构建工具
- **Axios**: HTTP客户端
- **React Dropzone**: 文件拖拽上传

**后端技术栈:**
- **Node.js**: 运行时环境
- **Express**: Web框架
- **Sequelize**: ORM框架
- **MySQL**: 关系型数据库
- **Redis**: 缓存和会话存储
- **JWT**: 身份认证
- **Multer**: 文件上传处理
- **Sharp**: 图像处理
- **Winston**: 日志管理

## 开发环境搭建

### 1. 环境要求

- Node.js 16.0+
- MySQL 8.0+
- Redis 6.0+
- Git

### 2. 项目初始化

```bash
# 克隆项目
git clone https://github.com/your-repo/ai-id-photo-platform.git
cd ai-id-photo-platform

# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 3. 数据库配置

```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ai_photo_platform_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'dev_user'@'localhost' IDENTIFIED BY 'dev_password';
GRANT ALL PRIVILEGES ON ai_photo_platform_dev.* TO 'dev_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 初始化数据库
cd backend
mysql -u dev_user -p ai_photo_platform_dev < ../database/init.sql

# 插入测试数据
mysql -u dev_user -p ai_photo_platform_dev < ../database/seeds/demo_data.sql
```

### 4. 环境配置

**后端配置** (`backend/.env`):
```bash
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3001

DB_HOST=localhost
DB_PORT=3306
DB_NAME=ai_photo_platform_dev
DB_USER=dev_user
DB_PASSWORD=dev_password

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

JWT_SECRET=dev-jwt-secret-key
JWT_EXPIRES_IN=7d

USE_MOCK_AI=true
LOG_LEVEL=debug
```

**前端配置** (`frontend/.env.development`):
```bash
VITE_API_BASE_URL=http://localhost:3000/api
```

### 5. 启动开发服务器

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务 (新终端)
cd frontend
npm run dev
```

访问 `http://localhost:3001` 查看应用。

## 项目结构

### 后端结构

```
backend/
├── src/
│   ├── app.js                 # 应用入口
│   ├── config/                # 配置文件
│   │   ├── database.js        # 数据库配置
│   │   ├── redis.js           # Redis配置
│   │   └── aiModels.js        # AI模型配置
│   ├── controllers/           # 控制器
│   │   ├── uploadController.js
│   │   ├── generateController.js
│   │   ├── taskController.js
│   │   └── userController.js
│   ├── middleware/            # 中间件
│   │   ├── auth.js            # 认证中间件
│   │   ├── upload.js          # 上传中间件
│   │   └── errorHandler.js    # 错误处理
│   ├── models/                # 数据模型
│   │   ├── User.js
│   │   ├── Upload.js
│   │   ├── GenerationTask.js
│   │   ├── GeneratedPhoto.js
│   │   └── index.js
│   ├── routes/                # 路由
│   │   ├── upload.js
│   │   ├── generate.js
│   │   ├── task.js
│   │   └── user.js
│   ├── services/              # 业务服务
│   │   └── ai/
│   │       └── aiService.js
│   └── utils/                 # 工具函数
│       ├── logger.js
│       ├── fileUtils.js
│       └── validation.js
├── uploads/                   # 上传文件目录
├── logs/                      # 日志目录
├── package.json
└── .env.example
```

### 前端结构

```
frontend/
├── src/
│   ├── main.jsx               # 应用入口
│   ├── App.jsx                # 根组件
│   ├── components/            # 公共组件
│   │   ├── Layout/
│   │   │   ├── Header.jsx
│   │   │   └── Footer.jsx
│   │   └── Common/
│   ├── pages/                 # 页面组件
│   │   ├── HomePage/
│   │   ├── UploadPage/
│   │   ├── ResultPage/
│   │   ├── LoginPage/
│   │   ├── RegisterPage/
│   │   ├── ProfilePage/
│   │   └── HistoryPage/
│   ├── services/              # API服务
│   │   ├── api.js
│   │   ├── authService.js
│   │   ├── uploadService.js
│   │   └── taskService.js
│   ├── store/                 # 状态管理
│   │   ├── index.js
│   │   └── slices/
│   │       ├── authSlice.js
│   │       ├── uploadSlice.js
│   │       ├── taskSlice.js
│   │       └── uiSlice.js
│   ├── utils/                 # 工具函数
│   ├── styles/                # 样式文件
│   └── assets/                # 静态资源
├── public/
├── package.json
└── vite.config.js
```

## 代码规范

### 1. JavaScript/JSX规范

**命名规范:**
- 变量和函数：camelCase
- 常量：UPPER_SNAKE_CASE
- 组件：PascalCase
- 文件名：camelCase或kebab-case

**代码风格:**
```javascript
// 好的示例
const getUserProfile = async (userId) => {
  try {
    const response = await api.get(`/user/${userId}`)
    return response.data
  } catch (error) {
    logger.error('获取用户信息失败', { userId, error: error.message })
    throw error
  }
}

// React组件
const UserProfile = ({ userId, onUpdate }) => {
  const [loading, setLoading] = useState(false)
  
  const handleUpdate = useCallback(async (data) => {
    setLoading(true)
    try {
      await updateProfile(data)
      onUpdate?.(data)
    } catch (error) {
      message.error('更新失败')
    } finally {
      setLoading(false)
    }
  }, [onUpdate])
  
  return (
    <Card loading={loading}>
      {/* 组件内容 */}
    </Card>
  )
}
```

### 2. CSS/SCSS规范

```scss
// 使用BEM命名规范
.upload-page {
  padding: 24px;
  
  &__header {
    margin-bottom: 32px;
    text-align: center;
  }
  
  &__dropzone {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 48px;
    text-align: center;
    transition: all 0.3s ease;
    
    &--active {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .upload-page {
    padding: 16px;
    
    &__dropzone {
      padding: 24px;
    }
  }
}
```

### 3. API设计规范

```javascript
// 控制器结构
class UploadController {
  /**
   * 单文件上传
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  async uploadSingle(req, res, next) {
    try {
      // 参数验证
      if (!req.file) {
        return res.status(400).json({
          code: 400,
          message: '请选择要上传的文件',
          errors: ['文件不能为空']
        })
      }
      
      // 业务逻辑
      const result = await this.processUpload(req.file)
      
      // 返回结果
      res.json({
        code: 200,
        message: '文件上传成功',
        data: result
      })
    } catch (error) {
      next(error)
    }
  }
}
```

## 数据库设计

### 1. 表结构设计原则

- 使用有意义的表名和字段名
- 合理设置字段类型和长度
- 添加必要的索引
- 使用外键约束保证数据完整性
- 添加适当的注释

### 2. 模型定义

```javascript
// Sequelize模型示例
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      notEmpty: true
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
      notEmpty: true
    }
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
})
```

## 测试

### 1. 单元测试

```javascript
// 使用Jest进行单元测试
describe('UploadService', () => {
  describe('validateFile', () => {
    it('应该验证有效的图片文件', () => {
      const file = {
        mimetype: 'image/jpeg',
        size: 1024 * 1024 // 1MB
      }
      
      const result = uploadService.validateFile(file)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
    
    it('应该拒绝无效的文件类型', () => {
      const file = {
        mimetype: 'text/plain',
        size: 1024
      }
      
      const result = uploadService.validateFile(file)
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('文件类型不支持')
    })
  })
})
```

### 2. 集成测试

```javascript
// API集成测试
describe('Upload API', () => {
  it('POST /api/upload/single - 应该成功上传文件', async () => {
    const response = await request(app)
      .post('/api/upload/single')
      .attach('file', 'test/fixtures/test-image.jpg')
      .expect(200)
    
    expect(response.body.code).toBe(200)
    expect(response.body.data.uploadId).toBeDefined()
  })
})
```

### 3. 运行测试

```bash
# 后端测试
cd backend
npm test

# 前端测试
cd frontend
npm test

# 测试覆盖率
npm run test:coverage
```

## 调试

### 1. 后端调试

```javascript
// 使用Winston日志
const logger = require('./utils/logger')

// 不同级别的日志
logger.debug('调试信息', { userId, action })
logger.info('操作成功', { result })
logger.warn('警告信息', { warning })
logger.error('错误信息', { error: error.message, stack: error.stack })
```

### 2. 前端调试

```javascript
// Redux DevTools
// 在store配置中启用
const store = configureStore({
  reducer: rootReducer,
  devTools: process.env.NODE_ENV !== 'production'
})

// 组件调试
const DebugComponent = () => {
  useEffect(() => {
    console.log('Component mounted')
    return () => console.log('Component unmounted')
  }, [])
  
  return <div>Debug Component</div>
}
```

## 性能优化

### 1. 后端优化

```javascript
// 数据库查询优化
const tasks = await GenerationTask.findAll({
  include: [
    {
      model: Upload,
      as: 'upload',
      attributes: ['originalFilename'] // 只选择需要的字段
    }
  ],
  limit: 10,
  offset: (page - 1) * 10,
  order: [['createdAt', 'DESC']]
})

// 缓存优化
const getCachedData = async (key) => {
  const cached = await redis.get(key)
  if (cached) {
    return JSON.parse(cached)
  }
  
  const data = await fetchDataFromDB()
  await redis.setex(key, 3600, JSON.stringify(data)) // 缓存1小时
  return data
}
```

### 2. 前端优化

```javascript
// 组件懒加载
const LazyComponent = React.lazy(() => import('./LazyComponent'))

// 使用React.memo优化渲染
const OptimizedComponent = React.memo(({ data }) => {
  return <div>{data.name}</div>
}, (prevProps, nextProps) => {
  return prevProps.data.id === nextProps.data.id
})

// 使用useMemo缓存计算结果
const ExpensiveComponent = ({ items }) => {
  const expensiveValue = useMemo(() => {
    return items.reduce((sum, item) => sum + item.value, 0)
  }, [items])
  
  return <div>{expensiveValue}</div>
}
```

## 部署流程

### 1. 构建流程

```bash
# 后端构建
cd backend
npm run build  # 如果有构建步骤

# 前端构建
cd frontend
npm run build
```

### 2. CI/CD配置

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
        
    - name: Run tests
      run: |
        cd backend && npm test
        cd ../frontend && npm test
        
    - name: Build frontend
      run: cd frontend && npm run build
      
    - name: Deploy
      run: |
        # 部署脚本
        ./deploy.sh
```

## 常见问题

### 1. 开发环境问题

**问题**: 数据库连接失败
**解决**: 检查数据库服务状态和连接配置

**问题**: 文件上传失败
**解决**: 检查uploads目录权限和磁盘空间

**问题**: Redis连接失败
**解决**: 确认Redis服务运行状态

### 2. 代码问题

**问题**: 内存泄漏
**解决**: 检查事件监听器清理、定时器清理

**问题**: 性能问题
**解决**: 使用性能分析工具，优化数据库查询

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 提交信息规范

```
type(scope): description

feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

示例:
```
feat(upload): 添加批量文件上传功能
fix(auth): 修复JWT token过期处理
docs(api): 更新API文档
```
