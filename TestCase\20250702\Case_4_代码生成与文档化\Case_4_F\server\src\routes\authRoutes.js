const express = require('express');
const router = express.Router();
const { 
  registerValidationRules, 
  loginValidationRules, 
  validate, 
  registerUser, 
  loginUser,
  logoutUser
} = require('../controllers/authController');
const { auth } = require('../middleware/authMiddleware');

// 注册用户
router.post('/register', registerValidationRules, validate, registerUser);

// 用户登录
router.post('/login', loginValidationRules, validate, loginUser);

// 注销登录 (需要身份验证)
router.post('/logout', auth, logoutUser);

module.exports = router; 