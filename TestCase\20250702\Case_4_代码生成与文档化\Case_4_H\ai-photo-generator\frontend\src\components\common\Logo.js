import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Typography } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import { ROUTES } from '../../utils/constants';

const { Title } = Typography;

/**
 * Logo组件
 * 显示应用的Logo和名称
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.collapsed - 是否折叠（用于侧边栏）
 * @returns {React.ReactNode} 渲染的组件
 */
const Logo = ({ collapsed = false }) => {
  const navigate = useNavigate();

  // 点击Logo时导航到首页
  const handleClick = () => {
    navigate(ROUTES.HOME);
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: collapsed ? 'center' : 'flex-start',
        cursor: 'pointer',
      }}
      onClick={handleClick}
    >
      <CameraOutlined
        style={{
          fontSize: collapsed ? '24px' : '28px',
          color: '#1890ff',
        }}
      />
      {!collapsed && (
        <Title
          level={4}
          style={{
            margin: '0 0 0 8px',
            padding: 0,
            color: 'inherit',
          }}
        >
          AI证件照
        </Title>
      )}
    </div>
  );
};

export default Logo;