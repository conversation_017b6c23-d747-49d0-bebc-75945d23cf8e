# AI证件照生成平台 API接口文档

## 1. API概述

本文档描述了AI证件照生成平台的API接口规范，包括请求格式、响应格式、错误处理等内容。这些接口设计用于前后端分离架构，支持未来与后端服务的集成。

### 1.1 基本信息

- **基础URL**: `https://api.example.com`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token

### 1.2 通用请求头

| 请求头 | 说明 |
|-------|------|
| `Authorization` | 认证令牌，格式为 `Bearer {token}` |
| `Content-Type` | 内容类型，通常为 `application/json` |
| `Accept` | 接受的响应格式，通常为 `application/json` |
| `X-API-Key` | API密钥（可选，用于公共API） |

### 1.3 通用响应格式

```json
{
  "code": 200,          // 状态码
  "message": "成功",     // 状态描述
  "data": { ... },      // 响应数据
  "timestamp": 1625097600000  // 时间戳
}
```

### 1.4 通用错误码

| 错误码 | 说明 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 2. 用户认证接口

### 2.1 用户注册

**请求方法**: `POST`

**请求路径**: `/api/v1/auth/register`

**请求参数**:

```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": "u123456",
    "username": "user123",
    "email": "<EMAIL>",
    "created_at": "2025-07-01T12:00:00Z"
  },
  "timestamp": 1625097600000
}
```

### 2.2 用户登录

**请求方法**: `POST`

**请求路径**: `/api/v1/auth/login`

**请求参数**:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": "u123456",
    "username": "user123",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  },
  "timestamp": 1625097600000
}
```

### 2.3 刷新令牌

**请求方法**: `POST`

**请求路径**: `/api/v1/auth/refresh-token`

**请求头**:

```
Authorization: Bearer {refresh_token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "令牌刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  },
  "timestamp": 1625097600000
}
```

### 2.4 退出登录

**请求方法**: `POST`

**请求路径**: `/api/v1/auth/logout`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "退出成功",
  "data": null,
  "timestamp": 1625097600000
}
```

## 3. 照片上传接口

### 3.1 上传照片

**请求方法**: `POST`

**请求路径**: `/api/v1/upload`

**请求头**:

```
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `file` | File | 是 | 上传的图片文件，支持JPG、PNG、JPEG格式 |
| `spec` | String | 否 | 证件照规格，如"1inch"、"2inch"等 |

**响应示例**:

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "upload_id": "upload_1625097600000",
    "file_url": "https://cdn.example.com/uploads/user123/photo_1625097600000.jpg",
    "file_name": "photo.jpg",
    "file_size": 1024000,
    "upload_time": "2025-07-01T12:00:00Z"
  },
  "timestamp": 1625097600000
}
```

### 3.2 获取上传历史

**请求方法**: `GET`

**请求路径**: `/api/v1/uploads`

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `page` | Integer | 否 | 页码，默认为1 |
| `limit` | Integer | 否 | 每页数量，默认为20，最大为100 |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "uploads": [
      {
        "upload_id": "upload_1625097600000",
        "file_url": "https://cdn.example.com/uploads/user123/photo_1625097600000.jpg",
        "file_name": "photo.jpg",
        "file_size": 1024000,
        "upload_time": "2025-07-01T12:00:00Z"
      },
      // ...更多上传记录
    ]
  },
  "timestamp": 1625097600000
}
```

### 3.3 删除上传的照片

**请求方法**: `DELETE`

**请求路径**: `/api/v1/uploads/{upload_id}`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null,
  "timestamp": 1625097600000
}
```

## 4. 证件照生成接口

### 4.1 生成证件照

**请求方法**: `POST`

**请求路径**: `/api/v1/generate`

**请求头**:

```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "upload_id": "upload_1625097600000",
  "spec": "1inch",
  "background": "blue",
  "count": 5,
  "model_config": {
    "model_name": "default_model",
    "parameters": {
      "quality": "high",
      "style": "formal"
    }
  }
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `upload_id` | String | 是 | 上传照片的ID |
| `spec` | String | 是 | 证件照规格，如"1inch"、"2inch"等 |
| `background` | String | 是 | 背景颜色，如"blue"、"red"、"white"等 |
| `count` | Integer | 否 | 生成数量，默认为5，最大为10 |
| `model_config` | Object | 否 | 模型配置参数 |

**响应示例**:

```json
{
  "code": 200,
  "message": "任务已提交",
  "data": {
    "task_id": "task_1625097600000",
    "status": "processing",
    "estimated_time": 60
  },
  "timestamp": 1625097600000
}
```

### 4.2 查询任务状态

**请求方法**: `GET`

**请求路径**: `/api/v1/tasks/{task_id}`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "task_id": "task_1625097600000",
    "status": "completed",
    "progress": 100,
    "created_at": "2025-07-01T12:00:00Z",
    "completed_at": "2025-07-01T12:01:00Z",
    "generated_photos": [
      {
        "id": "photo_1",
        "url": "https://cdn.example.com/generated/user123/photo_1.jpg",
        "spec": "1inch",
        "background": "blue",
        "quality_score": 0.95,
        "created_at": "2025-07-01T12:01:00Z"
      },
      // ...更多生成的照片
    ]
  },
  "timestamp": 1625097600000
}
```

### 4.3 获取生成历史

**请求方法**: `GET`

**请求路径**: `/api/v1/tasks`

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `page` | Integer | 否 | 页码，默认为1 |
| `limit` | Integer | 否 | 每页数量，默认为20，最大为100 |
| `status` | String | 否 | 任务状态，如"pending"、"processing"、"completed"、"failed" |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 15,
    "page": 1,
    "limit": 20,
    "tasks": [
      {
        "task_id": "task_1625097600000",
        "status": "completed",
        "created_at": "2025-07-01T12:00:00Z",
        "completed_at": "2025-07-01T12:01:00Z",
        "spec": "1inch",
        "background": "blue",
        "photo_count": 5
      },
      // ...更多任务记录
    ]
  },
  "timestamp": 1625097600000
}
```

## 5. 照片管理接口

### 5.1 获取生成的照片列表

**请求方法**: `GET`

**请求路径**: `/api/v1/photos`

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `page` | Integer | 否 | 页码，默认为1 |
| `limit` | Integer | 否 | 每页数量，默认为20，最大为100 |
| `task_id` | String | 否 | 按任务ID筛选 |
| `spec` | String | 否 | 按规格筛选 |
| `background` | String | 否 | 按背景色筛选 |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 35,
    "page": 1,
    "limit": 20,
    "photos": [
      {
        "id": "photo_1",
        "url": "https://cdn.example.com/generated/user123/photo_1.jpg",
        "task_id": "task_1625097600000",
        "spec": "1inch",
        "background": "blue",
        "quality_score": 0.95,
        "created_at": "2025-07-01T12:01:00Z"
      },
      // ...更多照片
    ]
  },
  "timestamp": 1625097600000
}
```

### 5.2 删除生成的照片

**请求方法**: `DELETE`

**请求路径**: `/api/v1/photos/{photo_id}`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null,
  "timestamp": 1625097600000
}
```

### 5.3 批量删除照片

**请求方法**: `DELETE`

**请求路径**: `/api/v1/photos`

**请求头**:

```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "photo_ids": ["photo_1", "photo_2", "photo_3"]
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": {
    "success_count": 3,
    "failed_count": 0
  },
  "timestamp": 1625097600000
}
```

## 6. 用户账户接口

### 6.1 获取用户信息

**请求方法**: `GET`

**请求路径**: `/api/v1/users/me`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_id": "u123456",
    "username": "user123",
    "email": "<EMAIL>",
    "created_at": "2025-07-01T12:00:00Z",
    "usage": {
      "total_uploads": 15,
      "total_generations": 42,
      "remaining_credits": 100
    }
  },
  "timestamp": 1625097600000
}
```

### 6.2 更新用户信息

**请求方法**: `PUT`

**请求路径**: `/api/v1/users/me`

**请求头**:

```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "username": "newUsername",
  "avatar": "https://cdn.example.com/avatars/user123.jpg"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "user_id": "u123456",
    "username": "newUsername",
    "email": "<EMAIL>",
    "avatar": "https://cdn.example.com/avatars/user123.jpg"
  },
  "timestamp": 1625097600000
}
```

### 6.3 修改密码

**请求方法**: `PUT`

**请求路径**: `/api/v1/users/me/password`

**请求头**:

```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "current_password": "currentPassword123",
  "new_password": "newPassword456"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null,
  "timestamp": 1625097600000
}
```

## 7. 支付与订单接口

### 7.1 获取套餐列表

**请求方法**: `GET`

**请求路径**: `/api/v1/packages`

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "packages": [
      {
        "id": "basic",
        "name": "基础版",
        "price": 9.9,
        "currency": "CNY",
        "period": "once",
        "features": [
          "单次证件照生成",
          "标准规格支持",
          "3种背景色选择",
          "生成3个版本",
          "有效期7天"
        ]
      },
      {
        "id": "premium",
        "name": "高级版",
        "price": 29.9,
        "currency": "CNY",
        "period": "month",
        "features": [
          "每月10次证件照生成",
          "所有规格支持",
          "全部背景色选择",
          "生成5个版本",
          "高级美化处理",
          "历史记录保存"
        ]
      },
      {
        "id": "enterprise",
        "name": "企业版",
        "price": 199,
        "currency": "CNY",
        "period": "month",
        "features": [
          "无限次证件照生成",
          "所有规格支持",
          "全部背景色选择",
          "生成10个版本",
          "批量处理功能",
          "专属客服支持",
          "API接口调用"
        ]
      }
    ]
  },
  "timestamp": 1625097600000
}
```

### 7.2 创建订单

**请求方法**: `POST`

**请求路径**: `/api/v1/orders`

**请求头**:

```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "package_id": "premium",
  "payment_method": "alipay"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order_id": "order_1625097600000",
    "package_id": "premium",
    "amount": 29.9,
    "currency": "CNY",
    "status": "pending",
    "created_at": "2025-07-01T12:00:00Z",
    "payment_url": "https://payment.example.com/pay/order_1625097600000"
  },
  "timestamp": 1625097600000
}
```

### 7.3 查询订单状态

**请求方法**: `GET`

**请求路径**: `/api/v1/orders/{order_id}`

**请求头**:

```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "order_id": "order_1625097600000",
    "package_id": "premium",
    "amount": 29.9,
    "currency": "CNY",
    "status": "paid",
    "created_at": "2025-07-01T12:00:00Z",
    "paid_at": "2025-07-01T12:05:00Z",
    "valid_until": "2025-08-01T12:05:00Z"
  },
  "timestamp": 1625097600000
}
```

### 7.4 获取订单历史

**请求方法**: `GET`

**请求路径**: `/api/v1/orders`

**请求头**:

```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| `page` | Integer | 否 | 页码，默认为1 |
| `limit` | Integer | 否 | 每页数量，默认为20，最大为100 |
| `status` | String | 否 | 订单状态，如"pending"、"paid"、"cancelled"、"expired" |

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "orders": [
      {
        "order_id": "order_1625097600000",
        "package_id": "premium",
        "package_name": "高级版",
        "amount": 29.9,
        "currency": "CNY",
        "status": "paid",
        "created_at": "2025-07-01T12:00:00Z",
        "paid_at": "2025-07-01T12:05:00Z"
      },
      // ...更多订单记录
    ]
  },
  "timestamp": 1625097600000
}
```

## 8. 错误处理

### 8.1 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ],
  "timestamp": 1625097600000
}
```

### 8.2 常见错误码及说明

| 错误码 | 说明 | 处理建议 |
|-------|------|---------|
| 400 | 请求参数错误 | 检查请求参数是否符合要求 |
| 401 | 未授权 | 检查认证令牌是否有效 |
| 403 | 禁止访问 | 检查用户权限 |
| 404 | 资源不存在 | 检查请求的资源ID是否正确 |
| 409 | 资源冲突 | 检查是否存在重复操作 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器内部错误 | 联系客服或稍后重试 |

## 9. 附录

### 9.1 证件照规格参数

| 规格代码 | 说明 | 尺寸 |
|---------|------|------|
| `1inch` | 1寸证件照 | 25mm × 35mm |
| `2inch` | 2寸证件照 | 35mm × 49mm |
| `small2inch` | 小2寸证件照 | 33mm × 48mm |
| `large1inch` | 大1寸证件照 | 33mm × 45mm |
| `custom` | 自定义尺寸 | 用户指定 |

### 9.2 背景颜色参数

| 颜色代码 | 说明 | 颜色值 |
|---------|------|-------|
| `blue` | 蓝色背景 | #438edb |
| `red` | 红色背景 | #d81e06 |
| `white` | 白色背景 | #ffffff |

### 9.3 模型参数

| 参数名 | 类型 | 说明 | 可选值 |
|-------|------|------|-------|
| `quality` | String | 图像质量 | "low", "medium", "high" |
| `style` | String | 照片风格 | "formal", "casual", "professional" |
| `enhancement` | Boolean | 是否进行增强 | true, false |
| `face_ratio` | Float | 面部比例 | 0.5-0.8 |

### 9.4 API限流规则

| 接口 | 普通用户限制 | 高级用户限制 | 企业用户限制 |
|------|------------|------------|------------|
| 上传照片 | 10次/分钟 | 30次/分钟 | 100次/分钟 |
| 生成证件照 | 5次/分钟 | 20次/分钟 | 50次/分钟 |
| 其他接口 | 60次/分钟 | 120次/分钟 | 300次/分钟 |