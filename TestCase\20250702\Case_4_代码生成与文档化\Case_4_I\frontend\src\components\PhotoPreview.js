import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Row, Col, Checkbox, Button, Modal, Spin, Rate, message } from 'antd';
import { DownloadOutlined, EyeOutlined, ReloadOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { 
  selectPhoto, 
  unselectPhoto, 
  selectAllPhotos, 
  unselectAllPhotos,
  downloadPhoto,
  downloadMultiplePhotos
} from '../services/generationSlice';
import './PhotoPreview.scss';

const PhotoPreview = ({ onRegenerate, onBack }) => {
  const dispatch = useDispatch();
  const { generatedPhotos, selectedPhotos, downloadLoading } = useSelector((state) => state.generation);
  
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  
  // 处理照片选择
  const handleSelect = (photoId) => {
    if (selectedPhotos.includes(photoId)) {
      dispatch(unselectPhoto(photoId));
    } else {
      dispatch(selectPhoto(photoId));
    }
  };
  
  // 全选/取消全选
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(selectAllPhotos());
    } else {
      dispatch(unselectAllPhotos());
    }
  };
  
  // 预览照片
  const handlePreview = (photo) => {
    setPreviewImage(photo.url);
    setPreviewTitle(`证件照 #${photo.id}`);
    setPreviewVisible(true);
  };
  
  // 下载选中的照片
  const handleDownload = async () => {
    if (selectedPhotos.length === 0) {
      message.warning('请先选择要下载的照片');
      return;
    }
    
    try {
      if (selectedPhotos.length === 1) {
        // 下载单张照片
        await dispatch(downloadPhoto({
          photoId: selectedPhotos[0],
          fileName: `photo_${selectedPhotos[0]}.jpg`
        })).unwrap();
        message.success('下载成功');
      } else {
        // 批量下载
        await dispatch(downloadMultiplePhotos({
          photoIds: selectedPhotos
        })).unwrap();
        message.success('批量下载成功');
      }
    } catch (error) {
      message.error(error.message || '下载失败，请重试');
    }
  };
  
  // 渲染质量评分
  const renderQualityScore = (score) => {
    return (
      <div className="quality-score">
        <span>质量评分：</span>
        <Rate disabled defaultValue={Math.round(score * 5)} />
        <span className="score-value">{(score * 100).toFixed(0)}%</span>
      </div>
    );
  };
  
  return (
    <div className="photo-preview">
      <Card 
        title="证件照生成结果" 
        className="preview-card"
        extra={
          <div className="card-actions">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={onBack}
              style={{ marginRight: 8 }}
            >
              返回上传
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={onRegenerate}
            >
              重新生成
            </Button>
          </div>
        }
      >
        {generatedPhotos.length === 0 ? (
          <div className="empty-result">
            <Spin tip="正在生成证件照..." />
          </div>
        ) : (
          <>
            <div className="select-actions">
              <Checkbox 
                onChange={handleSelectAll}
                checked={selectedPhotos.length === generatedPhotos.length && generatedPhotos.length > 0}
                indeterminate={selectedPhotos.length > 0 && selectedPhotos.length < generatedPhotos.length}
              >
                全选
              </Checkbox>
              
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownload}
                loading={downloadLoading}
                disabled={selectedPhotos.length === 0}
              >
                下载选中
              </Button>
            </div>
            
            <Row gutter={[16, 16]} className="photos-grid">
              {generatedPhotos.map((photo) => (
                <Col xs={12} sm={8} md={6} lg={4} xl={4} key={photo.id}>
                  <div className="photo-item">
                    <div className="photo-card">
                      <div className="photo-checkbox">
                        <Checkbox 
                          checked={selectedPhotos.includes(photo.id)}
                          onChange={() => handleSelect(photo.id)}
                        />
                      </div>
                      <div 
                        className="photo-image" 
                        onClick={() => handlePreview(photo)}
                        style={{ backgroundImage: `url(${photo.url})` }}
                      >
                        <div className="preview-overlay">
                          <EyeOutlined />
                          <span>预览</span>
                        </div>
                      </div>
                      {renderQualityScore(photo.quality_score)}
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </>
        )}
      </Card>
      
      <Modal
        visible={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="证件照预览" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default PhotoPreview;