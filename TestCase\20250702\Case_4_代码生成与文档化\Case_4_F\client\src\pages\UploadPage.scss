.upload-page {
  .page-title {
    margin-bottom: 16px;
  }
  
  .page-description {
    margin-bottom: 32px;
    font-size: 16px;
  }
  
  .upload-card {
    height: 100%;
    
    .upload-dragger {
      padding: 24px;
      
      .ant-upload-drag-icon {
        font-size: 48px;
        color: #1890ff;
      }
      
      .ant-upload-text {
        font-size: 16px;
        margin: 8px 0;
      }
      
      .ant-upload-hint {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    
    .upload-progress {
      margin-top: 24px;
    }
  }
  
  .options-card {
    height: 100%;
    
    .option-section {
      margin-bottom: 24px;
      
      h5 {
        margin-bottom: 16px;
      }
      
      .ant-radio-group {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .ant-radio-button-wrapper {
          margin-bottom: 8px;
          min-width: 80px;
          text-align: center;
        }
      }
    }
    
    .action-section {
      margin-top: 32px;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .upload-page {
    .options-card {
      margin-top: 16px;
    }
  }
} 