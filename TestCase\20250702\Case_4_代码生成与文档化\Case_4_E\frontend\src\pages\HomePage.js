import React from 'react';
import { Typography, Button, Row, Col, Card, Steps, Image } from 'antd';
import { UploadOutlined, RobotOutlined, DownloadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const HomePage = () => {
  const navigate = useNavigate();

  const features = [
    {
      title: '多种证件照规格',
      description: '支持1寸、2寸、小2寸等多种标准证件照规格，满足不同场景需求。',
      icon: <img src="/placeholder-icon.png" alt="规格" style={{ width: 48, height: 48 }} />,
    },
    {
      title: 'AI智能处理',
      description: '采用先进的AI技术，自动调整光线、姿势和表情，生成符合标准的证件照。',
      icon: <RobotOutlined style={{ fontSize: 48, color: '#1890ff' }} />,
    },
    {
      title: '快速下载',
      description: '生成后即可下载高清证件照，支持单张下载和批量打包下载。',
      icon: <DownloadOutlined style={{ fontSize: 48, color: '#52c41a' }} />,
    },
  ];

  const steps = [
    {
      title: '上传照片',
      description: '上传您的个人照片作为原始素材',
      icon: <UploadOutlined />,
    },
    {
      title: '选择规格',
      description: '选择需要的证件照规格和背景颜色',
      icon: <img src="/placeholder-icon.png" alt="选择规格" style={{ width: 16, height: 16 }} />,
    },
    {
      title: 'AI处理',
      description: 'AI自动处理照片，生成多个版本供选择',
      icon: <RobotOutlined />,
    },
    {
      title: '下载使用',
      description: '选择满意的照片进行下载使用',
      icon: <DownloadOutlined />,
    },
  ];

  return (
    <div className="home-page">
      {/* 英雄区域 */}
      <div className="hero-section" style={{ textAlign: 'center', padding: '60px 0' }}>
        <Title>AI证件照生成平台</Title>
        <Paragraph style={{ fontSize: 18, margin: '20px 0 40px' }}>
          只需上传一张照片，AI智能生成符合标准的高质量证件照
        </Paragraph>
        <Button 
          type="primary" 
          size="large" 
          icon={<UploadOutlined />}
          onClick={() => navigate('/upload')}
        >
          立即体验
        </Button>
      </div>

      {/* 特点展示 */}
      <div className="features-section" style={{ padding: '60px 0', backgroundColor: '#f5f5f5' }}>
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 40 }}>产品特点</Title>
          <Row gutter={[32, 32]} justify="center">
            {features.map((feature, index) => (
              <Col xs={24} sm={12} md={8} key={index}>
                <Card style={{ height: '100%', textAlign: 'center' }}>
                  <div style={{ marginBottom: 20 }}>{feature.icon}</div>
                  <Title level={4}>{feature.title}</Title>
                  <Paragraph>{feature.description}</Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 使用流程 */}
      <div className="process-section" style={{ padding: '60px 0' }}>
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 40 }}>使用流程</Title>
          <Steps
            current={-1}
            items={steps}
            style={{ maxWidth: 800, margin: '0 auto' }}
          />
        </div>
      </div>

      {/* 案例展示 */}
      <div className="showcase-section" style={{ padding: '60px 0', backgroundColor: '#f5f5f5' }}>
        <div className="container">
          <Title level={2} style={{ textAlign: 'center', marginBottom: 40 }}>效果展示</Title>
          <Row gutter={[16, 16]} justify="center">
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card
                cover={<div style={{ height: 200, backgroundColor: '#e6f7ff', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>原始照片</div>}
                style={{ textAlign: 'center' }}
              >
                <Card.Meta title="原始照片" />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card
                cover={<div style={{ height: 200, backgroundColor: '#e6f7ff', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>蓝底证件照</div>}
                style={{ textAlign: 'center' }}
              >
                <Card.Meta title="蓝底证件照" />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card
                cover={<div style={{ height: 200, backgroundColor: '#e6f7ff', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>红底证件照</div>}
                style={{ textAlign: 'center' }}
              >
                <Card.Meta title="红底证件照" />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Card
                cover={<div style={{ height: 200, backgroundColor: '#e6f7ff', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>白底证件照</div>}
                style={{ textAlign: 'center' }}
              >
                <Card.Meta title="白底证件照" />
              </Card>
            </Col>
          </Row>
        </div>
      </div>

      {/* 号召性用语 */}
      <div className="cta-section" style={{ padding: '60px 0', textAlign: 'center' }}>
        <Title level={2}>立即开始生成您的证件照</Title>
        <Paragraph style={{ fontSize: 16, margin: '20px 0 30px' }}>
          快速、便捷、高质量，满足您的各类证件照需求
        </Paragraph>
        <Button 
          type="primary" 
          size="large"
          onClick={() => navigate('/upload')}
        >
          开始上传
        </Button>
      </div>
    </div>
  );
};

export default HomePage;