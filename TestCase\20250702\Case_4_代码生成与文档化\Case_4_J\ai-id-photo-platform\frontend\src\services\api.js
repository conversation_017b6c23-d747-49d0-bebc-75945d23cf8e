import axios from 'axios'
import { message } from 'antd'
import store from '@/store'
import { clearAuth, setToken } from '@/store/slices/authSlice'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const state = store.getState()
    const token = state.auth.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      // 处理不同的HTTP状态码
      switch (status) {
        case 401:
          // 未授权，清除认证信息
          store.dispatch(clearAuth())
          message.error(data?.message || '登录已过期，请重新登录')
          // 可以在这里跳转到登录页面
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
          
        case 403:
          message.error(data?.message || '权限不足')
          break
          
        case 404:
          message.error(data?.message || '请求的资源不存在')
          break
          
        case 413:
          message.error(data?.message || '文件大小超过限制')
          break
          
        case 422:
          message.error(data?.message || '请求参数验证失败')
          break
          
        case 429:
          message.error(data?.message || '请求过于频繁，请稍后再试')
          break
          
        case 500:
          message.error(data?.message || '服务器内部错误')
          break
          
        default:
          message.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      message.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 创建文件上传专用的axios实例
export const uploadApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 60000, // 上传文件需要更长的超时时间
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 为上传API添加相同的拦截器
uploadApi.interceptors.request.use(
  (config) => {
    const state = store.getState()
    const token = state.auth.token
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

uploadApi.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const { response } = error
    
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          store.dispatch(clearAuth())
          message.error(data?.message || '登录已过期，请重新登录')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
          
        case 413:
          message.error(data?.message || '文件大小超过限制')
          break
          
        default:
          message.error(data?.message || `上传失败 (${status})`)
      }
    } else {
      message.error('网络连接失败，请检查网络设置')
    }
    
    return Promise.reject(error)
  }
)

export default api
