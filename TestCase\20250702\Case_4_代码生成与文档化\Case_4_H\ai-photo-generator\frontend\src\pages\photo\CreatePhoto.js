import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Select, 
  Slider, 
  Upload, 
  message, 
  Divider, 
  Space, 
  Row, 
  Col,
  Alert,
  Tooltip,
  Spin,
  Tag,
  Modal
} from 'antd';
import {
  PictureOutlined,
  UploadOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  QuestionCircleOutlined,
  CrownOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { createPhoto } from '../../store/photoSlice';
import { fetchUserSubscription } from '../../store/subscriptionSlice';
import { ROUTES, PHOTO_STYLES, PHOTO_ASPECT_RATIOS } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 样式化组件
 */
const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-bottom: 24px;
`;

const PreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background-color: ${({ theme }) => theme.colorBgContainer};
  border: 1px dashed ${({ theme }) => theme.colorBorderSecondary};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
`;

const GeneratingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
`;

const PromptSuggestion = styled(Button)`
  margin: 0 8px 8px 0;
`;

const StylePreview = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  border: 2px solid transparent;
  transition: all 0.3s;
  
  &:hover {
    background-color: ${({ theme }) => theme.colorBgTextHover};
  }
  
  &.selected {
    border-color: ${({ theme }) => theme.colorPrimary};
    background-color: ${({ theme }) => theme.colorBgTextActive};
  }
  
  img {
    width: 100%;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 8px;
  }
`;

const AspectRatioOption = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  border: 2px solid transparent;
  transition: all 0.3s;
  
  &:hover {
    background-color: ${({ theme }) => theme.colorBgTextHover};
  }
  
  &.selected {
    border-color: ${({ theme }) => theme.colorPrimary};
    background-color: ${({ theme }) => theme.colorBgTextActive};
  }
  
  .aspect-ratio-box {
    width: 60px;
    height: 60px;
    background-color: ${({ theme }) => theme.colorPrimary};
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
  }
  
  .aspect-1-1 {
    width: 60px;
    height: 60px;
  }
  
  .aspect-4-3 {
    width: 60px;
    height: 45px;
  }
  
  .aspect-3-4 {
    width: 45px;
    height: 60px;
  }
  
  .aspect-16-9 {
    width: 60px;
    height: 34px;
  }
  
  .aspect-9-16 {
    width: 34px;
    height: 60px;
  }
`;

const UpgradeAlert = styled(Alert)`
  margin-bottom: 24px;
`;

/**
 * 照片创建页面组件
 * 
 * 允许用户生成新的AI照片
 * 
 * @returns {JSX.Element} 照片创建页面组件
 */
const CreatePhoto = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从URL参数中获取提示词
  const queryParams = new URLSearchParams(location.search);
  const promptFromUrl = queryParams.get('prompt');
  
  // 从Redux获取状态
  const { loading, generatedPhoto } = useSelector((state) => state.photo);
  const { stats } = useSelector((state) => state.dashboard);
  const { userSubscription } = useSelector((state) => state.subscription);
  
  // 本地状态
  const [form] = Form.useForm();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [referenceImage, setReferenceImage] = useState(null);
  const [selectedStyle, setSelectedStyle] = useState(PHOTO_STYLES[0].value);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState(PHOTO_ASPECT_RATIOS[0].value);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  
  // 提示词建议
  const promptSuggestions = [
    '专业商务人像',
    '在巴黎埃菲尔铁塔前的照片',
    '在海滩上的休闲照片',
    '在山顶上的风景照',
    '在咖啡厅工作的照片',
    '参加正式活动的照片'
  ];
  
  // 判断是否有高级订阅
  const hasPremiumSubscription = userSubscription && 
    userSubscription.status === 'active' && 
    userSubscription.planType !== 'free';
  
  // 判断是否达到使用限制
  const hasReachedLimit = stats?.usageCount >= stats?.usageLimit;
  
  // 获取用户订阅信息
  useEffect(() => {
    dispatch(fetchUserSubscription());
  }, [dispatch]);
  
  // 如果URL中有提示词，填充表单
  useEffect(() => {
    if (promptFromUrl) {
      form.setFieldsValue({ prompt: promptFromUrl });
    }
  }, [form, promptFromUrl]);
  
  // 处理表单提交
  const handleSubmit = async (values) => {
    // 检查是否达到使用限制
    if (hasReachedLimit && !hasPremiumSubscription) {
      setShowUpgradeModal(true);
      return;
    }
    
    try {
      const formData = new FormData();
      formData.append('prompt', values.prompt);
      formData.append('style', selectedStyle);
      formData.append('aspectRatio', selectedAspectRatio);
      formData.append('negativePrompt', values.negativePrompt || '');
      
      if (referenceImage) {
        formData.append('referenceImage', referenceImage);
      }
      
      await dispatch(createPhoto(formData)).unwrap();
      message.success('照片生成成功');
    } catch (error) {
      message.error(error?.message || '照片生成失败，请稍后再试');
    }
  };
  
  // 处理参考图片上传
  const handleReferenceImageChange = (info) => {
    if (info.file.status === 'done') {
      setReferenceImage(info.file.originFileObj);
    }
  };
  
  // 处理参考图片预览
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await new Promise(resolve => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    
    setPreviewVisible(true);
  };
  
  // 处理提示词建议点击
  const handleSuggestionClick = (suggestion) => {
    const currentPrompt = form.getFieldValue('prompt') || '';
    form.setFieldsValue({ prompt: currentPrompt ? `${currentPrompt}, ${suggestion}` : suggestion });
  };
  
  // 处理样式选择
  const handleStyleSelect = (style) => {
    setSelectedStyle(style);
  };
  
  // 处理宽高比选择
  const handleAspectRatioSelect = (aspectRatio) => {
    setSelectedAspectRatio(aspectRatio);
  };
  
  // 渲染样式选项
  const renderStyleOptions = () => {
    return (
      <Row gutter={[16, 16]}>
        {PHOTO_STYLES.map((style) => (
          <Col xs={12} sm={8} md={6} key={style.value}>
            <StylePreview 
              className={selectedStyle === style.value ? 'selected' : ''}
              onClick={() => handleStyleSelect(style.value)}
            >
              <img src={style.preview} alt={style.label} />
              <Text strong>{style.label}</Text>
            </StylePreview>
          </Col>
        ))}
      </Row>
    );
  };
  
  // 渲染宽高比选项
  const renderAspectRatioOptions = () => {
    return (
      <Row gutter={[16, 16]}>
        {PHOTO_ASPECT_RATIOS.map((ratio) => (
          <Col xs={12} sm={8} md={4} key={ratio.value}>
            <AspectRatioOption 
              className={selectedAspectRatio === ratio.value ? 'selected' : ''}
              onClick={() => handleAspectRatioSelect(ratio.value)}
            >
              <div className={`aspect-ratio-box aspect-${ratio.value.replace(':', '-')}`}>
                {ratio.value}
              </div>
              <Text>{ratio.label}</Text>
            </AspectRatioOption>
          </Col>
        ))}
      </Row>
    );
  };
  
  return (
    <div>
      <Title level={2}>创建新照片</Title>
      <Paragraph>使用AI生成逼真的照片，根据您的描述和偏好定制。</Paragraph>
      
      {/* 使用限制提醒 */}
      {!hasPremiumSubscription && (
        <UpgradeAlert
          message={
            <Space>
              <span>免费版本限制: {stats?.usageCount || 0}/{stats?.usageLimit || 0} 张照片</span>
              <Button 
                type="primary" 
                size="small" 
                icon={<CrownOutlined />}
                onClick={() => navigate(ROUTES.SUBSCRIPTION)}
              >
                升级获取无限额度
              </Button>
            </Space>
          }
          type="info"
          showIcon
        />
      )}
      
      {/* 高级功能提醒 */}
      {!hasPremiumSubscription && (
        <UpgradeAlert
          message="升级到高级版以解锁更多功能：参考图片上传、高级样式选项和更高质量的生成结果"
          type="warning"
          showIcon
          icon={<CrownOutlined />}
        />
      )}
      
      <Row gutter={24}>
        <Col xs={24} lg={12}>
          {/* 照片生成表单 */}
          <StyledCard title="照片设置">
            <Form
              form={form}
              name="createPhoto"
              onFinish={handleSubmit}
              layout="vertical"
              requiredMark="optional"
            >
              {/* 提示词输入 */}
              <Form.Item
                name="prompt"
                label={
                  <Space>
                    <span>描述您想要的照片</span>
                    <Tooltip title="详细描述您想要的照片场景、人物、表情、服装、背景等。描述越详细，生成的照片越符合您的期望。">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                rules={[{ required: true, message: '请输入照片描述' }]}
              >
                <TextArea 
                  placeholder="例如：一位穿着正式商务套装的亚洲女性，在现代办公室环境中微笑着" 
                  rows={4} 
                  showCount 
                  maxLength={500} 
                />
              </Form.Item>
              
              {/* 提示词建议 */}
              <div style={{ marginBottom: 24 }}>
                <Text type="secondary">建议添加：</Text>
                <div style={{ marginTop: 8 }}>
                  {promptSuggestions.map((suggestion) => (
                    <PromptSuggestion 
                      key={suggestion} 
                      size="small"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </PromptSuggestion>
                  ))}
                </div>
              </div>
              
              {/* 负面提示词 */}
              <Form.Item
                name="negativePrompt"
                label={
                  <Space>
                    <span>负面提示词（可选）</span>
                    <Tooltip title="指定您不希望在照片中出现的元素，如'模糊'、'变形'、'多余的肢体'等">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <Input 
                  placeholder="例如：模糊、变形、低质量" 
                />
              </Form.Item>
              
              {/* 参考图片上传 */}
              <Form.Item
                label={
                  <Space>
                    <span>参考图片（可选）</span>
                    {!hasPremiumSubscription && <Tag color="gold">高级功能</Tag>}
                    <Tooltip title="上传参考图片以影响生成结果的风格和构图">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                <Upload
                  name="referenceImage"
                  listType="picture-card"
                  maxCount={1}
                  beforeUpload={() => false}
                  onChange={handleReferenceImageChange}
                  onPreview={handlePreview}
                  disabled={!hasPremiumSubscription}
                >
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>上传图片</div>
                  </div>
                </Upload>
                {!hasPremiumSubscription && (
                  <Text type="secondary">
                    <CrownOutlined /> 升级到高级版以使用此功能
                  </Text>
                )}
              </Form.Item>
              
              <Divider />
              
              {/* 样式选择 */}
              <Form.Item
                label={
                  <Space>
                    <span>照片风格</span>
                    <Tooltip title="选择照片的整体风格和美学效果">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                {renderStyleOptions()}
              </Form.Item>
              
              {/* 宽高比选择 */}
              <Form.Item
                label={
                  <Space>
                    <span>宽高比</span>
                    <Tooltip title="选择照片的宽高比例">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Space>
                }
              >
                {renderAspectRatioOptions()}
              </Form.Item>
              
              {/* 生成按钮 */}
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<PictureOutlined />} 
                  loading={loading}
                  size="large"
                  block
                >
                  生成照片
                </Button>
              </Form.Item>
            </Form>
          </StyledCard>
        </Col>
        
        <Col xs={24} lg={12}>
          {/* 预览区域 */}
          <StyledCard title="预览">
            {loading ? (
              <GeneratingContainer>
                <Spin size="large" />
                <Title level={4} style={{ marginTop: 24 }}>
                  正在生成您的照片...
                </Title>
                <Paragraph type="secondary">
                  这可能需要几秒钟时间，请耐心等待
                </Paragraph>
              </GeneratingContainer>
            ) : generatedPhoto ? (
              <PreviewContainer>
                <PreviewImage src={generatedPhoto.url} alt="生成的照片" />
                <div style={{ marginTop: 16, textAlign: 'center' }}>
                  <Space>
                    <Button 
                      type="primary" 
                      onClick={() => navigate(`${ROUTES.PHOTOS}/${generatedPhoto.id}`)}
                    >
                      查看详情
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      创建新照片
                    </Button>
                  </Space>
                </div>
              </PreviewContainer>
            ) : (
              <PreviewContainer>
                <PictureOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
                <Title level={4}>您的照片将在这里显示</Title>
                <Paragraph type="secondary" style={{ textAlign: 'center' }}>
                  填写左侧表单并点击"生成照片"按钮来创建您的AI照片
                </Paragraph>
                <div style={{ marginTop: 16 }}>
                  <Space direction="vertical" align="center">
                    <Text>
                      <BulbOutlined /> 提示：详细的描述会产生更好的结果
                    </Text>
                    <Text>
                      <BulbOutlined /> 提示：尝试不同的风格和宽高比
                    </Text>
                  </Space>
                </div>
              </PreviewContainer>
            )}
          </StyledCard>
        </Col>
      </Row>
      
      {/* 参考图片预览模态框 */}
      <Modal
        visible={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img 
          alt="参考图片预览" 
          style={{ width: '100%' }} 
          src={referenceImage?.preview} 
        />
      </Modal>
      
      {/* 升级提示模态框 */}
      <Modal
        title="已达到免费版使用限制"
        visible={showUpgradeModal}
        onCancel={() => setShowUpgradeModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowUpgradeModal(false)}>
            取消
          </Button>,
          <Button 
            key="upgrade" 
            type="primary" 
            icon={<CrownOutlined />}
            onClick={() => navigate(ROUTES.SUBSCRIPTION)}
          >
            升级到高级版
          </Button>,
        ]}
      >
        <Paragraph>
          您已达到本月的免费照片生成限制。升级到高级版以获取更多功能：
        </Paragraph>
        <ul>
          <li>无限照片生成</li>
          <li>更高质量的生成结果</li>
          <li>参考图片上传功能</li>
          <li>优先处理队列</li>
          <li>更多高级样式和选项</li>
        </ul>
      </Modal>
    </div>
  );
};

export default CreatePhoto;