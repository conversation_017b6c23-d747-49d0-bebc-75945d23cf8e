import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Row,
  Col,
  Card,
  Button,
  Tabs,
  Typography,
  Spin,
  message,
  Divider,
  Space,
  Modal,
  List,
  Tooltip,
  Input,
} from 'antd';
import {
  DownloadOutlined,
  EditOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
  DeleteOutlined,
  CopyOutlined,
  PrinterOutlined,
  SaveOutlined,
  WechatOutlined,
  QqOutlined,
  WeiboOutlined,
} from '@ant-design/icons';
import { getPhotoById, downloadPhoto, toggleFavorite, deletePhoto } from '../services/photoService';
import { setCurrentPhoto } from '../store/photoSlice';
import { addNotification } from '../store/uiSlice';
import { ROUTES, PHOTO_SIZES } from '../utils/constants';
import LoadingSpinner from '../components/common/LoadingSpinner';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const Result = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  // 从Redux获取当前照片
  const currentPhoto = useSelector((state) => state.photos.currentPhoto);
  
  // 本地状态
  const [loading, setLoading] = useState(true);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('preview');
  const [selectedSize, setSelectedSize] = useState(PHOTO_SIZES[0].value);
  
  // 加载照片数据
  useEffect(() => {
    const fetchPhotoData = async () => {
      try {
        setLoading(true);
        const photoData = await getPhotoById(id);
        dispatch(setCurrentPhoto(photoData));
      } catch (error) {
        console.error('获取照片失败:', error);
        message.error('获取照片失败，请重试');
        navigate(ROUTES.HOME);
      } finally {
        setLoading(false);
      }
    };
    
    // 如果Redux中没有当前照片或ID不匹配，则从API获取
    if (!currentPhoto || currentPhoto.id !== id) {
      fetchPhotoData();
    } else {
      setLoading(false);
    }
  }, [id, dispatch, navigate, currentPhoto]);
  
  // 处理下载
  const handleDownload = async (format = 'jpg') => {
    try {
      setDownloadLoading(true);
      await downloadPhoto(id, selectedSize, format);
      message.success('照片下载成功');
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败，请重试');
    } finally {
      setDownloadLoading(false);
    }
  };
  
  // 处理编辑
  const handleEdit = () => {
    navigate(`${ROUTES.EDIT}/${id}`);
  };
  
  // 处理收藏切换
  const handleToggleFavorite = async () => {
    try {
      const updatedPhoto = await toggleFavorite(id);
      dispatch(setCurrentPhoto(updatedPhoto));
      
      const message = updatedPhoto.isFavorite ? '已添加到收藏' : '已从收藏中移除';
      dispatch(addNotification({
        type: 'success',
        title: '收藏状态更新',
        message,
      }));
    } catch (error) {
      console.error('更新收藏状态失败:', error);
      message.error('操作失败，请重试');
    }
  };
  
  // 处理删除
  const handleDelete = async () => {
    try {
      await deletePhoto(id);
      setDeleteModalVisible(false);
      
      dispatch(addNotification({
        type: 'success',
        title: '删除成功',
        message: '照片已成功删除',
      }));
      
      navigate(ROUTES.GALLERY);
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败，请重试');
    }
  };
  
  // 处理分享
  const handleShare = () => {
    setShareModalVisible(true);
  };
  
  // 复制分享链接
  const copyShareLink = () => {
    const shareUrl = `${window.location.origin}${ROUTES.SHARE}/${id}`;
    navigator.clipboard.writeText(shareUrl)
      .then(() => {
        message.success('链接已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };
  
  // 处理打印
  const handlePrint = () => {
    window.print();
  };
  
  // 如果正在加载，显示加载状态
  if (loading) {
    return <LoadingSpinner tip="正在加载照片..." />;
  }
  
  // 如果没有照片数据，显示错误信息
  if (!currentPhoto) {
    return (
      <div className="error-container">
        <Title level={3}>照片不存在或已被删除</Title>
        <Button type="primary" onClick={() => navigate(ROUTES.HOME)}>
          返回首页
        </Button>
      </div>
    );
  }
  
  return (
    <div className="result-page">
      <Row gutter={[24, 24]}>
        <Col xs={24} md={16}>
          <Card className="photo-preview-card">
            <div className="photo-preview-container">
              <img 
                src={currentPhoto.url} 
                alt={currentPhoto.name} 
                className="photo-preview-image" 
              />
            </div>
            
            <div className="photo-actions">
              <Space size="middle">
                <Tooltip title={currentPhoto.isFavorite ? '取消收藏' : '收藏'}>
                  <Button
                    icon={currentPhoto.isFavorite ? <StarFilled /> : <StarOutlined />}
                    onClick={handleToggleFavorite}
                    className={currentPhoto.isFavorite ? 'favorite-active' : ''}
                  >
                    {currentPhoto.isFavorite ? '已收藏' : '收藏'}
                  </Button>
                </Tooltip>
                <Button icon={<EditOutlined />} onClick={handleEdit}>
                  编辑
                </Button>
                <Button icon={<ShareAltOutlined />} onClick={handleShare}>
                  分享
                </Button>
                <Button 
                  icon={<DeleteOutlined />} 
                  danger
                  onClick={() => setDeleteModalVisible(true)}
                >
                  删除
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} md={8}>
          <Card className="photo-info-card">
            <Title level={4}>照片信息</Title>
            <div className="photo-info">
              <div className="info-item">
                <Text strong>名称:</Text>
                <Text>{currentPhoto.name}</Text>
              </div>
              <div className="info-item">
                <Text strong>类型:</Text>
                <Text>{currentPhoto.typeName || currentPhoto.type}</Text>
              </div>
              <div className="info-item">
                <Text strong>尺寸:</Text>
                <Text>{currentPhoto.width} x {currentPhoto.height} 像素</Text>
              </div>
              <div className="info-item">
                <Text strong>创建时间:</Text>
                <Text>{new Date(currentPhoto.createdAt).toLocaleString()}</Text>
              </div>
              <div className="info-item">
                <Text strong>文件大小:</Text>
                <Text>{currentPhoto.size}</Text>
              </div>
            </div>
            
            <Divider />
            
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="下载" key="download">
                <div className="download-options">
                  <div className="size-selection">
                    <Text strong>选择尺寸:</Text>
                    <div className="size-options">
                      {PHOTO_SIZES.map((size) => (
                        <Button
                          key={size.value}
                          type={selectedSize === size.value ? 'primary' : 'default'}
                          onClick={() => setSelectedSize(size.value)}
                          className="size-option-btn"
                        >
                          {size.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <div className="format-options">
                    <Text strong>选择格式:</Text>
                    <div className="download-buttons">
                      <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload('jpg')}
                        loading={downloadLoading}
                        block
                      >
                        下载 JPG
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload('png')}
                        loading={downloadLoading}
                        block
                      >
                        下载 PNG
                      </Button>
                      <Button
                        icon={<PrinterOutlined />}
                        onClick={handlePrint}
                        block
                      >
                        打印
                      </Button>
                    </div>
                  </div>
                </div>
              </TabPane>
              
              <TabPane tab="分享" key="share">
                <div className="share-options">
                  <Paragraph>
                    通过以下方式分享您的照片:
                  </Paragraph>
                  
                  <div className="share-link">
                    <Input.Group compact>
                      <Input
                        style={{ width: 'calc(100% - 70px)' }}
                        value={`${window.location.origin}${ROUTES.SHARE}/${id}`}
                        readOnly
                      />
                      <Tooltip title="复制链接">
                        <Button icon={<CopyOutlined />} onClick={copyShareLink} />
                      </Tooltip>
                    </Input.Group>
                  </div>
                  
                  <Divider />
                  
                  <div className="social-share">
                    <Button icon={<WechatOutlined />} className="wechat-share-btn">
                      微信
                    </Button>
                    <Button icon={<QqOutlined />} className="qq-share-btn">
                      QQ
                    </Button>
                    <Button icon={<WeiboOutlined />} className="weibo-share-btn">
                      微博
                    </Button>
                  </div>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
      
      {/* 分享模态框 */}
      <Modal
        title="分享照片"
        visible={shareModalVisible}
        onCancel={() => setShareModalVisible(false)}
        footer={null}
      >
        <div className="share-modal-content">
          <Paragraph>
            通过以下方式分享您的照片:
          </Paragraph>
          
          <div className="share-link-modal">
            <Input.Group compact>
              <Input
                style={{ width: 'calc(100% - 70px)' }}
                value={`${window.location.origin}${ROUTES.SHARE}/${id}`}
                readOnly
              />
              <Tooltip title="复制链接">
                <Button icon={<CopyOutlined />} onClick={copyShareLink} />
              </Tooltip>
            </Input.Group>
          </div>
          
          <Divider />
          
          <div className="social-share-modal">
            <List
              grid={{ gutter: 16, column: 3 }}
              dataSource={[
                { icon: <WechatOutlined />, name: '微信', color: '#07C160' },
                { icon: <QqOutlined />, name: 'QQ', color: '#12B7F5' },
                { icon: <WeiboOutlined />, name: '微博', color: '#E6162D' },
              ]}
              renderItem={item => (
                <List.Item>
                  <Button 
                    icon={item.icon} 
                    style={{ color: item.color, borderColor: item.color }}
                  >
                    {item.name}
                  </Button>
                </List.Item>
              )}
            />
          </div>
        </div>
      </Modal>
      
      {/* 删除确认模态框 */}
      <Modal
        title="确认删除"
        visible={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        onOk={handleDelete}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除这张照片吗？此操作无法撤销。</p>
      </Modal>
    </div>
  );
};

export default Result;