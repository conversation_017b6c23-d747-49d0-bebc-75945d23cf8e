import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { 
  Typography, 
  Card, 
  Button, 
  Row, 
  Col, 
  Spin, 
  Alert, 
  Divider, 
  Space, 
  Modal, 
  message,
  Descriptions,
  Tag
} from 'antd';
import { 
  DownloadOutlined, 
  EditOutlined, 
  ShareAltOutlined, 
  DeleteOutlined,
  PrinterOutlined,
  StarOutlined,
  StarFilled
} from '@ant-design/icons';
import { 
  fetchPhotoById, 
  downloadPhoto, 
  deletePhoto, 
  toggleFavorite,
  selectCurrentPhoto, 
  selectPhotoLoading, 
  selectPhotoError 
} from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS, BACKGROUND_COLORS } from '../../utils/constants';
import PhotoPreview from '../../components/photo/PhotoPreview';
import ShareModal from '../../components/photo/ShareModal';

const { Title, Paragraph, Text } = Typography;

/**
 * 照片结果页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const PhotoResultPage = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const photo = useSelector(selectCurrentPhoto);
  const loading = useSelector(selectPhotoLoading);
  const error = useSelector(selectPhotoError);
  
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  // 获取照片详情
  useEffect(() => {
    if (id) {
      dispatch(fetchPhotoById(id));
    }
  }, [dispatch, id]);
  
  /**
   * 处理下载照片
   */
  const handleDownload = async () => {
    if (!photo) return;
    
    setDownloadLoading(true);
    try {
      await dispatch(downloadPhoto(photo.id)).unwrap();
      message.success('照片下载成功');
    } catch (err) {
      message.error('下载失败: ' + err.message);
    } finally {
      setDownloadLoading(false);
    }
  };
  
  /**
   * 处理删除照片
   */
  const handleDelete = async () => {
    if (!photo) return;
    
    try {
      await dispatch(deletePhoto(photo.id)).unwrap();
      message.success('照片删除成功');
      navigate(ROUTES.MY_PHOTOS);
    } catch (err) {
      message.error('删除失败: ' + err.message);
    } finally {
      setDeleteModalVisible(false);
    }
  };
  
  /**
   * 处理收藏/取消收藏
   */
  const handleToggleFavorite = async () => {
    if (!photo) return;
    
    try {
      await dispatch(toggleFavorite(photo.id)).unwrap();
      message.success(photo.isFavorite ? '已取消收藏' : '已添加到收藏');
    } catch (err) {
      message.error('操作失败: ' + err.message);
    }
  };
  
  /**
   * 处理打印照片
   */
  const handlePrint = () => {
    if (!photo) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>打印证件照 - ${photo.name || '未命名'}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              text-align: center;
            }
            img {
              max-width: 100%;
              height: auto;
            }
            .print-container {
              display: inline-block;
              margin: 0 auto;
            }
            @media print {
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="no-print" style="margin-bottom: 20px;">
              <h2>${photo.name || '未命名'}</h2>
              <p>点击下方的打印按钮开始打印</p>
              <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">打印</button>
              <button onclick="window.close();" style="padding: 10px 20px; margin-left: 10px; cursor: pointer;">关闭</button>
              <hr style="margin: 20px 0;" />
            </div>
            <img src="${photo.resultUrl}" alt="证件照" />
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
  };
  
  // 如果正在加载，显示加载状态
  if (loading && !photo) {
    return (
      <div className="photo-result-page page-container">
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </div>
    );
  }
  
  // 如果加载失败，显示错误信息
  if (error && !photo) {
    return (
      <div className="photo-result-page page-container">
        <Alert
          message="加载失败"
          description={error || '无法加载照片信息，请稍后再试'}
          type="error"
          showIcon
          action={
            <Button type="primary">
              <Link to={ROUTES.CREATE_PHOTO}>重新创建</Link>
            </Button>
          }
        />
      </div>
    );
  }
  
  // 如果没有照片数据，显示空状态
  if (!photo) {
    return (
      <div className="photo-result-page page-container">
        <Alert
          message="照片不存在"
          description="未找到相关照片信息，可能已被删除或链接无效"
          type="warning"
          showIcon
          action={
            <Button type="primary">
              <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
            </Button>
          }
        />
      </div>
    );
  }
  
  // 获取照片规格信息
  const specInfo = PHOTO_SPECS[photo.spec] || { name: '未知规格', width: '-', height: '-' };
  
  // 获取背景颜色信息
  const bgColorInfo = BACKGROUND_COLORS[photo.backgroundColor] || { name: '未知', hex: '#ffffff' };
  
  return (
    <div className="photo-result-page page-container">
      <div className="page-title">
        <Title level={2}>照片结果</Title>
        <Space>
          <Button 
            icon={photo.isFavorite ? <StarFilled /> : <StarOutlined />}
            onClick={handleToggleFavorite}
          >
            {photo.isFavorite ? '取消收藏' : '收藏'}
          </Button>
          <Button 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleDownload}
            loading={downloadLoading}
            className="btn-primary"
          >
            下载
          </Button>
        </Space>
      </div>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} md={12}>
          <Card bordered={false} title="证件照预览">
            <div style={{ textAlign: 'center' }}>
              {photo.resultUrl ? (
                <img 
                  src={photo.resultUrl} 
                  alt="证件照" 
                  className="preview-image"
                  style={{ maxWidth: '100%', maxHeight: 400 }}
                />
              ) : (
                <div className="loading-container">
                  <Spin size="large" />
                </div>
              )}
            </div>
            
            <Divider />
            
            <div style={{ textAlign: 'center' }}>
              <Space wrap>
                <Button icon={<EditOutlined />}>
                  <Link to={`${ROUTES.EDIT_PHOTO}/${photo.id}`}>编辑照片</Link>
                </Button>
                <Button icon={<PrinterOutlined />} onClick={handlePrint}>
                  打印照片
                </Button>
                <Button icon={<ShareAltOutlined />} onClick={() => setShareModalVisible(true)}>
                  分享
                </Button>
                <Button 
                  icon={<DeleteOutlined />} 
                  danger 
                  onClick={() => setDeleteModalVisible(true)}
                >
                  删除
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card bordered={false} title="照片信息">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="名称">
                {photo.name || '未命名'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(photo.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="照片规格">
                {specInfo.name} ({specInfo.width}mm × {specInfo.height}mm)
              </Descriptions.Item>
              <Descriptions.Item label="背景颜色">
                <Tag color={bgColorInfo.hex} style={{ color: ['white', 'blue', 'red'].includes(photo.backgroundColor) ? '#fff' : '#000' }}>
                  {bgColorInfo.name}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="增强质量">
                {photo.enhanceQuality ? '是' : '否'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={photo.status === 'completed' ? 'success' : 'processing'}>
                  {photo.status === 'completed' ? '已完成' : '处理中'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Title level={5}>下一步</Title>
            <Paragraph>
              您可以下载照片，或者进行编辑以满足不同需求。如果您对结果不满意，可以重新创建。
            </Paragraph>
            
            <Space wrap style={{ marginTop: 16 }}>
              <Button type="primary" className="btn-primary">
                <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
              </Button>
              <Button>
                <Link to={ROUTES.MY_PHOTOS}>查看我的照片</Link>
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
      
      {/* 分享模态框 */}
      <ShareModal
        visible={shareModalVisible}
        photo={photo}
        onClose={() => setShareModalVisible(false)}
      />
      
      {/* 删除确认模态框 */}
      <Modal
        title="确认删除"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => setDeleteModalVisible(false)}
        okText="删除"
        cancelText="取消"
        okButtonProps={{ danger: true, loading: loading }}
      >
        <p>确定要删除这张照片吗？此操作无法撤销。</p>
      </Modal>
    </div>
  );
};

export default PhotoResultPage;