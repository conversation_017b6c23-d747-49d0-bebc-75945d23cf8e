import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { 
  Modal, 
  Typography, 
  Tabs, 
  Input, 
  Button, 
  Space, 
  Divider, 
  Row, 
  Col,
  Form,
  message,
  Tooltip
} from 'antd';
import { 
  CopyOutlined, 
  LinkOutlined, 
  MailOutlined, 
  QrcodeOutlined,
  WechatOutlined,
  FacebookOutlined,
  TwitterOutlined,
  LinkedinOutlined,
  WhatsAppOutlined
} from '@ant-design/icons';
import QRCode from 'qrcode.react';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 分享模态框组件
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.visible - 是否可见
 * @param {Object} props.photo - 照片对象
 * @param {Function} props.onClose - 关闭回调
 * @returns {React.ReactNode} 渲染的组件
 */
const ShareModal = ({ visible, photo, onClose }) => {
  const [activeTab, setActiveTab] = useState('link');
  const [emailForm] = Form.useForm();
  const [emailSending, setEmailSending] = useState(false);
  
  if (!photo) return null;
  
  // 生成分享链接
  const shareUrl = `${window.location.origin}/shared-photo/${photo.id}`;
  
  /**
   * 复制链接到剪贴板
   */
  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl)
      .then(() => {
        message.success('链接已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };
  
  /**
   * 处理邮件分享
   * 
   * @param {Object} values - 表单值
   */
  const handleEmailShare = (values) => {
    setEmailSending(true);
    
    // 模拟API调用
    setTimeout(() => {
      setEmailSending(false);
      message.success(`分享链接已发送至 ${values.email}`);
      emailForm.resetFields();
    }, 1500);
  };
  
  /**
   * 处理社交媒体分享
   * 
   * @param {string} platform - 平台名称
   */
  const handleSocialShare = (platform) => {
    let shareLink = '';
    const text = `查看我的证件照 - ${photo.name || '证件照'}`;
    
    switch (platform) {
      case 'facebook':
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(text)}`;
        break;
      case 'twitter':
        shareLink = `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(text)}`;
        break;
      case 'linkedin':
        shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'whatsapp':
        shareLink = `https://api.whatsapp.com/send?text=${encodeURIComponent(text + ' ' + shareUrl)}`;
        break;
      case 'wechat':
        // 微信分享通常是通过二维码
        message.info('请使用二维码分享到微信');
        return;
      default:
        return;
    }
    
    window.open(shareLink, '_blank');
  };
  
  return (
    <Modal
      title="分享照片"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div className="share-modal-content">
        <div className="share-photo-preview" style={{ textAlign: 'center', marginBottom: 24 }}>
          <img 
            src={photo.resultUrl || photo.thumbnailUrl} 
            alt={photo.name || '证件照'} 
            style={{ maxWidth: '100%', maxHeight: 200, objectFit: 'contain' }}
          />
          <div style={{ marginTop: 8 }}>
            <Text strong>{photo.name || '未命名照片'}</Text>
          </div>
        </div>
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <LinkOutlined />
                链接分享
              </span>
            } 
            key="link"
          >
            <div className="share-link-tab">
              <Paragraph>
                通过以下链接分享您的照片。任何拥有此链接的人都可以查看您的照片。
              </Paragraph>
              
              <div className="share-link-input" style={{ display: 'flex', marginBottom: 16 }}>
                <Input 
                  value={shareUrl} 
                  readOnly 
                  style={{ flex: 1 }}
                />
                <Tooltip title="复制链接">
                  <Button 
                    icon={<CopyOutlined />} 
                    onClick={copyToClipboard}
                    style={{ marginLeft: 8 }}
                  >
                    复制
                  </Button>
                </Tooltip>
              </div>
              
              <div className="share-qrcode" style={{ textAlign: 'center', marginTop: 24 }}>
                <Title level={5}>
                  <QrcodeOutlined /> 二维码分享
                </Title>
                <div style={{ marginTop: 16 }}>
                  <QRCode value={shareUrl} size={150} />
                </div>
                <Paragraph type="secondary" style={{ marginTop: 8 }}>
                  扫描二维码查看照片
                </Paragraph>
              </div>
            </div>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <MailOutlined />
                邮件分享
              </span>
            } 
            key="email"
          >
            <div className="share-email-tab">
              <Paragraph>
                通过邮件将照片分享给您的朋友或同事。
              </Paragraph>
              
              <Form
                form={emailForm}
                layout="vertical"
                onFinish={handleEmailShare}
              >
                <Form.Item
                  name="email"
                  label="收件人邮箱"
                  rules={[
                    { required: true, message: '请输入收件人邮箱' },
                    { type: 'email', message: '请输入有效的邮箱地址' },
                  ]}
                >
                  <Input placeholder="请输入收件人邮箱" />
                </Form.Item>
                
                <Form.Item
                  name="message"
                  label="附加消息"
                >
                  <Input.TextArea 
                    placeholder="添加一条消息（可选）" 
                    rows={3}
                  />
                </Form.Item>
                
                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={emailSending}
                    className="btn-primary"
                  >
                    发送
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <WechatOutlined />
                社交分享
              </span>
            } 
            key="social"
          >
            <div className="share-social-tab">
              <Paragraph>
                将照片分享到您的社交媒体账号。
              </Paragraph>
              
              <div className="social-buttons" style={{ marginTop: 16 }}>
                <Row gutter={[16, 16]}>
                  <Col span={6}>
                    <Button 
                      block 
                      icon={<WechatOutlined style={{ color: '#07C160' }} />}
                      onClick={() => handleSocialShare('wechat')}
                    >
                      微信
                    </Button>
                  </Col>
                  <Col span={6}>
                    <Button 
                      block 
                      icon={<FacebookOutlined style={{ color: '#1877F2' }} />}
                      onClick={() => handleSocialShare('facebook')}
                    >
                      Facebook
                    </Button>
                  </Col>
                  <Col span={6}>
                    <Button 
                      block 
                      icon={<TwitterOutlined style={{ color: '#1DA1F2' }} />}
                      onClick={() => handleSocialShare('twitter')}
                    >
                      Twitter
                    </Button>
                  </Col>
                  <Col span={6}>
                    <Button 
                      block 
                      icon={<LinkedinOutlined style={{ color: '#0A66C2' }} />}
                      onClick={() => handleSocialShare('linkedin')}
                    >
                      LinkedIn
                    </Button>
                  </Col>
                </Row>
                
                <div style={{ marginTop: 16 }}>
                  <Button 
                    block 
                    icon={<WhatsAppOutlined style={{ color: '#25D366' }} />}
                    onClick={() => handleSocialShare('whatsapp')}
                  >
                    WhatsApp
                  </Button>
                </div>
              </div>
              
              <Divider />
              
              <div className="wechat-share" style={{ textAlign: 'center' }}>
                <Title level={5}>微信扫码分享</Title>
                <div style={{ marginTop: 16 }}>
                  <QRCode value={shareUrl} size={150} />
                </div>
                <Paragraph type="secondary" style={{ marginTop: 8 }}>
                  打开微信，使用"扫一扫"即可将网页分享给好友或朋友圈
                </Paragraph>
              </div>
            </div>
          </TabPane>
        </Tabs>
        
        <div className="share-privacy-notice" style={{ marginTop: 24 }}>
          <Paragraph type="secondary" style={{ fontSize: 12 }}>
            隐私提示：任何拥有分享链接的人都可以查看此照片。如果您想限制访问，请在分享后及时删除链接。
          </Paragraph>
        </div>
      </div>
    </Modal>
  );
};

ShareModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  photo: PropTypes.object,
  onClose: PropTypes.func.isRequired,
};

export default ShareModal;