import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { message } from 'antd';
import api from '../api';
import { API_ENDPOINTS, STORAGE_KEYS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

/**
 * 初始状态
 */
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

/**
 * 登录异步action
 */
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.LOGIN, credentials);
      
      // 保存token到本地存储
      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.refreshToken);
      }
      
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 注册异步action
 */
export const register = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.REGISTER, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 获取用户信息异步action
 */
export const fetchUserProfile = createAsyncThunk(
  'auth/fetchUserProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get(API_ENDPOINTS.USER.PROFILE);
      return response.data;
    } catch (error) {
      // 如果是401错误，清除本地存储的token
      if (error.response?.status === 401) {
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      }
      
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 更新用户信息异步action
 */
export const updateUserProfile = createAsyncThunk(
  'auth/updateUserProfile',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await api.put(API_ENDPOINTS.USER.UPDATE_PROFILE, userData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 更新用户设置异步action
 */
export const updateUserSettings = createAsyncThunk(
  'auth/updateUserSettings',
  async (settings, { rejectWithValue }) => {
    try {
      const response = await api.put(API_ENDPOINTS.USER.UPDATE_SETTINGS, settings);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 更新用户头像异步action
 */
export const updateUserAvatar = createAsyncThunk(
  'auth/updateUserAvatar',
  async (formData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.USER.AVATAR, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 修改密码异步action
 */
export const changePassword = createAsyncThunk(
  'auth/changePassword',
  async (passwordData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.USER.CHANGE_PASSWORD, passwordData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 忘记密码异步action
 */
export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (email, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, { email });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 重置密码异步action
 */
export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async (resetData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, resetData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 登出异步action
 */
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // 调用登出API
      await api.post(API_ENDPOINTS.AUTH.LOGOUT);
      
      // 清除本地存储的token
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      
      return null;
    } catch (error) {
      // 即使API调用失败，也清除本地存储的token
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 认证slice
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        message.success(SUCCESS_MESSAGES.LOGIN_SUCCESS);
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 注册
      .addCase(register.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state) => {
        state.loading = false;
        message.success(SUCCESS_MESSAGES.REGISTER_SUCCESS);
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 获取用户信息
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.error = action.payload;
      })
      
      // 更新用户信息
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        message.success(SUCCESS_MESSAGES.PROFILE_UPDATE_SUCCESS);
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 更新用户设置
      .addCase(updateUserSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserSettings.fulfilled, (state, action) => {
        state.loading = false;
        state.user = {
          ...state.user,
          settings: action.payload.settings,
        };
        message.success(SUCCESS_MESSAGES.SETTINGS_UPDATE_SUCCESS);
      })
      .addCase(updateUserSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 更新用户头像
      .addCase(updateUserAvatar.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserAvatar.fulfilled, (state, action) => {
        state.loading = false;
        state.user = {
          ...state.user,
          avatar: action.payload.avatar,
        };
        message.success('头像更新成功');
      })
      .addCase(updateUserAvatar.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 修改密码
      .addCase(changePassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.loading = false;
        message.success('密码修改成功');
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 忘记密码
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.loading = false;
        message.success(SUCCESS_MESSAGES.PASSWORD_RESET_EMAIL_SENT);
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 重置密码
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state) => {
        state.loading = false;
        message.success(SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESS);
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 登出
      .addCase(logout.pending, (state) => {
        state.loading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.error = null;
      })
      .addCase(logout.rejected, (state) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.error = null;
      });
  },
});

export const { clearError, setUser } = authSlice.actions;

export default authSlice.reducer;