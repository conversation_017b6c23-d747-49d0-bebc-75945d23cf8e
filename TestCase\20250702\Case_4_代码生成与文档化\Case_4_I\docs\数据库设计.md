# AI证件照生成平台 数据库设计

## 数据库选择

本项目使用 MongoDB 作为数据库，主要基于以下考虑：

1. **灵活的数据模型**：MongoDB 的文档模型适合存储照片任务和用户信息等非结构化或半结构化数据
2. **扩展性**：可以轻松扩展以处理大量用户和照片数据
3. **性能**：对于读写操作有良好的性能表现
4. **易于开发**：与 Node.js/Express 技术栈良好集成

## 数据模型

### 用户模型 (User)

```javascript
const UserSchema = new mongoose.Schema(
  {
    username: {
      type: String,
      required: [true, '请提供用户名'],
      unique: true,
      trim: true,
      minlength: [3, '用户名至少3个字符'],
      maxlength: [20, '用户名最多20个字符']
    },
    email: {
      type: String,
      required: [true, '请提供邮箱'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        '请提供有效的邮箱地址'
      ]
    },
    password: {
      type: String,
      required: [true, '请提供密码'],
      minlength: [6, '密码至少6个字符'],
      select: false
    },
    avatar: {
      type: String,
      default: ''
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user'
    },
    remainingCredits: {
      type: Number,
      default: 100
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date
  },
  {
    timestamps: true
  }
);
```

#### 字段说明

- **username**: 用户名，唯一，3-20个字符
- **email**: 邮箱地址，唯一，用于登录和找回密码
- **password**: 密码，经过bcrypt加密存储，不返回给客户端
- **avatar**: 用户头像URL
- **role**: 用户角色，普通用户或管理员
- **remainingCredits**: 剩余积分，用于生成证件照
- **resetPasswordToken**: 重置密码的临时令牌
- **resetPasswordExpire**: 重置密码令牌的过期时间
- **timestamps**: 自动添加createdAt和updatedAt字段

### 照片任务模型 (PhotoTask)

```javascript
const PhotoTaskSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    originalPhotos: [{
      path: {
        type: String,
        required: true
      },
      filename: {
        type: String,
        required: true
      }
    }],
    generatedPhotos: [{
      path: {
        type: String
      },
      filename: {
        type: String
      },
      originalPhotoIndex: {
        type: Number
      },
      qualityScore: {
        type: Number,
        min: 0,
        max: 100
      }
    }],
    specification: {
      type: String,
      required: true,
      enum: ['one_inch', 'two_inch', 'small_two_inch', 'passport', 'visa']
    },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    errorMessage: {
      type: String
    },
    processingTime: {
      type: Number
    },
    creditsUsed: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);
```

#### 字段说明

- **user**: 关联的用户ID，外键关联到User模型
- **originalPhotos**: 用户上传的原始照片数组
  - **path**: 照片在服务器上的路径
  - **filename**: 照片文件名
- **generatedPhotos**: 生成的证件照数组
  - **path**: 生成的照片在服务器上的路径
  - **filename**: 生成的照片文件名
  - **originalPhotoIndex**: 对应的原始照片索引
  - **qualityScore**: 照片质量评分（0-100）
- **specification**: 证件照规格
  - **one_inch**: 1寸
  - **two_inch**: 2寸
  - **small_two_inch**: 小2寸
  - **passport**: 护照
  - **visa**: 签证
- **status**: 任务状态
  - **pending**: 等待处理
  - **processing**: 处理中
  - **completed**: 已完成
  - **failed**: 失败
- **errorMessage**: 错误信息（如果任务失败）
- **processingTime**: 处理时间（秒）
- **creditsUsed**: 使用的积分数量
- **timestamps**: 自动添加createdAt和updatedAt字段

## 索引设计

### User 集合索引

```javascript
// 用户名索引（唯一）
UserSchema.index({ username: 1 }, { unique: true });

// 邮箱索引（唯一）
UserSchema.index({ email: 1 }, { unique: true });
```

### PhotoTask 集合索引

```javascript
// 用户ID索引（查询用户的所有任务）
PhotoTaskSchema.index({ user: 1 });

// 状态索引（查询特定状态的任务）
PhotoTaskSchema.index({ status: 1 });

// 创建时间索引（按时间排序）
PhotoTaskSchema.index({ createdAt: -1 });
```

## 数据关系

1. **用户与照片任务**：一对多关系，一个用户可以有多个照片任务
   - 通过PhotoTask中的user字段（外键）关联到User

## 数据安全

1. **密码安全**：
   - 使用bcrypt进行密码加密存储
   - 密码字段设置select: false，默认不返回

2. **数据验证**：
   - 使用Mongoose的内置验证功能
   - 使用express-validator进行请求数据验证

3. **访问控制**：
   - 使用JWT进行身份验证
   - 基于角色的访问控制（普通用户/管理员）

## 数据备份策略

1. **定期备份**：每日进行数据库完整备份
2. **增量备份**：每小时进行增量备份
3. **备份存储**：备份数据存储在独立的存储系统中
4. **备份测试**：定期测试备份恢复功能