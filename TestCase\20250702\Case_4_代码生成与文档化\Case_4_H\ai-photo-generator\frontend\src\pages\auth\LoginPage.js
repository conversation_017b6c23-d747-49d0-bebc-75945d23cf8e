import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Form, Input, Button, Checkbox, Card, Alert, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { login, selectAuthLoading, selectAuthError } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';
import Logo from '../../components/common/Logo';

const { Title, Text } = Typography;

/**
 * 登录页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const LoginPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(true);
  
  // 获取重定向路径
  const from = location.state?.from?.pathname || ROUTES.HOME;
  
  /**
   * 处理表单提交
   * 
   * @param {Object} values - 表单值
   */
  const handleSubmit = async (values) => {
    try {
      await dispatch(login({ email: values.email, password: values.password })).unwrap();
      navigate(from, { replace: true });
    } catch (err) {
      console.error('Login failed:', err);
    }
  };
  
  return (
    <div className="login-page" style={{ maxWidth: 400, margin: '0 auto', padding: '40px 20px' }}>
      <div className="login-logo" style={{ textAlign: 'center', marginBottom: 24 }}>
        <Logo size="large" />
        <Title level={2} style={{ marginTop: 16 }}>登录</Title>
        <Text type="secondary">登录您的AI证件照生成器账号</Text>
      </div>
      
      {error && (
        <Alert
          message="登录失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}
      
      <Card bordered={false} style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)' }}>
        <Form
          form={form}
          name="login"
          initialValues={{ remember: rememberMe }}
          onFinish={handleSubmit}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入您的邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="邮箱"
              autoComplete="email"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入您的密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              autoComplete="current-password"
              disabled={loading}
            />
          </Form.Item>
          
          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Checkbox
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                disabled={loading}
              >
                记住我
              </Checkbox>
              <Link to="/forgot-password">忘记密码？</Link>
            </div>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              loading={loading}
              className="btn-primary"
            >
              登录
            </Button>
          </Form.Item>
          
          <Divider>
            <Text type="secondary">或者</Text>
          </Divider>
          
          <div style={{ textAlign: 'center' }}>
            <Text>还没有账号？</Text>{' '}
            <Link to={ROUTES.REGISTER}>立即注册</Link>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default LoginPage;