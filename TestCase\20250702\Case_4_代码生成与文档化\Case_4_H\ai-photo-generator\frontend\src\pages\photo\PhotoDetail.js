import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  Row, 
  Col, 
  Card, 
  Typography, 
  Button, 
  Spin, 
  Tag, 
  Space, 
  Divider, 
  Tooltip, 
  Modal, 
  message, 
  Skeleton,
  Dropdown,
  Menu,
  Input,
  Form
} from 'antd';
import {
  HeartOutlined,
  HeartFilled,
  DownloadOutlined,
  ShareAltOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ArrowLeftOutlined,
  ExclamationCircleOutlined,
  PictureOutlined,
  TagOutlined,
  CalendarOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { 
  fetchPhotoById, 
  toggleFavorite, 
  deletePhoto, 
  downloadPhoto,
  updatePhotoDetails
} from '../../store/photoSlice';
import { ROUTES, PHOTO_STYLES } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { confirm } = Modal;

/**
 * 样式化组件
 */
const StyledCard = styled(Card)`
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
`;

const PhotoContainer = styled.div`
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background-color: ${({ theme }) => theme.colorBgContainer};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
`;

const PhotoImage = styled.img`
  width: 100%;
  display: block;
`;

const PhotoActions = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ActionButton = styled(Button)`
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
  
  &:hover {
    color: white;
    border-color: white;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .anticon {
    font-size: 16px;
  }
`;

const DetailItem = styled.div`
  margin-bottom: 16px;
  
  .label {
    font-weight: 500;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    
    .anticon {
      margin-right: 8px;
    }
  }
`;

const BackLink = styled(Link)`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  
  .anticon {
    margin-right: 8px;
  }
`;

const ShareLinkInput = styled(Input)`
  margin-bottom: 16px;
`;

/**
 * 照片详情页面组件
 * 
 * 显示单张照片的详细信息，并提供各种操作选项
 * 
 * @returns {JSX.Element} 照片详情页面组件
 */
const PhotoDetail = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  
  // 从Redux获取状态
  const { currentPhoto, loading } = useSelector((state) => state.photo);
  
  // 本地状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [form] = Form.useForm();
  
  // 获取照片详情
  useEffect(() => {
    if (id) {
      dispatch(fetchPhotoById(id));
    }
  }, [dispatch, id]);
  
  // 当照片数据加载完成后，设置表单初始值
  useEffect(() => {
    if (currentPhoto) {
      form.setFieldsValue({
        prompt: currentPhoto.prompt,
        tags: currentPhoto.tags?.join(', ') || ''
      });
    }
  }, [currentPhoto, form]);
  
  // 如果正在加载或没有照片数据，显示加载状态
  if (loading || !currentPhoto) {
    return (
      <div style={{ padding: '40px 0', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>加载照片详情...</Text>
        </div>
      </div>
    );
  }
  
  // 处理收藏切换
  const handleToggleFavorite = () => {
    dispatch(toggleFavorite({ 
      photoId: currentPhoto.id, 
      isFavorite: !currentPhoto.isFavorite 
    }));
  };
  
  // 处理照片下载
  const handleDownload = () => {
    dispatch(downloadPhoto(currentPhoto.id));
  };
  
  // 处理照片删除
  const handleDelete = () => {
    confirm({
      title: '确定要删除这张照片吗？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后将无法恢复',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        dispatch(deletePhoto(currentPhoto.id)).unwrap()
          .then(() => {
            message.success('照片已删除');
            navigate(ROUTES.PHOTOS);
          })
          .catch((error) => {
            message.error(error?.message || '删除照片失败');
          });
      }
    });
  };
  
  // 处理分享链接复制
  const handleCopyShareLink = () => {
    const shareLink = `${window.location.origin}${ROUTES.PHOTOS}/${currentPhoto.id}`;
    navigator.clipboard.writeText(shareLink)
      .then(() => {
        message.success('分享链接已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制链接失败，请手动复制');
      });
  };
  
  // 处理编辑模式切换
  const handleEditModeToggle = () => {
    if (isEditMode) {
      // 退出编辑模式，不保存更改
      form.setFieldsValue({
        prompt: currentPhoto.prompt,
        tags: currentPhoto.tags?.join(', ') || ''
      });
    }
    setIsEditMode(!isEditMode);
  };
  
  // 处理照片详情更新
  const handleUpdateDetails = async () => {
    try {
      const values = await form.validateFields();
      const tags = values.tags
        ? values.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        : [];
      
      await dispatch(updatePhotoDetails({
        photoId: currentPhoto.id,
        prompt: values.prompt,
        tags
      })).unwrap();
      
      message.success('照片详情已更新');
      setIsEditMode(false);
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error(error?.message || '更新照片详情失败');
    }
  };
  
  // 获取照片风格标签
  const getStyleTag = () => {
    if (!currentPhoto.style) return null;
    
    const styleObj = PHOTO_STYLES.find(s => s.value === currentPhoto.style);
    return styleObj ? (
      <Tag color="blue">{styleObj.label}</Tag>
    ) : (
      <Tag color="blue">{currentPhoto.style}</Tag>
    );
  };
  
  // 渲染照片标签
  const renderTags = () => {
    if (!currentPhoto.tags || currentPhoto.tags.length === 0) {
      return <Text type="secondary">无标签</Text>;
    }
    
    return (
      <div>
        {currentPhoto.tags.map(tag => (
          <Tag key={tag} style={{ marginBottom: 8 }}>
            {tag}
          </Tag>
        ))}
      </div>
    );
  };
  
  // 渲染更多操作菜单
  const moreMenu = (
    <Menu>
      <Menu.Item 
        key="edit" 
        icon={<EditOutlined />}
        onClick={handleEditModeToggle}
      >
        {isEditMode ? '取消编辑' : '编辑详情'}
      </Menu.Item>
      <Menu.Item 
        key="share" 
        icon={<ShareAltOutlined />}
        onClick={() => setShareModalVisible(true)}
      >
        分享照片
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item 
        key="delete" 
        icon={<DeleteOutlined />} 
        danger
        onClick={handleDelete}
      >
        删除照片
      </Menu.Item>
    </Menu>
  );
  
  return (
    <div>
      <BackLink to={ROUTES.PHOTOS}>
        <ArrowLeftOutlined /> 返回照片列表
      </BackLink>
      
      <Row gutter={24}>
        <Col xs={24} lg={16}>
          {/* 照片展示 */}
          <PhotoContainer>
            <PhotoImage src={currentPhoto.url} alt={currentPhoto.prompt} />
            <PhotoActions>
              <Space>
                <ActionButton 
                  icon={currentPhoto.isFavorite ? <HeartFilled /> : <HeartOutlined />}
                  onClick={handleToggleFavorite}
                  ghost
                >
                  {currentPhoto.isFavorite ? '取消收藏' : '收藏'}
                </ActionButton>
                <ActionButton 
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                  ghost
                >
                  下载
                </ActionButton>
              </Space>
              <Dropdown overlay={moreMenu} placement="bottomRight">
                <ActionButton ghost>更多操作</ActionButton>
              </Dropdown>
            </PhotoActions>
          </PhotoContainer>
        </Col>
        
        <Col xs={24} lg={8}>
          {/* 照片详情 */}
          <StyledCard>
            <Form form={form} layout="vertical">
              {/* 照片描述 */}
              <DetailItem>
                <div className="label">
                  <InfoCircleOutlined /> 照片描述
                </div>
                {isEditMode ? (
                  <Form.Item
                    name="prompt"
                    rules={[{ required: true, message: '请输入照片描述' }]}
                  >
                    <TextArea rows={4} />
                  </Form.Item>
                ) : (
                  <Paragraph>{currentPhoto.prompt}</Paragraph>
                )}
              </DetailItem>
              
              <Divider />
              
              {/* 照片标签 */}
              <DetailItem>
                <div className="label">
                  <TagOutlined /> 标签
                </div>
                {isEditMode ? (
                  <Form.Item
                    name="tags"
                    help="多个标签请用逗号分隔"
                  >
                    <Input placeholder="标签1, 标签2, 标签3" />
                  </Form.Item>
                ) : (
                  renderTags()
                )}
              </DetailItem>
              
              <Divider />
              
              {/* 照片元数据 */}
              <DetailItem>
                <div className="label">
                  <PictureOutlined /> 风格
                </div>
                {getStyleTag() || <Text type="secondary">标准</Text>}
              </DetailItem>
              
              <DetailItem>
                <div className="label">
                  <CalendarOutlined /> 创建时间
                </div>
                <Text>
                  {new Date(currentPhoto.createdAt).toLocaleString()}
                </Text>
              </DetailItem>
              
              {/* 编辑模式下的保存按钮 */}
              {isEditMode && (
                <div style={{ marginTop: 24 }}>
                  <Space>
                    <Button 
                      type="primary" 
                      onClick={handleUpdateDetails}
                    >
                      保存更改
                    </Button>
                    <Button onClick={handleEditModeToggle}>
                      取消
                    </Button>
                  </Space>
                </div>
              )}
            </Form>
          </StyledCard>
        </Col>
      </Row>
      
      {/* 分享模态框 */}
      <Modal
        title="分享照片"
        visible={shareModalVisible}
        onCancel={() => setShareModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setShareModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        <Paragraph>
          复制以下链接分享这张照片：
        </Paragraph>
        <ShareLinkInput
          value={`${window.location.origin}${ROUTES.PHOTOS}/${currentPhoto.id}`}
          readOnly
          addonAfter={
            <Tooltip title="复制链接">
              <CopyOutlined onClick={handleCopyShareLink} style={{ cursor: 'pointer' }} />
            </Tooltip>
          }
        />
        <Paragraph type="secondary">
          注意：只有登录用户才能查看分享的照片。
        </Paragraph>
      </Modal>
    </div>
  );
};

export default PhotoDetail;