const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 保护路由，需要登录
exports.protect = async (req, res, next) => {
  let token;

  // 从请求头获取token
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // 检查token是否存在
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未授权访问，请登录'
    });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 获取用户信息
    const user = await User.findById(decoded.id);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '找不到该用户'
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: '未授权访问，请登录',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 授权角色
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: `用户角色 ${req.user.role} 无权访问此资源`
      });
    }
    next();
  };
};

// 检查积分是否足够
exports.checkCredits = (requiredCredits) => {
  return (req, res, next) => {
    if (req.user.remainingCredits < requiredCredits) {
      return res.status(402).json({
        success: false,
        message: '积分不足，请充值'
      });
    }
    next();
  };
};