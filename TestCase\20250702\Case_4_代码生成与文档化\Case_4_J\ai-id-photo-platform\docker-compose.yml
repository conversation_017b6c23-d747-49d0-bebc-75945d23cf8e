version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: ai-photo-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-ai_photo_platform}
      MYSQL_USER: ${MYSQL_USER:-ai_photo_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-userpassword}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./database/seeds/demo_data.sql:/docker-entrypoint-initdb.d/02-demo_data.sql
      - ./mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    restart: unless-stopped
    networks:
      - ai-photo-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存服务
  redis:
    image: redis:6.2-alpine
    container_name: ai-photo-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redispassword} --appendonly yes
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "${REDIS_PORT:-6379}:6379"
    restart: unless-stopped
    networks:
      - ai-photo-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 3s
      retries: 5

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      args:
        NODE_ENV: ${NODE_ENV:-production}
    container_name: ai-photo-backend
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost}
      
      # 数据库配置
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE:-ai_photo_platform}
      DB_USER: ${MYSQL_USER:-ai_photo_user}
      DB_PASSWORD: ${MYSQL_PASSWORD:-userpassword}
      
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispassword}
      REDIS_DB: 0
      
      # JWT配置
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-super-secret-refresh-key}
      JWT_REFRESH_EXPIRES_IN: ${JWT_REFRESH_EXPIRES_IN:-30d}
      
      # AI模型配置
      DEFAULT_AI_ENDPOINT: ${DEFAULT_AI_ENDPOINT:-https://api.example.com/generate}
      DEFAULT_AI_API_KEY: ${DEFAULT_AI_API_KEY:-your-ai-api-key}
      CUSTOM_AI_ENDPOINT: ${CUSTOM_AI_ENDPOINT:-https://custom-api.example.com/process}
      CUSTOM_AI_TOKEN: ${CUSTOM_AI_TOKEN:-your-custom-ai-token}
      
      # 其他配置
      USE_MOCK_AI: ${USE_MOCK_AI:-false}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10485760}
      MAX_FILES_COUNT: ${MAX_FILES_COUNT:-5}
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - backend_node_modules:/app/node_modules
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - ai-photo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-/api}
    container_name: ai-photo-frontend
    environment:
      NGINX_HOST: ${NGINX_HOST:-localhost}
      NGINX_PORT: ${NGINX_PORT:-80}
      BACKEND_HOST: backend
      BACKEND_PORT: 3000
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "${FRONTEND_PORT:-80}:80"
      - "${FRONTEND_HTTPS_PORT:-443}:443"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - ai-photo-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 10s
      retries: 3

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-photo-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ai-photo-network
    restart: unless-stopped
    profiles:
      - monitoring

  # 可选：Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: ai-photo-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    networks:
      - ai-photo-network
    restart: unless-stopped
    profiles:
      - monitoring

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_node_modules:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 网络
networks:
  ai-photo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
