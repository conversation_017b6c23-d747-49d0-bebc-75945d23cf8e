#!/bin/bash

# AI证件照生成平台停止脚本
# 用于停止各种环境下的服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AI证件照生成平台停止脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev         停止开发环境"
    echo "  prod        停止生产环境"
    echo "  docker      停止Docker环境"
    echo "  all         停止所有环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -f, --force    强制停止"
    echo "  -c, --clean    停止并清理数据"
    echo "  --remove       删除Docker容器和镜像"
    echo ""
    echo "示例:"
    echo "  $0 dev                    # 停止开发环境"
    echo "  $0 docker                 # 停止Docker环境"
    echo "  $0 docker --clean         # 停止Docker并清理数据"
    echo "  $0 all --force            # 强制停止所有服务"
}

# 停止Node.js进程
stop_node_processes() {
    log_info "停止Node.js进程..."
    
    # 查找并停止相关进程
    local pids=$(pgrep -f "ai-photo\|node.*src/app.js\|npm.*dev" || true)
    
    if [ ! -z "$pids" ]; then
        log_info "发现运行中的进程: $pids"
        
        if [ "$FORCE" = true ]; then
            log_warn "强制停止进程..."
            echo $pids | xargs kill -9 2>/dev/null || true
        else
            log_info "优雅停止进程..."
            echo $pids | xargs kill -TERM 2>/dev/null || true
            
            # 等待进程停止
            sleep 3
            
            # 检查是否还有进程运行
            local remaining=$(pgrep -f "ai-photo\|node.*src/app.js\|npm.*dev" || true)
            if [ ! -z "$remaining" ]; then
                log_warn "部分进程未停止，强制终止..."
                echo $remaining | xargs kill -9 2>/dev/null || true
            fi
        fi
    else
        log_info "没有发现运行中的Node.js进程"
    fi
}

# 停止PM2进程
stop_pm2_processes() {
    if command -v pm2 &> /dev/null; then
        log_info "停止PM2进程..."
        
        # 停止特定应用
        pm2 stop ai-photo-backend 2>/dev/null || true
        pm2 delete ai-photo-backend 2>/dev/null || true
        
        # 如果需要，停止所有PM2进程
        if [ "$FORCE" = true ]; then
            pm2 kill 2>/dev/null || true
        fi
    fi
}

# 停止开发环境
stop_development() {
    log_info "停止开发环境..."
    
    stop_node_processes
    stop_pm2_processes
    
    # 停止可能的Vite开发服务器
    local vite_pids=$(pgrep -f "vite\|webpack-dev-server" || true)
    if [ ! -z "$vite_pids" ]; then
        log_info "停止前端开发服务器..."
        echo $vite_pids | xargs kill -TERM 2>/dev/null || true
    fi
    
    log_info "开发环境已停止"
}

# 停止生产环境
stop_production() {
    log_info "停止生产环境..."
    
    stop_pm2_processes
    stop_node_processes
    
    # 停止Nginx (如果由脚本管理)
    if command -v nginx &> /dev/null; then
        if pgrep nginx > /dev/null; then
            log_info "停止Nginx..."
            sudo nginx -s quit 2>/dev/null || true
        fi
    fi
    
    log_info "生产环境已停止"
}

# 停止Docker环境
stop_docker() {
    log_info "停止Docker环境..."
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        return 1
    fi
    
    # 停止服务
    log_info "停止Docker服务..."
    docker-compose down
    
    # 如果需要清理
    if [ "$CLEAN" = true ]; then
        log_warn "清理Docker数据..."
        docker-compose down -v --remove-orphans
        
        # 清理未使用的镜像
        docker image prune -f
        
        # 清理未使用的卷
        docker volume prune -f
    fi
    
    # 如果需要删除
    if [ "$REMOVE" = true ]; then
        log_warn "删除Docker镜像和容器..."
        
        # 停止并删除容器
        docker-compose down -v --remove-orphans --rmi all
        
        # 删除相关镜像
        docker images | grep ai-photo | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true
    fi
    
    log_info "Docker环境已停止"
}

# 停止所有环境
stop_all() {
    log_info "停止所有环境..."
    
    # 停止Docker环境
    if command -v docker-compose &> /dev/null && [ -f "docker-compose.yml" ]; then
        stop_docker
    fi
    
    # 停止本地进程
    stop_development
    stop_production
    
    # 停止相关数据库服务 (如果本地安装)
    if command -v systemctl &> /dev/null; then
        log_info "检查本地数据库服务..."
        
        # 停止MySQL
        if systemctl is-active --quiet mysql; then
            log_info "停止MySQL服务..."
            sudo systemctl stop mysql || true
        fi
        
        # 停止Redis
        if systemctl is-active --quiet redis; then
            log_info "停止Redis服务..."
            sudo systemctl stop redis || true
        fi
    fi
    
    log_info "所有环境已停止"
}

# 清理临时文件
cleanup_temp_files() {
    if [ "$CLEAN" = true ]; then
        log_info "清理临时文件..."
        
        # 清理上传的临时文件
        rm -rf backend/uploads/temp/* 2>/dev/null || true
        
        # 清理日志文件
        if [ "$FORCE" = true ]; then
            rm -rf backend/logs/* 2>/dev/null || true
        fi
        
        # 清理前端构建文件
        rm -rf frontend/dist 2>/dev/null || true
        rm -rf frontend/.vite 2>/dev/null || true
        
        log_info "临时文件清理完成"
    fi
}

# 显示状态
show_status() {
    log_info "检查服务状态..."
    
    # 检查Node.js进程
    local node_pids=$(pgrep -f "ai-photo\|node.*src/app.js" || true)
    if [ ! -z "$node_pids" ]; then
        log_warn "仍有Node.js进程运行: $node_pids"
    else
        log_info "没有Node.js进程运行"
    fi
    
    # 检查PM2进程
    if command -v pm2 &> /dev/null; then
        local pm2_status=$(pm2 list | grep ai-photo || true)
        if [ ! -z "$pm2_status" ]; then
            log_warn "PM2进程状态:"
            pm2 list | grep ai-photo || true
        fi
    fi
    
    # 检查Docker容器
    if command -v docker &> /dev/null; then
        local docker_containers=$(docker ps | grep ai-photo || true)
        if [ ! -z "$docker_containers" ]; then
            log_warn "仍有Docker容器运行:"
            docker ps | grep ai-photo || true
        else
            log_info "没有Docker容器运行"
        fi
    fi
    
    # 检查端口占用
    log_info "检查端口占用..."
    for port in 3000 3001 80 443; do
        if command -v lsof &> /dev/null; then
            local port_usage=$(lsof -i :$port 2>/dev/null || true)
            if [ ! -z "$port_usage" ]; then
                log_warn "端口 $port 仍被占用"
            fi
        elif command -v netstat &> /dev/null; then
            local port_usage=$(netstat -tlnp 2>/dev/null | grep :$port || true)
            if [ ! -z "$port_usage" ]; then
                log_warn "端口 $port 仍被占用"
            fi
        fi
    done
}

# 解析命令行参数
ENVIRONMENT=""
FORCE=false
CLEAN=false
REMOVE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --remove)
            REMOVE=true
            shift
            ;;
        dev|prod|docker|all)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定环境，默认停止所有
if [ -z "$ENVIRONMENT" ]; then
    ENVIRONMENT="all"
fi

# 显示停止信息
log_info "AI证件照生成平台停止脚本"
log_info "环境: $ENVIRONMENT"
log_info "强制停止: $FORCE"
log_info "清理数据: $CLEAN"

# 确认操作
if [ "$FORCE" = true ] || [ "$CLEAN" = true ] || [ "$REMOVE" = true ]; then
    echo -n "确认执行操作? [y/N]: "
    read -r confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
fi

# 根据环境停止相应服务
case $ENVIRONMENT in
    dev)
        stop_development
        ;;
    prod)
        stop_production
        ;;
    docker)
        stop_docker
        ;;
    all)
        stop_all
        ;;
    *)
        log_error "未知环境: $ENVIRONMENT"
        show_help
        exit 1
        ;;
esac

# 清理临时文件
cleanup_temp_files

# 显示最终状态
show_status

log_info "停止脚本执行完成"
