<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI证件照生成平台</title>
    <meta name="description" content="使用AI技术快速生成标准证件照，支持多种规格和背景色">
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/styles.css">
    <!-- 预加载关键资源 -->
    <link rel="preload" href="js/main.js" as="script">
    <link rel="preload" href="images/hero-image.png" as="image">
</head>
<body>
    <!-- 页面头部 -->
    <header class="site-header">
        <div class="container">
            <div class="logo">
                <h1>AI证件照生成平台</h1>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="#" class="active">首页</a></li>
                    <li><a href="#features">功能</a></li>
                    <li><a href="#pricing">价格</a></li>
                    <li><a href="#faq">常见问题</a></li>
                </ul>
            </nav>
            <div class="user-actions">
                <button class="btn btn-outline" id="login-btn">登录</button>
                <button class="btn btn-primary" id="register-btn">注册</button>
            </div>
            <button class="mobile-menu-toggle" aria-label="菜单">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="container">
                <div class="hero-content">
                    <h2>快速生成专业证件照</h2>
                    <p>只需上传照片，AI技术自动处理，几分钟内获得符合标准的证件照</p>
                    <button class="btn btn-large btn-primary" id="start-btn">立即开始</button>
                </div>
                <div class="hero-image">
                    <img src="images/hero-image.png" alt="AI证件照生成平台示例" width="500" height="400">
                </div>
            </div>
        </section>

        <!-- 功能特点 -->
        <section class="features-section" id="features">
            <div class="container">
                <h2 class="section-title">平台特点</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="48" height="48" rx="24" fill="#EEF1FF"/>
                                <path d="M24 16V32" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M16 24H32" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h3>多种规格</h3>
                        <p>支持1寸、2寸等多种标准证件照规格，满足不同场景需求</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="48" height="48" rx="24" fill="#EEF1FF"/>
                                <circle cx="24" cy="24" r="8" stroke="#4A6BFF" stroke-width="2"/>
                                <path d="M24 12V16" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M24 32V36" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M36 24L32 24" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M16 24L12 24" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h3>智能处理</h3>
                        <p>AI自动调整光线、姿势和表情，确保符合标准要求</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="48" height="48" rx="24" fill="#EEF1FF"/>
                                <path d="M16 20L24 28L32 20" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h3>一键下载</h3>
                        <p>生成后即可下载高清证件照，支持单张下载和批量打包</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="48" height="48" rx="24" fill="#EEF1FF"/>
                                <rect x="16" y="16" width="16" height="16" rx="2" stroke="#4A6BFF" stroke-width="2"/>
                                <path d="M22 22L26 26" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M26 22L22 26" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h3>多种背景</h3>
                        <p>提供蓝色、红色、白色等标准背景色，满足不同证件要求</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用流程 -->
        <section class="workflow-section">
            <div class="container">
                <h2 class="section-title">使用流程</h2>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h3>上传照片</h3>
                        <p>上传您的个人照片，支持JPG、PNG格式</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h3>选择规格</h3>
                        <p>选择需要的证件照规格和背景颜色</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h3>AI处理</h3>
                        <p>系统自动处理照片，生成多个版本</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <h3>下载使用</h3>
                        <p>选择最满意的照片，下载使用</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 照片上传区域 -->
        <section class="upload-section" id="upload-section">
            <div class="container">
                <h2 class="section-title">上传照片</h2>
                <div class="upload-container">
                    <div class="upload-area" id="upload-area">
                        <input type="file" id="file-input" accept=".jpg,.jpeg,.png" multiple hidden>
                        <div class="upload-placeholder">
                            <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M32 16V40" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                                <path d="M20 28L32 16L44 28" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M16 48H48" stroke="#4A6BFF" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            <h3>拖拽或点击上传照片</h3>
                            <p>支持格式：JPG、PNG、JPEG<br>文件大小：最大10MB<br>数量限制：最多5张</p>
                        </div>
                    </div>
                    <div class="preview-container" id="preview-container">
                        <h3>已上传照片</h3>
                        <div class="upload-preview" id="upload-preview"></div>
                    </div>
                </div>
                <div class="upload-actions">
                    <div class="spec-selector">
                        <label for="photo-spec">证件照规格：</label>
                        <select id="photo-spec">
                            <option value="1inch">1寸证件照 (25mm × 35mm)</option>
                            <option value="2inch">2寸证件照 (35mm × 49mm)</option>
                            <option value="small2inch">小2寸证件照 (33mm × 48mm)</option>
                            <option value="large1inch">大1寸证件照 (33mm × 45mm)</option>
                            <option value="custom">自定义尺寸</option>
                        </select>
                    </div>
                    <div class="background-selector">
                        <label>背景颜色：</label>
                        <div class="color-options">
                            <label class="color-option">
                                <input type="radio" name="background" value="blue" checked>
                                <span class="color-preview blue"></span>
                                <span>蓝色</span>
                            </label>
                            <label class="color-option">
                                <input type="radio" name="background" value="red">
                                <span class="color-preview red"></span>
                                <span>红色</span>
                            </label>
                            <label class="color-option">
                                <input type="radio" name="background" value="white">
                                <span class="color-preview white"></span>
                                <span>白色</span>
                            </label>
                        </div>
                    </div>
                    <button class="btn btn-primary" id="generate-btn" disabled>开始生成</button>
                </div>
            </div>
        </section>

        <!-- 生成结果区域 -->
        <section class="results-section" id="results-section">
            <div class="container">
                <h2 class="section-title">证件照生成结果</h2>
                <div class="results-container">
                    <div class="results-header">
                        <div class="select-all">
                            <label>
                                <input type="checkbox" id="select-all-checkbox">
                                <span>全选</span>
                            </label>
                        </div>
                        <div class="results-actions">
                            <button class="btn btn-outline" id="preview-btn" disabled>预览</button>
                            <button class="btn btn-primary" id="download-btn" disabled>下载选中</button>
                        </div>
                    </div>
                    <div class="results-grid" id="results-grid"></div>
                    <div class="results-footer">
                        <button class="btn btn-outline" id="regenerate-btn">重新生成</button>
                        <button class="btn btn-outline" id="back-btn">返回上传</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 价格方案 -->
        <section class="pricing-section" id="pricing">
            <div class="container">
                <h2 class="section-title">价格方案</h2>
                <div class="pricing-grid">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3>免费版</h3>
                            <div class="price">¥0<span>/月</span></div>
                        </div>
                        <div class="pricing-features">
                            <ul>
                                <li>每日5张证件照</li>
                                <li>标准处理质量</li>
                                <li>基础背景选择</li>
                                <li>标准规格支持</li>
                                <li class="not-included">批量处理</li>
                                <li class="not-included">高级美化</li>
                            </ul>
                        </div>
                        <button class="btn btn-outline btn-block">开始使用</button>
                    </div>
                    <div class="pricing-card featured">
                        <div class="pricing-header">
                            <h3>专业版</h3>
                            <div class="price">¥29<span>/月</span></div>
                        </div>
                        <div class="pricing-features">
                            <ul>
                                <li>每日50张证件照</li>
                                <li>高清处理质量</li>
                                <li>全部背景选择</li>
                                <li>所有规格支持</li>
                                <li>批量处理(10张)</li>
                                <li>基础美化功能</li>
                            </ul>
                        </div>
                        <button class="btn btn-primary btn-block">立即购买</button>
                    </div>
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3>企业版</h3>
                            <div class="price">¥99<span>/月</span></div>
                        </div>
                        <div class="pricing-features">
                            <ul>
                                <li>无限证件照</li>
                                <li>超高清处理质量</li>
                                <li>自定义背景</li>
                                <li>自定义规格</li>
                                <li>批量处理(不限量)</li>
                                <li>高级美化功能</li>
                            </ul>
                        </div>
                        <button class="btn btn-outline btn-block">联系我们</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 常见问题 -->
        <section class="faq-section" id="faq">
            <div class="container">
                <h2 class="section-title">常见问题</h2>
                <div class="faq-container">
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>如何获得最佳证件照效果？</h3>
                            <span class="faq-toggle"></span>
                        </div>
                        <div class="faq-answer">
                            <p>为获得最佳效果，请上传正面清晰的面部照片，光线充足，表情自然，背景尽量简单。照片分辨率越高，生成的证件照质量越好。</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>支持哪些证件照规格？</h3>
                            <span class="faq-toggle"></span>
                        </div>
                        <div class="faq-answer">
                            <p>我们支持1寸(25mm×35mm)、2寸(35mm×49mm)、小2寸(33mm×48mm)、大1寸(33mm×45mm)等标准规格，以及自定义尺寸。</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>生成的证件照可以用于正式场合吗？</h3>
                            <span class="faq-toggle"></span>
                        </div>
                        <div class="faq-answer">
                            <p>是的，我们的AI生成的证件照完全符合官方标准，可用于护照、身份证、驾照等各类证件办理。</p>
                        </div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">
                            <h3>照片会保存多久？</h3>
                            <span class="faq-toggle"></span>
                        </div>
                        <div class="faq-answer">
                            <p>为保护用户隐私，上传的原始照片和生成的证件照将在24小时后自动删除。请确保及时下载您需要的照片。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>AI证件照生成平台</h3>
                    <p>使用AI技术快速生成标准证件照，支持多种规格和背景色</p>
                </div>
                <div class="footer-column">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="#">首页</a></li>
                        <li><a href="#features">功能</a></li>
                        <li><a href="#pricing">价格</a></li>
                        <li><a href="#faq">常见问题</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>联系我们</h4>
                    <ul>
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="tel:+8610000000">+86 10000000</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h4>关注我们</h4>
                    <div class="social-links">
                        <a href="#" aria-label="微信">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.5 8.5C9.5 7.67157 8.82843 7 8 7C7.17157 7 6.5 7.67157 6.5 8.5C6.5 9.32843 7.17157 10 8 10C8.82843 10 9.5 9.32843 9.5 8.5Z" fill="currentColor"/>
                                <path d="M17.5 14.5C17.5 13.6716 16.8284 13 16 13C15.1716 13 14.5 13.6716 14.5 14.5C14.5 15.3284 15.1716 16 16 16C16.8284 16 17.5 15.3284 17.5 14.5Z" fill="currentColor"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2ZM4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12Z" fill="currentColor"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="微博">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2ZM4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12Z" fill="currentColor"/>
                                <path d="M14 7H16V9H14V7Z" fill="currentColor"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 11C7.44772 11 7 11.4477 7 12C7 14.2091 8.79086 16 11 16C13.2091 16 15 14.2091 15 12C15 11.4477 14.5523 11 14 11H8Z" fill="currentColor"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 AI证件照生成平台. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 模态框 -->
    <div class="modal" id="login-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>登录</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="login-email">邮箱</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">密码</label>
                    <input type="password" id="login-password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-block">登录</button>
            </form>
            <p class="form-footer">还没有账号？<a href="#" id="show-register">立即注册</a></p>
        </div>
    </div>

    <div class="modal" id="register-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>注册</h2>
            <form id="register-form">
                <div class="form-group">
                    <label for="register-username">用户名</label>
                    <input type="text" id="register-username" required>
                </div>
                <div class="form-group">
                    <label for="register-email">邮箱</label>
                    <input type="email" id="register-email" required>
                </div>
                <div class="form-group">
                    <label for="register-password">密码</label>
                    <input type="password" id="register-password" required>
                </div>
                <div class="form-group">
                    <label for="register-confirm-password">确认密码</label>
                    <input type="password" id="register-confirm-password" required>
                </div>
                <button type="submit" class="btn btn-primary btn-block">注册</button>
            </form>
            <p class="form-footer">已有账号？<a href="#" id="show-login">立即登录</a></p>
        </div>
    </div>

    <div class="modal" id="preview-modal">
        <div class="modal-content modal-large">
            <span class="close-modal">&times;</span>
            <h2>照片预览</h2>
            <div class="preview-modal-content">
                <img id="preview-image" src="" alt="证件照预览">
                <div class="preview-info">
                    <h3>照片信息</h3>
                    <ul>
                        <li>规格：<span id="preview-spec"></span></li>
                        <li>背景色：<span id="preview-background"></span></li>
                        <li>质量评分：<span id="preview-quality"></span></li>
                    </ul>
                    <button class="btn btn-primary" id="preview-download-btn">下载此照片</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/normalize.js"></script>
    <script src="js/main.js"></script>
</body>
</html>