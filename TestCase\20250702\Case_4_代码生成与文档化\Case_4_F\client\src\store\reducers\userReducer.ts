// 用户信息类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  credits: number;
}

// 用户状态类型
export interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
};

// Action 类型
export enum UserActionTypes {
  LOGIN_REQUEST = 'LOGIN_REQUEST',
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  REGISTER_REQUEST = 'REGISTER_REQUEST',
  REGISTER_SUCCESS = 'REGISTER_SUCCESS',
  REGISTER_FAILURE = 'REGISTER_FAILURE',
  LOGOUT = 'LOGOUT',
  UPDATE_USER = 'UPDATE_USER',
  UPDATE_CREDITS = 'UPDATE_CREDITS'
}

interface LoginRequestAction {
  type: UserActionTypes.LOGIN_REQUEST;
}

interface LoginSuccessAction {
  type: UserActionTypes.LOGIN_SUCCESS;
  payload: User;
}

interface LoginFailureAction {
  type: UserActionTypes.LOGIN_FAILURE;
  payload: string;
}

interface RegisterRequestAction {
  type: UserActionTypes.REGISTER_REQUEST;
}

interface RegisterSuccessAction {
  type: UserActionTypes.REGISTER_SUCCESS;
  payload: User;
}

interface RegisterFailureAction {
  type: UserActionTypes.REGISTER_FAILURE;
  payload: string;
}

interface LogoutAction {
  type: UserActionTypes.LOGOUT;
}

interface UpdateUserAction {
  type: UserActionTypes.UPDATE_USER;
  payload: Partial<User>;
}

interface UpdateCreditsAction {
  type: UserActionTypes.UPDATE_CREDITS;
  payload: number;
}

export type UserAction =
  | LoginRequestAction
  | LoginSuccessAction
  | LoginFailureAction
  | RegisterRequestAction
  | RegisterSuccessAction
  | RegisterFailureAction
  | LogoutAction
  | UpdateUserAction
  | UpdateCreditsAction;

// Reducer
const userReducer = (state = initialState, action: UserAction): UserState => {
  switch (action.type) {
    case UserActionTypes.LOGIN_REQUEST:
    case UserActionTypes.REGISTER_REQUEST:
      return {
        ...state,
        isLoading: true,
        error: null
      };
    
    case UserActionTypes.LOGIN_SUCCESS:
    case UserActionTypes.REGISTER_SUCCESS:
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        currentUser: action.payload,
        error: null
      };
    
    case UserActionTypes.LOGIN_FAILURE:
    case UserActionTypes.REGISTER_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload
      };
    
    case UserActionTypes.LOGOUT:
      return {
        ...initialState
      };
    
    case UserActionTypes.UPDATE_USER:
      return {
        ...state,
        currentUser: state.currentUser
          ? { ...state.currentUser, ...action.payload }
          : null
      };
    
    case UserActionTypes.UPDATE_CREDITS:
      return {
        ...state,
        currentUser: state.currentUser
          ? { ...state.currentUser, credits: action.payload }
          : null
      };
    
    default:
      return state;
  }
};

export default userReducer; 