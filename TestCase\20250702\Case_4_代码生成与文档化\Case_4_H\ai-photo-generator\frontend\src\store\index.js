import { configureStore } from '@reduxjs/toolkit';
import authReducer from './authSlice';
import photoReducer from './photoSlice';
import subscriptionReducer from './subscriptionSlice';
import uiReducer from './uiSlice';

/**
 * Redux store配置
 * 
 * 配置应用的全局状态管理
 */
const store = configureStore({
  reducer: {
    auth: authReducer,
    photo: photoReducer,
    subscription: subscriptionReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略特定路径的序列化检查
        ignoredActions: ['auth/loginSuccess', 'auth/registerSuccess', 'auth/fetchUserProfileSuccess'],
        ignoredPaths: ['auth.user.createdAt', 'auth.user.updatedAt'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export default store;