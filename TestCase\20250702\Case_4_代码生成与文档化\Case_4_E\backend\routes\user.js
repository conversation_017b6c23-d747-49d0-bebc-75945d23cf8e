const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 模拟用户数据库
const users = [];
// 模拟用户历史记录
const userHistory = [];

// 中间件：验证JWT令牌
const auth = (req, res, next) => {
  // 获取token
  const token = req.header('x-auth-token');

  // 检查是否有token
  if (!token) {
    return res.status(401).json({ message: '无访问权限，请先登录' });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret_key');
    
    // 将用户ID添加到请求对象
    req.user = decoded.user;
    next();
  } catch (err) {
    res.status(401).json({ message: '无效的令牌' });
  }
};

/**
 * @route   GET api/user/profile
 * @desc    获取用户个人资料
 * @access  Private
 */
router.get('/profile', auth, (req, res) => {
  try {
    // 查找用户
    const user = users.find(user => user.id === req.user.id);
    
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 返回用户信息（不包含密码）
    res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      credits: user.credits,
      createdAt: user.createdAt,
      lastLogin: new Date() // 模拟最后登录时间
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '服务器错误' });
  }
});

/**
 * @route   PUT api/user/profile
 * @desc    更新用户个人资料
 * @access  Private
 */
router.put(
  '/profile',
  [
    auth,
    body('username').optional().not().isEmpty().withMessage('用户名不能为空'),
    body('email').optional().isEmail().withMessage('请提供有效的邮箱地址'),
    body('phone').optional().matches(/^1[3-9]\d{9}$/).withMessage('请提供有效的手机号码')
  ],
  (req, res) => {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // 查找用户
      const userIndex = users.findIndex(user => user.id === req.user.id);
      
      if (userIndex === -1) {
        return res.status(404).json({ message: '用户不存在' });
      }
      
      const { username, email, phone } = req.body;
      
      // 更新用户信息
      if (username) users[userIndex].username = username;
      if (email) users[userIndex].email = email;
      if (phone) users[userIndex].phone = phone;
      
      // 返回更新后的用户信息
      const updatedUser = users[userIndex];
      res.json({
        message: '个人资料更新成功',
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          email: updatedUser.email,
          phone: updatedUser.phone,
          credits: updatedUser.credits
        }
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: '服务器错误' });
    }
  }
);

/**
 * @route   PUT api/user/password
 * @desc    修改密码
 * @access  Private
 */
router.put(
  '/password',
  [
    auth,
    body('currentPassword').not().isEmpty().withMessage('请输入当前密码'),
    body('newPassword').isLength({ min: 6 }).withMessage('新密码至少需要6个字符')
  ],
  async (req, res) => {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { currentPassword, newPassword } = req.body;
      
      // 查找用户
      const userIndex = users.findIndex(user => user.id === req.user.id);
      
      if (userIndex === -1) {
        return res.status(404).json({ message: '用户不存在' });
      }
      
      // 验证当前密码
      const isMatch = await bcrypt.compare(currentPassword, users[userIndex].password);
      if (!isMatch) {
        return res.status(400).json({ message: '当前密码错误' });
      }
      
      // 加密新密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      // 更新密码
      users[userIndex].password = hashedPassword;
      
      res.json({ message: '密码修改成功' });
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: '服务器错误' });
    }
  }
);

/**
 * @route   GET api/user/history
 * @desc    获取用户历史记录
 * @access  Private
 */
router.get('/history', auth, (req, res) => {
  try {
    // 查找用户的历史记录
    const history = userHistory.filter(record => record.userId === req.user.id);
    
    res.json({
      history: history.map(record => ({
        id: record.id,
        taskId: record.taskId,
        spec: record.spec,
        background: record.background,
        status: record.status,
        createdAt: record.createdAt,
        completedAt: record.completedAt,
        photoCount: record.photoCount
      }))
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: '服务器错误' });
  }
});

/**
 * @route   POST api/user/credits/purchase
 * @desc    购买积分
 * @access  Private
 */
router.post(
  '/credits/purchase',
  [
    auth,
    body('amount').isInt({ min: 1 }).withMessage('请输入有效的积分数量')
  ],
  (req, res) => {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { amount } = req.body;
      
      // 查找用户
      const userIndex = users.findIndex(user => user.id === req.user.id);
      
      if (userIndex === -1) {
        return res.status(404).json({ message: '用户不存在' });
      }
      
      // 模拟支付过程
      // 实际项目中，这里应该调用支付接口
      
      // 更新用户积分
      users[userIndex].credits += parseInt(amount);
      
      res.json({
        message: '积分购买成功',
        credits: users[userIndex].credits
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: '服务器错误' });
    }
  }
);

module.exports = router;