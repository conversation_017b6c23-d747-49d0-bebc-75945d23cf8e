/**
 * 格式化日期
 * 
 * @param {string|Date} date - 日期字符串或Date对象
 * @param {string} format - 格式化模式，默认为'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * 格式化货币
 * 
 * @param {number} amount - 金额
 * @param {string} currency - 货币代码，默认为'CNY'
 * @param {string} locale - 地区，默认为'zh-CN'
 * @returns {string} 格式化后的货币字符串
 */
export const formatCurrency = (amount, currency = 'CNY', locale = 'zh-CN') => {
  if (amount === undefined || amount === null) return '';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(amount);
};

/**
 * 格式化文件大小
 * 
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * 截断文本
 * 
 * @param {string} text - 文本
 * @param {number} length - 最大长度，默认为50
 * @param {string} suffix - 后缀，默认为'...'
 * @returns {string} 截断后的文本
 */
export const truncateText = (text, length = 50, suffix = '...') => {
  if (!text) return '';
  
  if (text.length <= length) return text;
  
  return text.substring(0, length) + suffix;
};

/**
 * 获取文件扩展名
 * 
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  
  return filename.split('.').pop().toLowerCase();
};

/**
 * 检查文件类型是否为图片
 * 
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为图片
 */
export const isImageFile = (filename) => {
  if (!filename) return false;
  
  const ext = getFileExtension(filename);
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  
  return imageExtensions.includes(ext);
};

/**
 * 生成随机ID
 * 
 * @param {number} length - ID长度，默认为8
 * @returns {string} 随机ID
 */
export const generateRandomId = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

/**
 * 深度克隆对象
 * 
 * @param {Object} obj - 要克隆的对象
 * @returns {Object} 克隆后的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  
  return JSON.parse(JSON.stringify(obj));
};

/**
 * 防抖函数
 * 
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间，默认为300ms
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait = 300) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * 
 * @param {Function} func - 要执行的函数
 * @param {number} limit - 限制时间，默认为300ms
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

/**
 * 获取图片的主要颜色
 * 
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<string>} 主要颜色的十六进制值
 */
export const getImageDominantColor = (imageUrl) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.src = imageUrl;
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.width;
      canvas.height = img.height;
      
      ctx.drawImage(img, 0, 0, img.width, img.height);
      
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
      const colorCounts = {};
      let dominantColor = '';
      let maxCount = 0;
      
      // 简化版本：每10个像素采样一次
      for (let i = 0; i < imageData.length; i += 40) {
        const r = imageData[i];
        const g = imageData[i + 1];
        const b = imageData[i + 2];
        
        // 转换为十六进制
        const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
        
        if (!colorCounts[hex]) {
          colorCounts[hex] = 0;
        }
        
        colorCounts[hex]++;
        
        if (colorCounts[hex] > maxCount) {
          maxCount = colorCounts[hex];
          dominantColor = hex;
        }
      }
      
      resolve(dominantColor);
    };
    
    img.onerror = (error) => {
      reject(error);
    };
  });
};

/**
 * 将Base64转换为Blob
 * 
 * @param {string} base64 - Base64字符串
 * @returns {Blob} Blob对象
 */
export const base64ToBlob = (base64) => {
  const parts = base64.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  
  return new Blob([uInt8Array], { type: contentType });
};

/**
 * 将Blob转换为File
 * 
 * @param {Blob} blob - Blob对象
 * @param {string} fileName - 文件名
 * @returns {File} File对象
 */
export const blobToFile = (blob, fileName) => {
  return new File([blob], fileName, { type: blob.type });
};

/**
 * 检查浏览器是否支持某个特性
 * 
 * @param {string} feature - 特性名称
 * @returns {boolean} 是否支持
 */
export const isFeatureSupported = (feature) => {
  switch (feature) {
    case 'webp':
      const canvas = document.createElement('canvas');
      if (canvas.getContext && canvas.getContext('2d')) {
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      }
      return false;
    case 'webgl':
      try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
      } catch (e) {
        return false;
      }
    case 'webrtc':
      return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    case 'serviceworker':
      return 'serviceWorker' in navigator;
    default:
      return false;
  }
};

/**
 * 获取浏览器信息
 * 
 * @returns {Object} 浏览器信息
 */
export const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  let browserName = 'Unknown';
  let browserVersion = 'Unknown';
  
  if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
    browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Chrome') > -1) {
    browserName = 'Chrome';
    browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Safari') > -1) {
    browserName = 'Safari';
    browserVersion = userAgent.match(/Version\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident/') > -1) {
    browserName = 'Internet Explorer';
    browserVersion = userAgent.match(/(?:MSIE |rv:)([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Edge') > -1) {
    browserName = 'Edge';
    browserVersion = userAgent.match(/Edge\/([0-9.]+)/)[1];
  }
  
  return {
    name: browserName,
    version: browserVersion,
    userAgent,
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
  };
};