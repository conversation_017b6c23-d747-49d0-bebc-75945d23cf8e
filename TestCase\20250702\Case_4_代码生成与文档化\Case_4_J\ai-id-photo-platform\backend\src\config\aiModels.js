require('dotenv').config();

const aiModels = {
  models: [
    {
      name: 'default_model',
      endpoint: process.env.DEFAULT_AI_ENDPOINT || 'https://api.example.com/generate',
      apiKey: process.env.DEFAULT_AI_API_KEY || '',
      timeout: 30000,
      retryCount: 3,
      isDefault: true
    },
    {
      name: 'custom_model_v1',
      endpoint: process.env.CUSTOM_AI_ENDPOINT || 'https://custom-api.example.com/process',
      headers: {
        'Authorization': `Bearer ${process.env.CUSTOM_AI_TOKEN || ''}`,
        'Content-Type': 'application/json'
      },
      timeout: 45000,
      retryCount: 2,
      isDefault: false
    }
  ],
  
  // 证件照规格配置
  photoSpecs: {
    '1inch': { width: 295, height: 413, name: '1寸证件照' },
    '2inch': { width: 413, height: 579, name: '2寸证件照' },
    'small2inch': { width: 390, height: 567, name: '小2寸证件照' },
    'big1inch': { width: 390, height: 531, name: '大1寸证件照' }
  },
  
  // 背景颜色配置
  backgroundColors: {
    'blue': '#438EDB',
    'red': '#FF0000',
    'white': '#FFFFFF',
    'lightblue': '#87CEEB',
    'lightgray': '#D3D3D3'
  },
  
  // 默认生成参数
  defaultParams: {
    count: 5,
    quality: 'high',
    style: 'formal'
  }
};

// 获取默认模型
const getDefaultModel = () => {
  return aiModels.models.find(model => model.isDefault) || aiModels.models[0];
};

// 根据名称获取模型
const getModelByName = (name) => {
  return aiModels.models.find(model => model.name === name);
};

// 验证证件照规格
const validatePhotoSpec = (spec) => {
  return aiModels.photoSpecs.hasOwnProperty(spec);
};

// 验证背景颜色
const validateBackgroundColor = (color) => {
  return aiModels.backgroundColors.hasOwnProperty(color);
};

module.exports = {
  aiModels,
  getDefaultModel,
  getModelByName,
  validatePhotoSpec,
  validateBackgroundColor
};
