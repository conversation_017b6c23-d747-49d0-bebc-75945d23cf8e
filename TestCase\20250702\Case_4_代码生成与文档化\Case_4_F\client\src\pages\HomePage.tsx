import React from 'react';
import { Button, Typography, Row, Col, Card, Space, Steps } from 'antd';
import { Link } from 'react-router-dom';
import { CloudUploadOutlined, PictureOutlined, DownloadOutlined } from '@ant-design/icons';
import './HomePage.scss';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

// 使用步骤
const steps = [
  {
    title: '上传照片',
    description: '上传您的个人照片',
    icon: <CloudUploadOutlined />
  },
  {
    title: '选择规格',
    description: '选择证件照规格和背景色',
    icon: <PictureOutlined />
  },
  {
    title: '下载成品',
    description: '下载高清证件照',
    icon: <DownloadOutlined />
  }
];

// 特色介绍
const features = [
  {
    title: '智能AI处理',
    description: '采用最先进的AI技术，自动调整光线、姿势和表情，生成标准合规的证件照'
  },
  {
    title: '多种规格支持',
    description: '支持1寸、2寸等多种标准规格，满足不同场景需求'
  },
  {
    title: '多种背景色选择',
    description: '提供蓝、红、白等多种背景色选择，适应不同证件要求'
  },
  {
    title: '快速便捷',
    description: '无需专业设备，几分钟即可获得专业证件照'
  }
];

const HomePage: React.FC = () => {
  return (
    <div className="home-page">
      {/* 顶部横幅 */}
      <div className="hero-section">
        <Row justify="center" align="middle">
          <Col xs={24} sm={20} md={16} lg={12}>
            <div className="hero-content">
              <Title level={1}>AI证件照生成平台</Title>
              <Paragraph className="subtitle">
                只需上传照片，AI自动为您生成专业证件照
              </Paragraph>
              <Space size="large">
                <Link to="/upload">
                  <Button type="primary" size="large">
                    立即体验
                  </Button>
                </Link>
                <Button size="large">了解更多</Button>
              </Space>
            </div>
          </Col>
        </Row>
      </div>

      {/* 使用步骤 */}
      <div className="section">
        <Title level={2} className="section-title">使用步骤</Title>
        <Steps current={-1}>
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>
      </div>

      {/* 特色介绍 */}
      <div className="section features-section">
        <Title level={2} className="section-title">产品特色</Title>
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} md={12} lg={6} key={index}>
              <Card className="feature-card">
                <Title level={4}>{feature.title}</Title>
                <Paragraph>{feature.description}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 号召行动 */}
      <div className="section cta-section">
        <Title level={2}>立即开始制作您的证件照</Title>
        <Paragraph>
          只需几分钟，即可获得专业级证件照
        </Paragraph>
        <Link to="/upload">
          <Button type="primary" size="large">
            免费试用
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HomePage; 