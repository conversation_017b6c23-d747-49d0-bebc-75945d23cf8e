import React, { useEffect } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { publicRoutes, privateRoutes } from './index';
import { ROUTES } from '../utils/constants';
import PrivateRoute from './PrivateRoute';
import PublicRoute from './PublicRoute';
import { selectIsAuthenticated } from '../store/authSlice';
import { getCurrentUser } from '../store/authSlice';

/**
 * 应用路由配置组件
 * 配置所有路由，并应用路由守卫
 * 
 * @returns {React.ReactNode} 路由配置
 */
const AppRoutes = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // 应用加载时，如果用户已登录，获取最新的用户信息
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(getCurrentUser());
    }
  }, [dispatch, isAuthenticated]);

  // 处理路由变化
  useEffect(() => {
    // 可以在这里添加路由变化的处理逻辑
    // 例如：页面访问统计、路由权限检查等
  }, [location.pathname]);

  return (
    <Routes>
      {/* 公共路由 */}
      {publicRoutes.map((route, index) => {
        const isAuthRoute = [ROUTES.LOGIN, ROUTES.REGISTER].includes(route.path);
        
        return (
          <Route
            key={index}
            path={route.path}
            element={
              <PublicRoute restricted={isAuthRoute}>
                {route.element}
              </PublicRoute>
            }
          />
        );
      })}

      {/* 私有路由 */}
      {privateRoutes.map((route, index) => (
        <Route
          key={index}
          path={route.path}
          element={
            <PrivateRoute>
              {route.element}
            </PrivateRoute>
          }
        />
      ))}

      {/* 默认路由 - 重定向到首页 */}
      <Route path="/" element={<PrivateRoute>{privateRoutes[0].element}</PrivateRoute>} />
      
      {/* 404路由 - 未找到页面 */}
      <Route path="*" element={<PublicRoute>{publicRoutes.find(route => route.path === ROUTES.NOT_FOUND).element}</PublicRoute>} />
    </Routes>
  );
};

export default AppRoutes;