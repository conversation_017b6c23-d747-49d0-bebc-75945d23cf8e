const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 验证用户令牌
const auth = async (req, res, next) => {
  try {
    // 获取请求头中的令牌
    const authHeader = req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        message: '未提供访问令牌'
      });
    }
    
    const token = authHeader.replace('Bearer ', '');
    
    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 查找用户
    const user = await User.findOne({
      where: { id: decoded.id }
    });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    console.error('身份验证失败:', error);
    res.status(401).json({
      code: 401,
      message: '身份验证失败',
      error: error.message
    });
  }
};

// 可选的身份验证中间件
// 如果有令牌则验证，没有则继续
const optionalAuth = async (req, res, next) => {
  try {
    // 获取请求头中的令牌
    const authHeader = req.header('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有令牌，继续执行
      return next();
    }
    
    const token = authHeader.replace('Bearer ', '');
    
    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 查找用户
    const user = await User.findOne({
      where: { id: decoded.id }
    });
    
    if (user) {
      // 将用户信息添加到请求对象
      req.user = user;
      req.token = token;
    }
    
    next();
  } catch (error) {
    // 令牌无效，但不阻止请求
    console.error('可选身份验证失败:', error);
    next();
  }
};

module.exports = {
  auth,
  optionalAuth
}; 