import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Typography, Row, Col, Card, Carousel, Steps, Divider } from 'antd';
import {
  UploadOutlined,
  EditOutlined,
  CheckCircleOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ROUTES } from '../utils/constants';

const { Title, Paragraph } = Typography;
const { Step } = Steps;

const Home = () => {
  const navigate = useNavigate();

  // 示例照片类型
  const photoTypes = [
    {
      title: '证件照',
      description: '适用于身份证、护照等官方证件',
      icon: <UserOutlined />,
      color: '#1890ff',
    },
    {
      title: '签证照',
      description: '符合各国签证照片规格要求',
      icon: <EditOutlined />,
      color: '#52c41a',
    },
    {
      title: '驾照照片',
      description: '符合驾驶证照片标准',
      icon: <CheckCircleOutlined />,
      color: '#fa8c16',
    },
  ];

  // 使用步骤
  const steps = [
    {
      title: '上传照片',
      description: '上传您的照片或使用摄像头拍摄',
      icon: <UploadOutlined />,
    },
    {
      title: '智能处理',
      description: 'AI自动调整照片尺寸、背景和光线',
      icon: <EditOutlined />,
    },
    {
      title: '获取成果',
      description: '下载符合标准的证件照，可选择不同尺寸和格式',
      icon: <CheckCircleOutlined />,
    },
  ];

  // 轮播图内容
  const carouselItems = [
    {
      title: '智能证件照生成',
      description: '使用AI技术，一键生成符合标准的各类证件照',
      image: '/images/carousel-1.jpg',
    },
    {
      title: '多种规格可选',
      description: '支持各国护照、签证、身份证等多种规格照片',
      image: '/images/carousel-2.jpg',
    },
    {
      title: '专业证件照效果',
      description: '自动调整光线、背景和姿态，呈现专业效果',
      image: '/images/carousel-3.jpg',
    },
  ];

  return (
    <div className="home-page">
      {/* 轮播图部分 */}
      <Carousel autoplay className="home-carousel">
        {carouselItems.map((item, index) => (
          <div key={index} className="carousel-item">
            <div className="carousel-content">
              <Title level={2}>{item.title}</Title>
              <Paragraph>{item.description}</Paragraph>
              <Button 
                type="primary" 
                size="large" 
                onClick={() => navigate(ROUTES.UPLOAD)}
              >
                立即体验
              </Button>
            </div>
            <div 
              className="carousel-image" 
              style={{ backgroundImage: `url(${item.image})` }}
            />
          </div>
        ))}
      </Carousel>

      {/* 功能介绍部分 */}
      <div className="home-section">
        <Title level={2} className="section-title">
          智能证件照生成器
        </Title>
        <Paragraph className="section-description">
          使用先进的AI技术，自动生成符合各类标准的证件照，省去专业拍摄的麻烦和费用
        </Paragraph>
        
        <Row gutter={[24, 24]} className="feature-cards">
          {photoTypes.map((type, index) => (
            <Col xs={24} sm={8} key={index}>
              <Card 
                className="feature-card"
                hoverable
                onClick={() => navigate(ROUTES.UPLOAD, { state: { type: type.title } })}
              >
                <div className="feature-icon" style={{ backgroundColor: type.color }}>
                  {type.icon}
                </div>
                <Title level={4}>{type.title}</Title>
                <Paragraph>{type.description}</Paragraph>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 使用步骤部分 */}
      <div className="home-section steps-section">
        <Title level={2} className="section-title">
          三步完成证件照制作
        </Title>
        <Steps current={-1} className="usage-steps">
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>
        <div className="steps-action">
          <Button 
            type="primary" 
            size="large" 
            icon={<UploadOutlined />}
            onClick={() => navigate(ROUTES.UPLOAD)}
          >
            开始制作
          </Button>
        </div>
      </div>

      {/* 优势介绍部分 */}
      <div className="home-section advantages-section">
        <Title level={2} className="section-title">
          我们的优势
        </Title>
        <Row gutter={[32, 32]}>
          <Col xs={24} md={8}>
            <Card className="advantage-card">
              <Title level={4}>AI智能处理</Title>
              <Paragraph>
                采用先进的人工智能算法，自动识别人脸，调整光线、对比度和背景，生成专业效果
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="advantage-card">
              <Title level={4}>多种规格支持</Title>
              <Paragraph>
                支持各国护照、签证、身份证、驾照等多种规格照片，满足不同场景需求
              </Paragraph>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="advantage-card">
              <Title level={4}>便捷快速</Title>
              <Paragraph>
                无需专业摄影设备，在家即可完成证件照制作，节省时间和费用
              </Paragraph>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 号召性动作 */}
      <div className="home-section cta-section">
        <Title level={2}>立即开始制作您的证件照</Title>
        <Paragraph>
          只需简单几步，即可获得专业品质的证件照
        </Paragraph>
        <Button 
          type="primary" 
          size="large"
          onClick={() => navigate(ROUTES.UPLOAD)}
        >
          开始制作
        </Button>
      </div>
    </div>
  );
};

export default Home;