import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Form, 
  Input, 
  Button, 
  Checkbox, 
  Typography, 
  Divider, 
  message, 
  Space 
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  MailOutlined, 
  GoogleOutlined, 
  FacebookOutlined 
} from '@ant-design/icons';
import styled from 'styled-components';
import { register } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const { Title, Text, Paragraph } = Typography;

/**
 * 样式化组件
 */
const FormHeader = styled.div`
  text-align: center;
  margin-bottom: 24px;
`;

const SocialLoginButtons = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`;

const SocialButton = styled(Button)`
  width: 48%;
`;

const FormFooter = styled.div`
  text-align: center;
  margin-top: 24px;
`;

/**
 * 注册页面组件
 * 
 * @returns {JSX.Element} 注册页面组件
 */
const Register = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // 从Redux获取状态
  const { loading } = useSelector((state) => state.auth);
  
  // 本地状态
  const [form] = Form.useForm();
  
  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      await dispatch(register({ 
        name: values.name,
        email: values.email, 
        password: values.password,
      })).unwrap();
      
      message.success('注册成功，请登录');
      navigate(ROUTES.LOGIN);
    } catch (error) {
      message.error(error?.message || '注册失败，请稍后再试');
    }
  };
  
  // 处理社交登录
  const handleSocialLogin = (provider) => {
    message.info(`${provider}注册功能即将推出`);
  };
  
  return (
    <>
      <FormHeader>
        <Title level={2}>创建账户</Title>
        <Text type="secondary">开始使用AI照片生成器</Text>
      </FormHeader>
      
      <SocialLoginButtons>
        <SocialButton 
          icon={<GoogleOutlined />} 
          onClick={() => handleSocialLogin('Google')}
        >
          Google注册
        </SocialButton>
        <SocialButton 
          icon={<FacebookOutlined />} 
          onClick={() => handleSocialLogin('Facebook')}
        >
          Facebook注册
        </SocialButton>
      </SocialLoginButtons>
      
      <Divider plain>或使用邮箱注册</Divider>
      
      <Form
        form={form}
        name="register"
        onFinish={handleSubmit}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="name"
          rules={[
            { required: true, message: '请输入您的姓名' },
            { min: 2, message: '姓名长度不能少于2个字符' }
          ]}
        >
          <Input 
            prefix={<UserOutlined />} 
            placeholder="姓名" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="email"
          rules={[
            { required: true, message: '请输入您的邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Input 
            prefix={<MailOutlined />} 
            placeholder="邮箱" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入您的密码' },
            { min: 6, message: '密码长度不能少于6个字符' }
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />} 
            placeholder="密码" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            { required: true, message: '请确认您的密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password 
            prefix={<LockOutlined />} 
            placeholder="确认密码" 
            size="large" 
          />
        </Form.Item>
        
        <Form.Item
          name="agreement"
          valuePropName="checked"
          rules={[
            { 
              validator: (_, value) =>
                value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意服务条款和隐私政策')),
            },
          ]}
        >
          <Checkbox>
            我已阅读并同意 <Link to={ROUTES.TERMS}>服务条款</Link> 和 <Link to={ROUTES.PRIVACY}>隐私政策</Link>
          </Checkbox>
        </Form.Item>
        
        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            size="large" 
            block 
            loading={loading}
          >
            注册
          </Button>
        </Form.Item>
      </Form>
      
      <FormFooter>
        <Space>
          <Text type="secondary">已有账户？</Text>
          <Link to={ROUTES.LOGIN}>立即登录</Link>
        </Space>
      </FormFooter>
    </>
  );
};

export default Register;