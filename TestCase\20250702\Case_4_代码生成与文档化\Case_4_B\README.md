# AI证件照生成平台

## 项目简介

本项目是一个基于AI技术的证件照生成平台，用户可以通过上传照片生成符合规范的证件照。

## 技术栈

- 前端：HTML, CSS, JavaScript
- 后端：Node.js, Express

## 功能模块

1. **照片上传**：用户上传原始照片。
2. **AI处理**：后端模拟AI处理照片，生成证件照。
3. **结果展示**：前端展示生成的证件照。

## 部署指南

1. 安装依赖：`npm install express multer`
2. 启动后端：`node server.js`
3. 打开前端：浏览器访问 `index.html`

## API文档

- **POST /api/generate**：上传照片并生成证件照。
  - 请求：`FormData`，包含 `photo` 字段。
  - 响应：`{ url: "生成的证件照URL" }`