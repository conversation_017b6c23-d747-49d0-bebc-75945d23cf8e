const multer = require('multer');
const path = require('path');
const { generateUniqueFileName, ensureDirectoryExists } = require('../utils/fileUtils');

// 上传目录配置
const uploadDir = path.join(__dirname, '../../uploads');
const tempDir = path.join(uploadDir, 'temp');
const originalDir = path.join(uploadDir, 'original');
const generatedDir = path.join(uploadDir, 'generated');

// 确保上传目录存在
const initUploadDirs = async () => {
  await ensureDirectoryExists(uploadDir);
  await ensureDirectoryExists(tempDir);
  await ensureDirectoryExists(originalDir);
  await ensureDirectoryExists(generatedDir);
};

// 初始化目录
initUploadDirs().catch(console.error);

// 存储配置
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      await ensureDirectoryExists(tempDir);
      cb(null, tempDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = generateUniqueFileName(file.originalname, 'temp_');
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('文件类型不支持，仅支持 JPG、JPEG、PNG 格式'), false);
  }
};

// Multer配置
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // 最多5个文件
  }
});

// 单文件上传中间件
const uploadSingle = upload.single('file');

// 多文件上传中间件
const uploadMultiple = upload.array('files', 5);

// 上传错误处理中间件
const handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(413).json({
        code: 413,
        message: '文件大小超过限制',
        errors: ['文件大小不能超过10MB']
      });
    }
    
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(413).json({
        code: 413,
        message: '文件数量超过限制',
        errors: ['最多只能上传5个文件']
      });
    }
    
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        code: 400,
        message: '意外的文件字段',
        errors: ['请检查文件上传字段名称']
      });
    }
  }
  
  if (err.message.includes('文件类型不支持')) {
    return res.status(400).json({
      code: 400,
      message: '文件类型不支持',
      errors: [err.message]
    });
  }
  
  next(err);
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  handleUploadError,
  uploadDir,
  tempDir,
  originalDir,
  generatedDir
};
