import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Typo<PERSON>, 
  Card, 
  Button, 
  Row, 
  Col, 
  Tabs, 
  Table, 
  Tag, 
  Space, 
  Divider,
  Input,
  Tooltip
} from 'antd';
import { 
  SearchOutlined, 
  PlusOutlined, 
  InfoCircleOutlined, 
  QuestionCircleOutlined
} from '@ant-design/icons';
import { ROUTES, PHOTO_SPECS, PHOTO_SPEC_CATEGORIES } from '../../utils/constants';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 照片规格页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const PhotoSpecsPage = () => {
  const [searchText, setSearchText] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  
  /**
   * 处理搜索
   * 
   * @param {Event} e - 事件对象
   */
  const handleSearch = (e) => {
    setSearchText(e.target.value);
  };
  
  /**
   * 过滤规格列表
   * 
   * @returns {Array} 过滤后的规格列表
   */
  const getFilteredSpecs = () => {
    let filteredSpecs = Object.entries(PHOTO_SPECS).map(([key, spec]) => ({
      key,
      ...spec
    }));
    
    // 按类别过滤
    if (activeCategory !== 'all') {
      filteredSpecs = filteredSpecs.filter(spec => spec.category === activeCategory);
    }
    
    // 按搜索文本过滤
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filteredSpecs = filteredSpecs.filter(spec => 
        spec.name.toLowerCase().includes(lowerSearchText) || 
        spec.description?.toLowerCase().includes(lowerSearchText) ||
        spec.category?.toLowerCase().includes(lowerSearchText)
      );
    }
    
    return filteredSpecs;
  };
  
  // 表格列定义
  const columns = [
    {
      title: '规格名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.isPopular && <Tag color="red">热门</Tag>}
        </Space>
      ),
    },
    {
      title: '尺寸',
      key: 'size',
      render: (_, record) => (
        <Text>{record.width} × {record.height} mm</Text>
      ),
    },
    {
      title: '像素尺寸',
      key: 'pixels',
      render: (_, record) => (
        <Text>{record.pixelWidth} × {record.pixelHeight} px</Text>
      ),
    },
    {
      title: '分辨率',
      dataIndex: 'dpi',
      key: 'dpi',
      render: (dpi) => <Text>{dpi} DPI</Text>,
    },
    {
      title: '背景颜色',
      dataIndex: 'defaultBackground',
      key: 'defaultBackground',
      render: (bg) => {
        const bgColors = {
          white: { color: '#ffffff', text: '白色' },
          blue: { color: '#2f54eb', text: '蓝色' },
          red: { color: '#f5222d', text: '红色' },
          gray: { color: '#d9d9d9', text: '灰色' },
        };
        
        const bgInfo = bgColors[bg] || { color: '#ffffff', text: '白色' };
        
        return (
          <Tag 
            color={bgInfo.color} 
            style={{ 
              color: ['white'].includes(bg) ? '#000' : '#fff',
              borderColor: bg === 'white' ? '#d9d9d9' : bgInfo.color
            }}
          >
            {bgInfo.text}
          </Tag>
        );
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (category) => {
        const categoryInfo = PHOTO_SPEC_CATEGORIES[category] || { color: 'default', name: '其他' };
        return <Tag color={categoryInfo.color}>{categoryInfo.name}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<InfoCircleOutlined />}
              onClick={() => showSpecDetails(record.key)}
            />
          </Tooltip>
          <Button type="primary" size="small" className="btn-primary">
            <Link to={`${ROUTES.CREATE_PHOTO}?spec=${record.key}`}>
              使用此规格
            </Link>
          </Button>
        </Space>
      ),
    },
  ];
  
  /**
   * 显示规格详情
   * 
   * @param {string} specKey - 规格键
   */
  const showSpecDetails = (specKey) => {
    const spec = PHOTO_SPECS[specKey];
    // 这里可以实现显示详情的逻辑，如打开模态框
    console.log('Show details for:', spec);
  };
  
  // 获取过滤后的规格列表
  const filteredSpecs = getFilteredSpecs();
  
  // 获取热门规格
  const popularSpecs = Object.entries(PHOTO_SPECS)
    .filter(([_, spec]) => spec.isPopular)
    .map(([key, spec]) => ({ key, ...spec }));
  
  return (
    <div className="photo-specs-page page-container">
      <div className="page-title">
        <Title level={2}>证件照规格</Title>
        <Button type="primary" icon={<PlusOutlined />} className="btn-primary">
          <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
        </Button>
      </div>
      
      <Card bordered={false} className="intro-card" style={{ marginBottom: 24 }}>
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={16}>
            <Title level={4}>选择合适的证件照规格</Title>
            <Paragraph>
              我们支持多种证件照规格，包括身份证、护照、驾照、签证等。选择合适的规格，确保您的证件照符合要求。
            </Paragraph>
            <Paragraph>
              <Text strong>
                不确定需要什么规格？
                <Tooltip title="我们会根据您的需求推荐合适的规格">
                  <QuestionCircleOutlined style={{ marginLeft: 8 }} />
                </Tooltip>
              </Text>
            </Paragraph>
            <Button type="primary" className="btn-primary">
              <Link to="/photo-specs-guide">查看规格选择指南</Link>
            </Button>
          </Col>
          <Col xs={24} md={8}>
            <img
              src="/images/photo-specs-illustration.svg"
              alt="证件照规格"
              style={{ width: '100%', maxWidth: 250, margin: '0 auto', display: 'block' }}
            />
          </Col>
        </Row>
      </Card>
      
      <Card bordered={false}>
        <div className="toolbar" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <Input
            placeholder="搜索规格"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={handleSearch}
            style={{ width: 200, marginBottom: 16 }}
            allowClear
          />
        </div>
        
        <Tabs activeKey={activeCategory} onChange={setActiveCategory}>
          <TabPane tab="全部规格" key="all" />
          {Object.entries(PHOTO_SPEC_CATEGORIES).map(([key, category]) => (
            <TabPane tab={category.name} key={key} />
          ))}
        </Tabs>
        
        <Table
          columns={columns}
          dataSource={filteredSpecs}
          rowKey="key"
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      <Divider orientation="left">热门规格</Divider>
      
      <Row gutter={[16, 16]}>
        {popularSpecs.map(spec => (
          <Col xs={24} sm={12} md={8} lg={6} key={spec.key}>
            <Card
              hoverable
              title={spec.name}
              extra={
                <Tag color="red">热门</Tag>
              }
              actions={[
                <Tooltip title="查看详情" key="details">
                  <InfoCircleOutlined onClick={() => showSpecDetails(spec.key)} />
                </Tooltip>,
                <Link to={`${ROUTES.CREATE_PHOTO}?spec=${spec.key}`} key="use">
                  使用此规格
                </Link>,
              ]}
            >
              <div style={{ textAlign: 'center', marginBottom: 16 }}>
                <div 
                  style={{ 
                    width: 100, 
                    height: 140, 
                    background: '#f0f2f5', 
                    margin: '0 auto',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '1px solid #d9d9d9'
                  }}
                >
                  <Text type="secondary">{spec.width} × {spec.height} mm</Text>
                </div>
              </div>
              <Paragraph ellipsis={{ rows: 2 }}>
                {spec.description || `适用于${spec.name}的标准照片规格。`}
              </Paragraph>
              <div>
                <Text type="secondary">分辨率: </Text>
                <Text>{spec.dpi} DPI</Text>
              </div>
              <div>
                <Text type="secondary">像素: </Text>
                <Text>{spec.pixelWidth} × {spec.pixelHeight} px</Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
      
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        <Paragraph>
          没有找到您需要的规格？请联系我们的客服，我们将为您提供定制服务。
        </Paragraph>
        <Button type="primary" className="btn-primary">
          <Link to="/contact">联系客服</Link>
        </Button>
      </div>
    </div>
  );
};

export default PhotoSpecsPage;