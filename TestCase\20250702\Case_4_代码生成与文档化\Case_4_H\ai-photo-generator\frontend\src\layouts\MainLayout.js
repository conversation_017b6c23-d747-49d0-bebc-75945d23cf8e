import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Layout, ConfigProvider, theme as antdTheme } from 'antd';
import styled from 'styled-components';
import Header from '../components/layout/Header';
import Sidebar from '../components/layout/Sidebar';
import Footer from '../components/layout/Footer';
import { setTheme } from '../store/uiSlice';
import { THEME_MODES } from '../utils/constants';

const { Content } = Layout;

/**
 * 样式化组件
 */
const StyledLayout = styled(Layout)`
  min-height: 100vh;
`;

const StyledContent = styled(Content)`
  margin: 24px 16px;
  padding: 24px;
  background: ${({ theme }) => theme.colorBgContainer};
  border-radius: 8px;
  min-height: 280px;
`;

/**
 * 主布局组件
 * 
 * 包含应用的主要结构，如侧边栏、头部和内容区域
 * 
 * @returns {JSX.Element} 布局组件
 */
const MainLayout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从Redux获取状态
  const { currentTheme, sidebarCollapsed } = useSelector((state) => state.ui);
  const { isAuthenticated } = useSelector((state) => state.auth);
  
  // 本地状态
  const [collapsed, setCollapsed] = useState(sidebarCollapsed);
  
  // 监听Redux中的sidebarCollapsed变化
  useEffect(() => {
    setCollapsed(sidebarCollapsed);
  }, [sidebarCollapsed]);
  
  // 从localStorage获取主题设置
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme && Object.values(THEME_MODES).includes(savedTheme)) {
      dispatch(setTheme(savedTheme));
    }
  }, [dispatch]);
  
  // 如果未认证，重定向到登录页面
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: location } });
    }
  }, [isAuthenticated, navigate, location]);
  
  // 切换侧边栏折叠状态
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  
  // 根据当前主题选择antd主题算法
  const themeAlgorithm = currentTheme === THEME_MODES.DARK 
    ? antdTheme.darkAlgorithm 
    : antdTheme.defaultAlgorithm;
  
  return (
    <ConfigProvider
      theme={{
        algorithm: themeAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <StyledLayout>
        <Sidebar collapsed={collapsed} />
        <Layout>
          <Header collapsed={collapsed} toggleCollapsed={toggleCollapsed} />
          <StyledContent>
            <Outlet />
          </StyledContent>
          <Footer />
        </Layout>
      </StyledLayout>
    </ConfigProvider>
  );
};

export default MainLayout;