import api from './api'
import { saveAs } from 'file-saver'

class TaskService {
  /**
   * 创建生成任务
   * @param {Object} taskData 任务数据
   * @param {string} taskData.uploadId 上传ID
   * @param {string} taskData.spec 证件照规格
   * @param {string} taskData.background 背景颜色
   * @param {number} taskData.count 生成数量
   * @param {Object} taskData.modelConfig 模型配置
   * @param {Object} taskData.customSpec 自定义规格
   * @returns {Promise} 创建响应
   */
  async createGenerationTask(taskData) {
    return await api.post('/generate', taskData)
  }

  /**
   * 获取任务状态
   * @param {string} taskId 任务ID
   * @returns {Promise} 任务状态响应
   */
  async getTaskStatus(taskId) {
    return await api.get(`/generate/task/${taskId}`)
  }

  /**
   * 获取任务详情
   * @param {string} taskId 任务ID
   * @returns {Promise} 任务详情响应
   */
  async getTaskDetail(taskId) {
    return await api.get(`/task/${taskId}`)
  }

  /**
   * 获取用户任务列表
   * @param {Object} params 查询参数
   * @param {number} params.page 页码
   * @param {number} params.limit 每页数量
   * @param {string} params.status 任务状态
   * @returns {Promise} 任务列表响应
   */
  async getUserTasks(params = {}) {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page)
    if (params.limit) queryParams.append('limit', params.limit)
    if (params.status) queryParams.append('status', params.status)
    
    const queryString = queryParams.toString()
    const url = queryString ? `/generate/user/tasks?${queryString}` : '/generate/user/tasks'
    
    return await api.get(url)
  }

  /**
   * 删除任务
   * @param {string} taskId 任务ID
   * @returns {Promise} 删除响应
   */
  async deleteTask(taskId) {
    return await api.delete(`/task/${taskId}`)
  }

  /**
   * 下载单张照片
   * @param {string} photoId 照片ID
   * @param {string} filename 文件名
   * @returns {Promise} 下载响应
   */
  async downloadPhoto(photoId, filename = null) {
    try {
      const response = await api.get(`/task/photo/${photoId}/download`, {
        responseType: 'blob'
      })
      
      // 从响应头获取文件名，如果没有则使用默认名称
      const contentDisposition = response.headers['content-disposition']
      let downloadFilename = filename || `id_photo_${photoId}.jpg`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          downloadFilename = filenameMatch[1]
        }
      }
      
      // 使用file-saver保存文件
      saveAs(response.data, downloadFilename)
      
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 批量下载任务的所有照片
   * @param {string} taskId 任务ID
   * @param {string} filename 文件名
   * @returns {Promise} 下载响应
   */
  async downloadTaskPhotos(taskId, filename = null) {
    try {
      const response = await api.get(`/task/${taskId}/download`, {
        responseType: 'blob'
      })
      
      // 从响应头获取文件名
      const contentDisposition = response.headers['content-disposition']
      let downloadFilename = filename || `id_photos_${taskId}.zip`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          downloadFilename = filenameMatch[1]
        }
      }
      
      // 使用file-saver保存文件
      saveAs(response.data, downloadFilename)
      
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取任务统计信息
   * @returns {Promise} 统计信息响应
   */
  async getTaskStats() {
    return await api.get('/task/user/stats')
  }

  /**
   * 轮询任务状态
   * @param {string} taskId 任务ID
   * @param {Function} onUpdate 状态更新回调
   * @param {number} interval 轮询间隔（毫秒）
   * @param {number} maxAttempts 最大尝试次数
   * @returns {Promise} 轮询结果
   */
  async pollTaskStatus(taskId, onUpdate, interval = 2000, maxAttempts = 150) {
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          
          const response = await this.getTaskStatus(taskId)
          const taskData = response.data
          
          // 调用更新回调
          if (onUpdate) {
            onUpdate(taskData)
          }
          
          // 检查任务是否完成
          if (taskData.status === 'completed') {
            resolve(taskData)
            return
          }
          
          // 检查任务是否失败
          if (taskData.status === 'failed') {
            reject(new Error(taskData.errorMessage || '任务执行失败'))
            return
          }
          
          // 检查是否超过最大尝试次数
          if (attempts >= maxAttempts) {
            reject(new Error('任务执行超时'))
            return
          }
          
          // 继续轮询
          setTimeout(poll, interval)
        } catch (error) {
          reject(error)
        }
      }
      
      // 开始轮询
      poll()
    })
  }

  /**
   * 格式化任务状态
   * @param {string} status 状态值
   * @returns {Object} 格式化后的状态信息
   */
  formatTaskStatus(status) {
    const statusMap = {
      pending: { text: '等待中', color: 'default' },
      processing: { text: '处理中', color: 'processing' },
      completed: { text: '已完成', color: 'success' },
      failed: { text: '失败', color: 'error' }
    }
    
    return statusMap[status] || { text: '未知', color: 'default' }
  }

  /**
   * 格式化证件照规格
   * @param {string} spec 规格值
   * @returns {string} 格式化后的规格名称
   */
  formatPhotoSpec(spec) {
    const specMap = {
      '1inch': '1寸证件照 (25mm × 35mm)',
      '2inch': '2寸证件照 (35mm × 49mm)',
      'small2inch': '小2寸证件照 (33mm × 48mm)',
      'big1inch': '大1寸证件照 (33mm × 45mm)',
      'custom': '自定义尺寸'
    }
    
    return specMap[spec] || spec
  }

  /**
   * 格式化背景颜色
   * @param {string} background 背景颜色值
   * @returns {string} 格式化后的颜色名称
   */
  formatBackground(background) {
    const backgroundMap = {
      blue: '蓝色',
      red: '红色',
      white: '白色',
      lightblue: '浅蓝色',
      lightgray: '浅灰色'
    }
    
    return backgroundMap[background] || background
  }
}

export default new TaskService()
