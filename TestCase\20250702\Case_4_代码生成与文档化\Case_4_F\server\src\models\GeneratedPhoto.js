const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const GenerationTask = require('./GenerationTask');

const GeneratedPhoto = sequelize.define('GeneratedPhoto', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  taskId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    references: {
      model: GenerationTask,
      key: 'id'
    }
  },
  filePath: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  fileUrl: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  qualityScore: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: false,
    defaultValue: 0.0
  }
}, {
  timestamps: true,
  tableName: 'generated_photos',
  indexes: [
    {
      fields: ['taskId']
    }
  ]
});

// 关联关系
GeneratedPhoto.belongsTo(GenerationTask, { foreignKey: 'taskId', as: 'task' });
GenerationTask.hasMany(GeneratedPhoto, { foreignKey: 'taskId', as: 'photos' });

module.exports = GeneratedPhoto; 