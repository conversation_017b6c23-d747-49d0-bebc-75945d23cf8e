const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken } = require('../middleware/auth');
const { validate, userRegistrationSchema, userLoginSchema } = require('../utils/validation');
const Joi = require('joi');

// 用户信息更新验证schema
const updateProfileSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(50).optional(),
  avatar: Joi.string().uri().optional().allow('')
});

// 修改密码验证schema
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).max(128).required()
});

/**
 * @route POST /api/user/register
 * @desc 用户注册
 * @access Public
 */
router.post('/register',
  validate(userRegistrationSchema),
  userController.register
);

/**
 * @route POST /api/user/login
 * @desc 用户登录
 * @access Public
 */
router.post('/login',
  validate(userLoginSchema),
  userController.login
);

/**
 * @route GET /api/user/profile
 * @desc 获取用户信息
 * @access Private
 */
router.get('/profile',
  authenticateToken,
  userController.getProfile
);

/**
 * @route PUT /api/user/profile
 * @desc 更新用户信息
 * @access Private
 */
router.put('/profile',
  authenticateToken,
  validate(updateProfileSchema),
  userController.updateProfile
);

/**
 * @route PUT /api/user/password
 * @desc 修改密码
 * @access Private
 */
router.put('/password',
  authenticateToken,
  validate(changePasswordSchema),
  userController.changePassword
);

/**
 * @route GET /api/user/stats
 * @desc 获取用户使用统计
 * @access Private
 */
router.get('/stats',
  authenticateToken,
  userController.getUserStats
);

/**
 * @route POST /api/user/logout
 * @desc 用户注销
 * @access Private
 */
router.post('/logout',
  authenticateToken,
  userController.logout
);

module.exports = router;
