import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from 'antd';
import './App.scss';

// 页面组件
import HomePage from './pages/HomePage';
import UploadPage from './pages/UploadPage';
import ResultsPage from './pages/ResultsPage';
import UserPage from './pages/UserPage';
import NotFoundPage from './pages/NotFoundPage';

// 布局组件
import AppHeader from './components/layout/AppHeader';
import AppFooter from './components/layout/AppFooter';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <Layout className="app-container">
      <AppHeader />
      <Content className="app-content">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/upload" element={<UploadPage />} />
          <Route path="/results/:taskId" element={<ResultsPage />} />
          <Route path="/user" element={<UserPage />} />
          <Route path="/404" element={<NotFoundPage />} />
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </Content>
      <AppFooter />
    </Layout>
  );
};

export default App; 