const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/original');
    
    // 确保上传目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 只接受jpg、jpeg和png格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型。只允许JPG、JPEG和PNG格式。'), false);
  }
};

// 配置multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

/**
 * @route   POST api/upload
 * @desc    上传照片
 * @access  Private
 */
router.post('/', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: '没有上传文件' });
    }

    // 生成上传ID
    const uploadId = uuidv4();
    
    // 构建文件URL
    const fileUrl = `${req.protocol}://${req.get('host')}/uploads/original/${req.file.filename}`;
    
    // 返回上传信息
    res.status(200).json({
      code: 200,
      message: '上传成功',
      data: {
        upload_id: uploadId,
        file_url: fileUrl,
        original_filename: req.file.originalname,
        file_size: req.file.size,
        file_type: req.file.mimetype
      }
    });
  } catch (err) {
    console.error('文件上传错误:', err);
    res.status(500).json({ message: '文件上传失败', error: err.message });
  }
});

/**
 * @route   POST api/upload/batch
 * @desc    批量上传照片
 * @access  Private
 */
router.post('/batch', upload.array('files', 5), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: '没有上传文件' });
    }

    // 生成上传ID
    const uploadId = uuidv4();
    
    // 处理上传的文件
    const uploadedFiles = req.files.map(file => {
      const fileUrl = `${req.protocol}://${req.get('host')}/uploads/original/${file.filename}`;
      
      return {
        file_url: fileUrl,
        original_filename: file.originalname,
        file_size: file.size,
        file_type: file.mimetype
      };
    });
    
    // 返回上传信息
    res.status(200).json({
      code: 200,
      message: '批量上传成功',
      data: {
        upload_id: uploadId,
        files: uploadedFiles,
        total_count: uploadedFiles.length
      }
    });
  } catch (err) {
    console.error('批量文件上传错误:', err);
    res.status(500).json({ message: '批量文件上传失败', error: err.message });
  }
});

module.exports = router;