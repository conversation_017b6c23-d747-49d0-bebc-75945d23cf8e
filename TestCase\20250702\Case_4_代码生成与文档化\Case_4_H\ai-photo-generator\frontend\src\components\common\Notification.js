import React, { useEffect } from 'react';
import { Alert } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { removeNotification } from '../../store/uiSlice';

const Notification = () => {
  const dispatch = useDispatch();
  const { notifications } = useSelector((state) => state.ui);

  useEffect(() => {
    // 自动关闭通知
    if (notifications.length > 0) {
      const timer = setTimeout(() => {
        dispatch(removeNotification(notifications[0].id));
      }, 5000); // 5秒后自动关闭

      return () => clearTimeout(timer);
    }
  }, [notifications, dispatch]);

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="notifications-container">
      {notifications.map((notification) => (
        <Alert
          key={notification.id}
          message={notification.title}
          description={notification.message}
          type={notification.type || 'info'}
          showIcon
          closable
          onClose={() => dispatch(removeNotification(notification.id))}
          className="notification-item"
        />
      ))}
    </div>
  );
};

export default Notification;