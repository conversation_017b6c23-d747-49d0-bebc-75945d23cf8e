import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Row, Col, Card, Typography, Space, Divider } from 'antd'
import { 
  CameraOutlined, 
  RocketOutlined, 
  SafetyOutlined, 
  ThunderboltOutlined,
  CheckCircleOutlined,
  StarOutlined
} from '@ant-design/icons'
import './index.scss'

const { Title, Paragraph, Text } = Typography

const HomePage = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <ThunderboltOutlined />,
      title: 'AI智能处理',
      description: '采用先进的AI技术，自动识别人脸，智能调整比例和光线'
    },
    {
      icon: <RocketOutlined />,
      title: '快速生成',
      description: '上传照片后60秒内即可生成多张高质量证件照'
    },
    {
      icon: <SafetyOutlined />,
      title: '隐私安全',
      description: '照片加密存储，定期自动清理，保护您的隐私安全'
    },
    {
      icon: <CheckCircleOutlined />,
      title: '标准规格',
      description: '支持1寸、2寸等多种标准规格，满足各种证件需求'
    }
  ]

  const steps = [
    {
      step: '01',
      title: '上传照片',
      description: '选择一张清晰的个人照片上传'
    },
    {
      step: '02',
      title: '选择规格',
      description: '选择需要的证件照规格和背景颜色'
    },
    {
      step: '03',
      title: 'AI处理',
      description: 'AI自动处理，生成标准证件照'
    },
    {
      step: '04',
      title: '下载使用',
      description: '预览选择满意的照片并下载'
    }
  ]

  const testimonials = [
    {
      name: '张先生',
      role: '公司职员',
      content: '非常方便，在家就能制作证件照，质量很好，省时省力！',
      rating: 5
    },
    {
      name: '李女士',
      role: '学生',
      content: 'AI处理效果很棒，生成的证件照比照相馆拍的还要好看。',
      rating: 5
    },
    {
      name: '王先生',
      role: '自由职业者',
      content: '价格实惠，操作简单，几分钟就搞定了，强烈推荐！',
      rating: 5
    }
  ]

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <Row justify="center" align="middle" style={{ minHeight: '60vh' }}>
            <Col xs={24} sm={20} md={16} lg={12} className="text-center">
              <Title level={1} className="hero-title">
                AI证件照生成平台
              </Title>
              <Paragraph className="hero-subtitle">
                利用先进的AI技术，快速生成高质量标准证件照
                <br />
                简单上传，智能处理，一键下载
              </Paragraph>
              <Space size="large" className="hero-buttons">
                <Button 
                  type="primary" 
                  size="large" 
                  icon={<CameraOutlined />}
                  onClick={() => navigate('/upload')}
                >
                  立即开始
                </Button>
                <Button 
                  size="large"
                  onClick={() => navigate('/login')}
                >
                  登录账户
                </Button>
              </Space>
            </Col>
          </Row>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container">
          <Title level={2} className="section-title text-center">
            为什么选择我们
          </Title>
          <Row gutter={[32, 32]} justify="center">
            {features.map((feature, index) => (
              <Col xs={24} sm={12} md={6} key={index}>
                <Card className="feature-card" bordered={false}>
                  <div className="feature-icon">
                    {feature.icon}
                  </div>
                  <Title level={4}>{feature.title}</Title>
                  <Paragraph>{feature.description}</Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* How it works Section */}
      <section className="steps-section">
        <div className="container">
          <Title level={2} className="section-title text-center">
            使用流程
          </Title>
          <Row gutter={[32, 32]} justify="center">
            {steps.map((step, index) => (
              <Col xs={24} sm={12} md={6} key={index}>
                <div className="step-item">
                  <div className="step-number">{step.step}</div>
                  <Title level={4}>{step.title}</Title>
                  <Paragraph>{step.description}</Paragraph>
                </div>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials-section">
        <div className="container">
          <Title level={2} className="section-title text-center">
            用户评价
          </Title>
          <Row gutter={[32, 32]} justify="center">
            {testimonials.map((testimonial, index) => (
              <Col xs={24} sm={12} md={8} key={index}>
                <Card className="testimonial-card">
                  <div className="testimonial-rating">
                    {Array.from({ length: testimonial.rating }, (_, i) => (
                      <StarOutlined key={i} style={{ color: '#faad14' }} />
                    ))}
                  </div>
                  <Paragraph className="testimonial-content">
                    "{testimonial.content}"
                  </Paragraph>
                  <div className="testimonial-author">
                    <Text strong>{testimonial.name}</Text>
                    <br />
                    <Text type="secondary">{testimonial.role}</Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <Row justify="center">
            <Col xs={24} md={16} lg={12} className="text-center">
              <Title level={2}>准备好制作您的证件照了吗？</Title>
              <Paragraph>
                只需几分钟，即可获得专业品质的证件照
              </Paragraph>
              <Button 
                type="primary" 
                size="large" 
                icon={<CameraOutlined />}
                onClick={() => navigate('/upload')}
              >
                立即开始制作
              </Button>
            </Col>
          </Row>
        </div>
      </section>
    </div>
  )
}

export default HomePage
