.result-page {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    
    .loading-text {
      margin-top: 16px;
      font-size: 16px;
    }
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    margin-top: 8px;
  }
}

// 响应式样式
@media (max-width: 768px) {
  .result-page {
    .loading-container {
      padding: 40px 0;
    }
  }
}