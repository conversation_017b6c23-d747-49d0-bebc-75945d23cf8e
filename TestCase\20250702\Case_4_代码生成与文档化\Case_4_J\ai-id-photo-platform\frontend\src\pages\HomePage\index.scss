.home-page {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .text-center {
    text-align: center;
  }

  .section-title {
    margin-bottom: 48px !important;
    color: #262626;
  }

  // Hero Section
  .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      z-index: 1;
    }

    .hero-content {
      position: relative;
      z-index: 2;
      padding: 80px 24px;
    }

    .hero-title {
      color: white !important;
      font-size: 3.5rem !important;
      font-weight: 700 !important;
      margin-bottom: 24px !important;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

      @media (max-width: 768px) {
        font-size: 2.5rem !important;
      }
    }

    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 40px !important;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.6;
    }

    .hero-buttons {
      .ant-btn {
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
        border-radius: 24px;
      }

      .ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }
      }

      .ant-btn:not(.ant-btn-primary) {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
          color: white;
        }
      }
    }
  }

  // Features Section
  .features-section {
    padding: 80px 0;
    background: #fafafa;

    .feature-card {
      text-align: center;
      height: 100%;
      transition: all 0.3s ease;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      .feature-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 24px;
      }

      .ant-card-body {
        padding: 32px 24px;
      }

      h4 {
        margin-bottom: 16px !important;
        color: #262626;
      }

      p {
        color: #595959;
        margin-bottom: 0;
      }
    }
  }

  // Steps Section
  .steps-section {
    padding: 80px 0;
    background: white;

    .step-item {
      text-align: center;
      position: relative;

      .step-number {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        color: white;
        font-size: 24px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 24px;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      }

      h4 {
        margin-bottom: 16px !important;
        color: #262626;
      }

      p {
        color: #595959;
        margin-bottom: 0;
      }
    }
  }

  // Testimonials Section
  .testimonials-section {
    padding: 80px 0;
    background: #fafafa;

    .testimonial-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      .ant-card-body {
        padding: 32px 24px;
      }

      .testimonial-rating {
        margin-bottom: 16px;
        font-size: 16px;
      }

      .testimonial-content {
        font-style: italic;
        margin-bottom: 24px !important;
        color: #595959;
        line-height: 1.6;
      }

      .testimonial-author {
        text-align: center;
      }
    }
  }

  // CTA Section
  .cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;

    h2 {
      color: white !important;
      margin-bottom: 16px !important;
    }

    p {
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.1rem;
      margin-bottom: 32px !important;
    }

    .ant-btn {
      height: 48px;
      padding: 0 32px;
      font-size: 16px;
      border-radius: 24px;
      background: white;
      color: #1890ff;
      border-color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        color: #1890ff;
        border-color: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home-page {
    .hero-section {
      .hero-content {
        padding: 60px 16px;
      }
    }

    .features-section,
    .steps-section,
    .testimonials-section,
    .cta-section {
      padding: 60px 0;
    }

    .container {
      padding: 0 16px;
    }
  }
}
