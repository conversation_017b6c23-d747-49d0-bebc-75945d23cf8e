import React from 'react';
import { Layout, Menu } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  HomeOutlined,
  UploadOutlined,
  UserOutlined,
  PictureOutlined,
  HistoryOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { ROUTES } from '../utils/constants';

const { Sider } = Layout;

const Sidebar = () => {
  const location = useLocation();
  const { sidebarCollapsed } = useSelector((state) => state.ui);
  const { isAuthenticated } = useSelector((state) => state.auth);

  // 如果用户未登录，不显示侧边栏
  if (!isAuthenticated) {
    return null;
  }

  return (
    <Sider
      collapsible
      collapsed={sidebarCollapsed}
      trigger={null}
      className="app-sidebar"
      width={200}
    >
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        defaultOpenKeys={['photos']}
      >
        <Menu.Item key={ROUTES.HOME} icon={<HomeOutlined />}>
          <Link to={ROUTES.HOME}>首页</Link>
        </Menu.Item>
        <Menu.Item key={ROUTES.UPLOAD} icon={<UploadOutlined />}>
          <Link to={ROUTES.UPLOAD}>上传照片</Link>
        </Menu.Item>
        
        <Menu.SubMenu key="photos" icon={<PictureOutlined />} title="我的照片">
          <Menu.Item key={`${ROUTES.USER}/photos`} icon={<PictureOutlined />}>
            <Link to={`${ROUTES.USER}/photos`}>所有照片</Link>
          </Menu.Item>
          <Menu.Item key={`${ROUTES.USER}/favorites`} icon={<PictureOutlined />}>
            <Link to={`${ROUTES.USER}/favorites`}>收藏照片</Link>
          </Menu.Item>
        </Menu.SubMenu>
        
        <Menu.Item key={`${ROUTES.USER}/history`} icon={<HistoryOutlined />}>
          <Link to={`${ROUTES.USER}/history`}>历史记录</Link>
        </Menu.Item>
        
        <Menu.Item key={ROUTES.USER} icon={<UserOutlined />}>
          <Link to={ROUTES.USER}>个人中心</Link>
        </Menu.Item>
        
        <Menu.Item key={`${ROUTES.USER}/settings`} icon={<SettingOutlined />}>
          <Link to={`${ROUTES.USER}/settings`}>账号设置</Link>
        </Menu.Item>
      </Menu>
    </Sider>
  );
};

export default Sidebar;