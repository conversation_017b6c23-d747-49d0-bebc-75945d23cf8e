import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { Typography, Alert, Spin } from 'antd';
import PhotoPreview from '../components/PhotoPreview';
import { checkTaskStatus, resetGeneration } from '../services/generationSlice';
import './ResultPage.scss';

const { Title, Paragraph } = Typography;

const ResultPage = () => {
  const { taskId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { taskStatus, generatedPhotos, loading, error } = useSelector((state) => state.generation);
  
  const [pollingInterval, setPollingInterval] = useState(null);
  
  // 初始化和清理
  useEffect(() => {
    // 首次加载时检查任务状态
    checkStatus();
    
    // 如果任务状态是处理中，则开始轮询
    if (taskStatus === 'processing' || !taskStatus) {
      const interval = setInterval(checkStatus, 3000); // 每3秒检查一次
      setPollingInterval(interval);
    }
    
    // 组件卸载时清理
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [taskId]);
  
  // 当任务状态变化时处理
  useEffect(() => {
    if (taskStatus === 'completed' && pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  }, [taskStatus]);
  
  // 检查任务状态
  const checkStatus = async () => {
    if (!taskId) return;
    
    try {
      await dispatch(checkTaskStatus(taskId)).unwrap();
    } catch (error) {
      console.error('检查任务状态失败:', error);
    }
  };
  
  // 处理重新生成
  const handleRegenerate = () => {
    dispatch(resetGeneration());
    navigate('/upload');
  };
  
  // 返回上传页面
  const handleBack = () => {
    dispatch(resetGeneration());
    navigate('/upload');
  };
  
  // 渲染加载状态
  const renderLoading = () => (
    <div className="loading-container">
      <Spin size="large" />
      <Paragraph className="loading-text">正在获取生成结果，请稍候...</Paragraph>
    </div>
  );
  
  // 渲染错误状态
  const renderError = () => (
    <Alert
      message="获取结果失败"
      description={error || '无法获取证件照生成结果，请重试或联系客服'}
      type="error"
      showIcon
      action={
        <div className="error-actions">
          <a onClick={checkStatus}>重试</a>
          <a onClick={handleBack}>返回上传</a>
        </div>
      }
    />
  );
  
  return (
    <div className="result-page">
      <div className="container">
        <Title level={2} className="page-title text-center">证件照生成结果</Title>
        
        {error && renderError()}
        
        {loading && !generatedPhotos.length ? (
          renderLoading()
        ) : (
          <PhotoPreview 
            onRegenerate={handleRegenerate} 
            onBack={handleBack} 
          />
        )}
      </div>
    </div>
  );
};

export default ResultPage;