import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  Typography, 
  Card, 
  Button, 
  Row, 
  Col, 
  Spin, 
  Alert, 
  Tabs, 
  Form, 
  Select, 
  Radio, 
  Slider,
  Switch,
  Divider,
  message,
  Space
} from 'antd';
import { 
  SaveOutlined, 
  UndoOutlined, 
  DownloadOutlined, 
  ArrowLeftOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { 
  fetchPhotoById, 
  updatePhoto, 
  downloadPhoto,
  selectCurrentPhoto, 
  selectPhotoLoading, 
  selectPhotoError 
} from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS, BACKGROUND_COLORS } from '../../utils/constants';
import PhotoPreview from '../../components/photo/PhotoPreview';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 照片编辑页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const EditPhotoPage = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const photo = useSelector(selectCurrentPhoto);
  const loading = useSelector(selectPhotoLoading);
  const error = useSelector(selectPhotoError);
  
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [previewSettings, setPreviewSettings] = useState({});
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  
  // 获取照片详情
  useEffect(() => {
    if (id) {
      dispatch(fetchPhotoById(id));
    }
  }, [dispatch, id]);
  
  // 初始化表单值
  useEffect(() => {
    if (photo) {
      form.setFieldsValue({
        name: photo.name || '',
        spec: photo.spec,
        backgroundColor: photo.backgroundColor,
        enhanceQuality: photo.enhanceQuality,
        brightness: photo.adjustments?.brightness || 0,
        contrast: photo.adjustments?.contrast || 0,
        saturation: photo.adjustments?.saturation || 0,
        removeGlasses: photo.adjustments?.removeGlasses || false,
        removeSmile: photo.adjustments?.removeSmile || false,
        cropAdjustment: photo.adjustments?.cropAdjustment || 0,
      });
      
      setPreviewSettings({
        spec: photo.spec,
        backgroundColor: photo.backgroundColor,
        ...photo.adjustments
      });
    }
  }, [photo, form]);
  
  /**
   * 处理表单值变化
   * 
   * @param {Object} changedValues - 变化的值
   * @param {Object} allValues - 所有值
   */
  const handleValuesChange = (changedValues, allValues) => {
    setPreviewSettings({
      ...previewSettings,
      ...changedValues
    });
  };
  
  /**
   * 处理保存编辑
   * 
   * @param {Object} values - 表单值
   */
  const handleSave = async (values) => {
    if (!photo) return;
    
    setSaveLoading(true);
    try {
      const { brightness, contrast, saturation, removeGlasses, removeSmile, cropAdjustment, ...basicInfo } = values;
      
      const updateData = {
        ...basicInfo,
        adjustments: {
          brightness,
          contrast,
          saturation,
          removeGlasses,
          removeSmile,
          cropAdjustment
        }
      };
      
      await dispatch(updatePhoto({ id: photo.id, data: updateData })).unwrap();
      message.success('照片更新成功');
    } catch (err) {
      message.error('更新失败: ' + err.message);
    } finally {
      setSaveLoading(false);
    }
  };
  
  /**
   * 处理下载照片
   */
  const handleDownload = async () => {
    if (!photo) return;
    
    setDownloadLoading(true);
    try {
      await dispatch(downloadPhoto(photo.id)).unwrap();
      message.success('照片下载成功');
    } catch (err) {
      message.error('下载失败: ' + err.message);
    } finally {
      setDownloadLoading(false);
    }
  };
  
  /**
   * 处理重置编辑
   */
  const handleReset = () => {
    if (!photo) return;
    
    form.setFieldsValue({
      name: photo.name || '',
      spec: photo.spec,
      backgroundColor: photo.backgroundColor,
      enhanceQuality: photo.enhanceQuality,
      brightness: photo.adjustments?.brightness || 0,
      contrast: photo.adjustments?.contrast || 0,
      saturation: photo.adjustments?.saturation || 0,
      removeGlasses: photo.adjustments?.removeGlasses || false,
      removeSmile: photo.adjustments?.removeSmile || false,
      cropAdjustment: photo.adjustments?.cropAdjustment || 0,
    });
    
    setPreviewSettings({
      spec: photo.spec,
      backgroundColor: photo.backgroundColor,
      ...photo.adjustments
    });
    
    message.info('已重置所有编辑');
  };
  
  // 如果正在加载，显示加载状态
  if (loading && !photo) {
    return (
      <div className="edit-photo-page page-container">
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </div>
    );
  }
  
  // 如果加载失败，显示错误信息
  if (error && !photo) {
    return (
      <div className="edit-photo-page page-container">
        <Alert
          message="加载失败"
          description={error || '无法加载照片信息，请稍后再试'}
          type="error"
          showIcon
          action={
            <Button type="primary">
              <Link to={ROUTES.MY_PHOTOS}>返回我的照片</Link>
            </Button>
          }
        />
      </div>
    );
  }
  
  // 如果没有照片数据，显示空状态
  if (!photo) {
    return (
      <div className="edit-photo-page page-container">
        <Alert
          message="照片不存在"
          description="未找到相关照片信息，可能已被删除或链接无效"
          type="warning"
          showIcon
          action={
            <Button type="primary">
              <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
            </Button>
          }
        />
      </div>
    );
  }
  
  return (
    <div className="edit-photo-page page-container">
      <div className="page-title">
        <Space>
          <Button icon={<ArrowLeftOutlined />}>
            <Link to={`${ROUTES.PHOTO_RESULT}/${photo.id}`}>返回照片详情</Link>
          </Button>
          <Title level={2} style={{ margin: 0 }}>编辑照片</Title>
        </Space>
        <Space>
          <Button 
            icon={<UndoOutlined />} 
            onClick={handleReset}
          >
            重置
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            onClick={() => form.submit()}
            loading={saveLoading}
            className="btn-primary"
          >
            保存
          </Button>
          <Button 
            icon={<DownloadOutlined />}
            onClick={handleDownload}
            loading={downloadLoading}
          >
            下载
          </Button>
        </Space>
      </div>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} md={12}>
          <Card bordered={false} title="照片预览">
            <div style={{ textAlign: 'center' }}>
              {photo.resultUrl ? (
                <PhotoPreview 
                  photo={photo} 
                  settings={previewSettings}
                  style={{ maxWidth: '100%', maxHeight: 400 }}
                />
              ) : (
                <div className="loading-container">
                  <Spin size="large" />
                </div>
              )}
            </div>
            
            <Paragraph type="secondary" style={{ textAlign: 'center', marginTop: 16 }}>
              预览效果仅供参考，实际效果可能有所不同
            </Paragraph>
          </Card>
        </Col>
        
        <Col xs={24} md={12}>
          <Card bordered={false}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              onValuesChange={handleValuesChange}
              initialValues={{
                name: photo.name || '',
                spec: photo.spec,
                backgroundColor: photo.backgroundColor,
                enhanceQuality: photo.enhanceQuality,
                brightness: photo.adjustments?.brightness || 0,
                contrast: photo.adjustments?.contrast || 0,
                saturation: photo.adjustments?.saturation || 0,
                removeGlasses: photo.adjustments?.removeGlasses || false,
                removeSmile: photo.adjustments?.removeSmile || false,
                cropAdjustment: photo.adjustments?.cropAdjustment || 0,
              }}
            >
              <Tabs activeKey={activeTab} onChange={setActiveTab}>
                <TabPane tab="基本设置" key="basic">
                  <Form.Item
                    name="name"
                    label="照片名称"
                  >
                    <input placeholder="输入照片名称" />
                  </Form.Item>
                  
                  <Form.Item
                    name="spec"
                    label="证件照规格"
                    rules={[{ required: true, message: '请选择证件照规格' }]}
                  >
                    <Select placeholder="选择证件照规格">
                      {Object.entries(PHOTO_SPECS).map(([key, spec]) => (
                        <Option key={key} value={key}>
                          {spec.name} ({spec.width}mm × {spec.height}mm)
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  
                  <Form.Item
                    name="backgroundColor"
                    label="背景颜色"
                    rules={[{ required: true, message: '请选择背景颜色' }]}
                  >
                    <Radio.Group>
                      {Object.entries(BACKGROUND_COLORS).map(([key, color]) => (
                        <Radio.Button 
                          key={key} 
                          value={key}
                          style={{ 
                            backgroundColor: color.hex,
                            color: ['white', 'blue', 'red'].includes(key) ? '#fff' : '#000',
                            marginRight: 8,
                            marginBottom: 8,
                          }}
                        >
                          {color.name}
                        </Radio.Button>
                      ))}
                    </Radio.Group>
                  </Form.Item>
                  
                  <Form.Item
                    name="enhanceQuality"
                    label="照片增强"
                    valuePropName="checked"
                  >
                    <Switch 
                      checkedChildren={<CheckOutlined />}
                      unCheckedChildren={<CloseOutlined />}
                    />
                  </Form.Item>
                </TabPane>
              </Tabs>
              
              <Divider />
              
              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={saveLoading}
                    className="btn-primary"
                  >
                    保存更改
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default EditPhotoPage;
                  </Form.Item>
                </TabPane>
                
                <TabPane tab="调整" key="adjustments">
                  <Form.Item
                    name="brightness"
                    label="亮度"
                    tooltip="调整照片的亮度，范围 -100 到 100"
                  >
                    <Slider 
                      min={-100} 
                      max={100} 
                      marks={{ 
                        '-100': '暗', 
                        0: '默认', 
                        100: '亮' 
                      }} 
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="contrast"
                    label="对比度"
                    tooltip="调整照片的对比度，范围 -100 到 100"
                  >
                    <Slider 
                      min={-100} 
                      max={100} 
                      marks={{ 
                        '-100': '低', 
                        0: '默认', 
                        100: '高' 
                      }} 
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="saturation"
                    label="饱和度"
                    tooltip="调整照片的饱和度，范围 -100 到 100"
                  >
                    <Slider 
                      min={-100} 
                      max={100} 
                      marks={{ 
                        '-100': '低', 
                        0: '默认', 
                        100: '高' 
                      }} 
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="cropAdjustment"
                    label="裁剪调整"
                    tooltip="调整照片的裁剪位置，范围 -50 到 50"
                  >
                    <Slider 
                      min={-50} 
                      max={50} 
                      marks={{ 
                        '-50': '上移', 
                        0: '默认', 
                        50: '下移' 
                      }} 
                    />
                  </Form.Item>
                </TabPane>
                
                <TabPane tab="AI 增强" key="ai">
                  <Paragraph>
                    使用AI技术增强您的证件照，使其符合标准要求。
                  </Paragraph>
                  
                  <Form.Item
                    name="removeGlasses"
                    label="移除眼镜"
                    valuePropName="checked"
                    tooltip="使用AI技术移除照片中的眼镜"
                  >
                    <Switch 
                      checkedChildren={<CheckOutlined />}
                      unCheckedChildren={<CloseOutlined />}
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="removeSmile"
                    label="调整表情"
                    valuePropName="checked"
                    tooltip="使用AI技术调整为标准证件照表情"
                  >
                    <Switch 
                      checkedChildren={<CheckOutlined />}
                      unCheckedChildren={<CloseOutlined />}
                    />
                