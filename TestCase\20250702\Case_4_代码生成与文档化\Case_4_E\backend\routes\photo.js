const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');

// 模拟任务数据库
const tasks = [];
// 模拟生成的照片数据库
const generatedPhotos = [];

/**
 * @route   POST api/photo/generate
 * @desc    生成证件照
 * @access  Private
 */
router.post('/generate', async (req, res) => {
  try {
    const { upload_id, spec, background, count = 5, model_config } = req.body;

    if (!upload_id || !spec || !background) {
      return res.status(400).json({ message: '缺少必要参数' });
    }

    // 生成任务ID
    const taskId = uuidv4();
    
    // 创建新任务
    const newTask = {
      id: taskId,
      upload_id,
      spec,
      background,
      status: 'processing',
      created_at: new Date(),
      completed_at: null,
      model_config: model_config || { model_name: 'default_model' }
    };
    
    // 保存任务（实际项目中应该保存到数据库）
    tasks.push(newTask);
    
    // 模拟异步处理
    // 在实际项目中，这里应该将任务发送到消息队列或后台处理服务
    setTimeout(() => {
      // 模拟生成照片
      const photos = [];
      for (let i = 0; i < count; i++) {
        const photoId = uuidv4();
        const qualityScore = (Math.random() * 0.2 + 0.8).toFixed(2); // 0.8-1.0之间的随机分数
        
        // 模拟生成的照片URL
        // 实际项目中，这里应该是AI处理后的照片URL
        const photoUrl = `/uploads/generated/${photoId}.jpg`;
        
        // 创建照片记录
        const photo = {
          id: photoId,
          task_id: taskId,
          url: photoUrl,
          quality_score: qualityScore,
          created_at: new Date()
        };
        
        photos.push(photo);
        generatedPhotos.push(photo);
      }
      
      // 更新任务状态
      const taskIndex = tasks.findIndex(task => task.id === taskId);
      if (taskIndex !== -1) {
        tasks[taskIndex].status = 'completed';
        tasks[taskIndex].completed_at = new Date();
      }
    }, 5000); // 模拟5秒处理时间
    
    // 立即返回任务创建成功
    res.status(200).json({
      code: 200,
      message: '生成任务已创建',
      data: {
        task_id: taskId,
        status: 'processing',
        generated_photos: [] // 初始为空，需要通过任务状态查询接口获取结果
      }
    });
  } catch (err) {
    console.error('证件照生成错误:', err);
    res.status(500).json({ message: '证件照生成失败', error: err.message });
  }
});

/**
 * @route   GET api/photo/task/:taskId
 * @desc    获取任务状态
 * @access  Private
 */
router.get('/task/:taskId', (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 查找任务
    const task = tasks.find(task => task.id === taskId);
    
    if (!task) {
      return res.status(404).json({ message: '任务不存在' });
    }
    
    // 获取该任务生成的照片
    const photos = generatedPhotos.filter(photo => photo.task_id === taskId);
    
    // 计算进度
    let progress = 0;
    if (task.status === 'completed') {
      progress = 100;
    } else if (task.status === 'processing') {
      // 模拟进度计算
      const now = new Date();
      const elapsedTime = now - new Date(task.created_at);
      progress = Math.min(Math.floor(elapsedTime / 50), 99); // 最多到99%
    }
    
    res.status(200).json({
      code: 200,
      data: {
        task_id: task.id,
        status: task.status,
        progress,
        spec: task.spec,
        background: task.background,
        created_at: task.created_at,
        completed_at: task.completed_at,
        generated_photos: photos.map(photo => ({
          id: photo.id,
          url: photo.url,
          quality_score: photo.quality_score
        }))
      }
    });
  } catch (err) {
    console.error('获取任务状态错误:', err);
    res.status(500).json({ message: '获取任务状态失败', error: err.message });
  }
});

/**
 * @route   POST api/photo/download
 * @desc    下载照片
 * @access  Private
 */
router.post('/download', (req, res) => {
  try {
    const { photo_ids } = req.body;
    
    if (!photo_ids || !Array.isArray(photo_ids) || photo_ids.length === 0) {
      return res.status(400).json({ message: '请提供要下载的照片ID' });
    }
    
    // 查找照片
    const photos = generatedPhotos.filter(photo => photo_ids.includes(photo.id));
    
    if (photos.length === 0) {
      return res.status(404).json({ message: '未找到指定的照片' });
    }
    
    // 在实际项目中，这里应该处理文件下载或生成下载链接
    // 这里只是模拟返回下载信息
    res.status(200).json({
      code: 200,
      message: '下载请求成功',
      data: {
        download_id: uuidv4(),
        photos: photos.map(photo => ({
          id: photo.id,
          url: photo.url,
          download_url: `/api/photo/download/${photo.id}` // 模拟下载链接
        })),
        total_count: photos.length
      }
    });
  } catch (err) {
    console.error('照片下载错误:', err);
    res.status(500).json({ message: '照片下载失败', error: err.message });
  }
});

/**
 * @route   GET api/photo/download/:photoId
 * @desc    下载单张照片
 * @access  Private
 */
router.get('/download/:photoId', (req, res) => {
  try {
    const { photoId } = req.params;
    
    // 查找照片
    const photo = generatedPhotos.find(photo => photo.id === photoId);
    
    if (!photo) {
      return res.status(404).json({ message: '照片不存在' });
    }
    
    // 在实际项目中，这里应该返回实际的文件
    // 这里只是模拟返回一个JSON响应
    res.status(200).json({
      code: 200,
      message: '照片下载成功',
      data: {
        photo_id: photo.id,
        url: photo.url
      }
    });
  } catch (err) {
    console.error('照片下载错误:', err);
    res.status(500).json({ message: '照片下载失败', error: err.message });
  }
});

module.exports = router;