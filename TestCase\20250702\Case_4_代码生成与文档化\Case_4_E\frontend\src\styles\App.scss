// 主题颜色变量
:root {
  --primary-color: #1890ff;
  --secondary-color: #52c41a;
  --danger-color: #f5222d;
  --warning-color: #faad14;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --border-color: #d9d9d9;
  --background-color: #f0f2f5;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

// 页面过渡动画
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

// 按钮样式
.btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  
  &-primary {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
    
    &:hover {
      background-color: darken(#1890ff, 10%);
      border-color: darken(#1890ff, 10%);
    }
  }
  
  &-secondary {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    
    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
    }
  }
}

// 卡片样式
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: 20px;
  margin-bottom: 20px;
}

// 表单样式
.form-group {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  input, select, textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// 辅助类
.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}