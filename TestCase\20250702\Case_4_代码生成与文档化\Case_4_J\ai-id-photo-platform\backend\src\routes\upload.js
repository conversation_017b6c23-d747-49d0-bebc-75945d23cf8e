const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const { uploadSingle, uploadMultiple, handleUploadError } = require('../middleware/upload');
const { optionalAuth } = require('../middleware/auth');
const { validate, uploadSchema } = require('../utils/validation');

/**
 * @route POST /api/upload/single
 * @desc 单文件上传
 * @access Public (可选认证)
 */
router.post('/single', 
  optionalAuth,
  uploadSingle,
  handleUploadError,
  validate(uploadSchema),
  uploadController.uploadSingle
);

/**
 * @route POST /api/upload/multiple
 * @desc 多文件上传
 * @access Public (可选认证)
 */
router.post('/multiple',
  optionalAuth,
  uploadMultiple,
  handleUploadError,
  validate(uploadSchema),
  uploadController.uploadMultiple
);

/**
 * @route GET /api/upload/:uploadId
 * @desc 获取上传记录
 * @access Public (可选认证)
 */
router.get('/:uploadId',
  optionalAuth,
  uploadController.getUpload
);

/**
 * @route DELETE /api/upload/:uploadId
 * @desc 删除上传记录
 * @access Public (可选认证)
 */
router.delete('/:uploadId',
  optionalAuth,
  uploadController.deleteUpload
);

module.exports = router;
