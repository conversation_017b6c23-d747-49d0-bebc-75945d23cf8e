const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 模拟用户数据库
const users = [];

/**
 * @route   POST api/auth/register
 * @desc    注册新用户
 * @access  Public
 */
router.post(
  '/register',
  [
    body('username').not().isEmpty().withMessage('用户名不能为空'),
    body('email').isEmail().withMessage('请提供有效的邮箱地址'),
    body('password').isLength({ min: 6 }).withMessage('密码至少需要6个字符'),
    body('phone').matches(/^1[3-9]\d{9}$/).withMessage('请提供有效的手机号码')
  ],
  async (req, res) => {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, email, password, phone } = req.body;

    try {
      // 检查用户是否已存在
      const userExists = users.find(user => user.email === email || user.username === username);
      if (userExists) {
        return res.status(400).json({ message: '用户已存在' });
      }

      // 加密密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      // 创建新用户
      const newUser = {
        id: users.length + 1,
        username,
        email,
        phone,
        password: hashedPassword,
        createdAt: new Date(),
        credits: 10 // 新用户赠送10积分
      };

      // 保存用户（实际项目中应该保存到数据库）
      users.push(newUser);

      res.status(201).json({
        message: '注册成功',
        user: {
          id: newUser.id,
          username: newUser.username,
          email: newUser.email,
          phone: newUser.phone,
          credits: newUser.credits
        }
      });
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: '服务器错误' });
    }
  }
);

/**
 * @route   POST api/auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post(
  '/login',
  [
    body('username').not().isEmpty().withMessage('请输入用户名或邮箱'),
    body('password').exists().withMessage('请输入密码')
  ],
  async (req, res) => {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, password } = req.body;

    try {
      // 查找用户（实际项目中应该从数据库查询）
      const user = users.find(
        user => user.username === username || user.email === username
      );

      if (!user) {
        return res.status(400).json({ message: '用户不存在' });
      }

      // 验证密码
      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        return res.status(400).json({ message: '密码错误' });
      }

      // 生成JWT令牌
      const payload = {
        user: {
          id: user.id
        }
      };

      jwt.sign(
        payload,
        process.env.JWT_SECRET || 'secret_key',
        { expiresIn: '24h' },
        (err, token) => {
          if (err) throw err;
          res.json({
            message: '登录成功',
            token,
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              phone: user.phone,
              credits: user.credits
            }
          });
        }
      );
    } catch (err) {
      console.error(err);
      res.status(500).json({ message: '服务器错误' });
    }
  }
);

module.exports = router;