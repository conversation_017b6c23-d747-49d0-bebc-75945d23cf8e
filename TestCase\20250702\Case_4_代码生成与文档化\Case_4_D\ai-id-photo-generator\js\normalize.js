/**
 * AI证件照生成平台 - 浏览器兼容性脚本
 * 版本: 1.0.0
 */

// 检查浏览器支持
(function() {
    // 检查是否支持FileReader API
    if (!window.FileReader) {
        showBrowserWarning('您的浏览器不支持文件上传功能，请使用现代浏览器如Chrome、Firefox、Edge或Safari的最新版本。');
    }
    
    // 检查是否支持Fetch API
    if (!window.fetch) {
        // 添加fetch polyfill
        console.warn('浏览器不支持Fetch API，已加载polyfill');
        loadScript('https://cdn.jsdelivr.net/npm/whatwg-fetch@3.6.2/dist/fetch.umd.min.js');
    }
    
    // 检查是否支持Promise
    if (!window.Promise) {
        // 添加Promise polyfill
        console.warn('浏览器不支持Promise，已加载polyfill');
        loadScript('https://cdn.jsdelivr.net/npm/promise-polyfill@8.2.3/dist/polyfill.min.js');
    }
    
    // 检查是否支持URL.createObjectURL
    if (!window.URL || !window.URL.createObjectURL) {
        showBrowserWarning('您的浏览器不支持预览功能，请使用现代浏览器如Chrome、Firefox、Edge或Safari的最新版本。');
    }
})();

/**
 * 显示浏览器兼容性警告
 * @param {string} message - 警告消息
 */
function showBrowserWarning(message) {
    // 创建警告元素
    const warning = document.createElement('div');
    warning.className = 'browser-warning';
    warning.innerHTML = `
        <div class="browser-warning-content">
            <h3>浏览器兼容性警告</h3>
            <p>${message}</p>
            <div class="browser-links">
                <a href="https://www.google.com/chrome/" target="_blank">下载Chrome</a>
                <a href="https://www.mozilla.org/firefox/" target="_blank">下载Firefox</a>
                <a href="https://www.microsoft.com/edge" target="_blank">下载Edge</a>
            </div>
        </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .browser-warning {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .browser-warning-content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 500px;
            text-align: center;
        }
        
        .browser-warning h3 {
            margin-bottom: 15px;
            color: #dc3545;
        }
        
        .browser-warning p {
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .browser-links {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .browser-links a {
            display: inline-block;
            padding: 8px 15px;
            background-color: #4a6bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .browser-links a:hover {
            background-color: #3a56d4;
        }
    `;
    
    // 添加到文档
    document.head.appendChild(style);
    document.body.appendChild(warning);
}

/**
 * 动态加载脚本
 * @param {string} src - 脚本URL
 * @param {Function} callback - 加载完成回调
 */
function loadScript(src, callback) {
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    
    if (callback) {
        script.onload = callback;
    }
    
    document.head.appendChild(script);
}

/**
 * 检测设备类型
 * @returns {Object} 设备信息
 */
function detectDevice() {
    const ua = navigator.userAgent;
    const device = {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        os: 'unknown'
    };
    
    // 检测移动设备
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)) {
        device.isDesktop = false;
        
        // 区分平板和手机
        if (/iPad|Android(?!.*Mobile)/i.test(ua)) {
            device.isTablet = true;
        } else {
            device.isMobile = true;
        }
    }
    
    // 检测操作系统
    if (/Windows/i.test(ua)) {
        device.os = 'windows';
    } else if (/Macintosh|Mac OS X/i.test(ua)) {
        device.os = 'mac';
    } else if (/Android/i.test(ua)) {
        device.os = 'android';
    } else if (/iPhone|iPad|iPod/i.test(ua)) {
        device.os = 'ios';
    } else if (/Linux/i.test(ua)) {
        device.os = 'linux';
    }
    
    return device;
}

/**
 * 检测浏览器类型和版本
 * @returns {Object} 浏览器信息
 */
function detectBrowser() {
    const ua = navigator.userAgent;
    let browser = {
        name: 'unknown',
        version: 'unknown'
    };
    
    // Chrome
    if (/Chrome/.test(ua) && !/Chromium|Edge|Edg|OPR|SamsungBrowser/.test(ua)) {
        browser.name = 'chrome';
        browser.version = ua.match(/Chrome\/(\d+\.\d+)/)[1];
    }
    // Firefox
    else if (/Firefox/.test(ua)) {
        browser.name = 'firefox';
        browser.version = ua.match(/Firefox\/(\d+\.\d+)/)[1];
    }
    // Safari
    else if (/Safari/.test(ua) && !/Chrome|Chromium|Edge|Edg|OPR/.test(ua)) {
        browser.name = 'safari';
        browser.version = ua.match(/Version\/(\d+\.\d+)/)[1];
    }
    // Edge
    else if (/Edge|Edg/.test(ua)) {
        browser.name = 'edge';
        browser.version = ua.match(/(?:Edge|Edg)\/(\d+\.\d+)/)[1];
    }
    // Opera
    else if (/OPR/.test(ua)) {
        browser.name = 'opera';
        browser.version = ua.match(/OPR\/(\d+\.\d+)/)[1];
    }
    // IE
    else if (/Trident/.test(ua)) {
        browser.name = 'ie';
        browser.version = ua.match(/rv:(\d+\.\d+)/) ? ua.match(/rv:(\d+\.\d+)/)[1] : 'unknown';
    }
    
    return browser;
}

/**
 * 添加浏览器和设备类名到HTML元素
 */
(function addDeviceClasses() {
    const device = detectDevice();
    const browser = detectBrowser();
    const html = document.documentElement;
    
    // 添加设备类型
    if (device.isMobile) {
        html.classList.add('is-mobile');
    } else if (device.isTablet) {
        html.classList.add('is-tablet');
    } else {
        html.classList.add('is-desktop');
    }
    
    // 添加操作系统
    html.classList.add(`os-${device.os}`);
    
    // 添加浏览器
    html.classList.add(`browser-${browser.name}`);
    
    // 添加浏览器版本主版本号
    const majorVersion = parseInt(browser.version);
    if (!isNaN(majorVersion)) {
        html.classList.add(`browser-${browser.name}-${majorVersion}`);
    }
})();

/**
 * 检测网络状态
 */
(function monitorNetworkStatus() {
    function updateNetworkStatus() {
        const isOnline = navigator.onLine;
        document.documentElement.classList.toggle('is-offline', !isOnline);
        
        if (!isOnline) {
            showOfflineWarning();
        } else {
            hideOfflineWarning();
        }
    }
    
    // 初始检查
    updateNetworkStatus();
    
    // 监听网络状态变化
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);
    
    function showOfflineWarning() {
        // 检查是否已存在离线提示
        if (document.querySelector('.offline-warning')) return;
        
        const warning = document.createElement('div');
        warning.className = 'offline-warning';
        warning.innerHTML = `
            <div class="offline-warning-content">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4C7.31 4 3.07 5.9 0 8.98L12 21L24 8.98C20.93 5.9 16.69 4 12 4Z" fill="#ccc"/>
                    <path d="M2.92 8.98C4.95 7.18 7.33 5.97 9.84 5.35L5.74 9.45L2.92 8.98Z" fill="#f44336"/>
                    <path d="M21.08 8.98C19.05 7.18 16.67 5.97 14.16 5.35L18.26 9.45L21.08 8.98Z" fill="#f44336"/>
                    <path d="M12 4C10.36 4 8.71 4.27 7.13 4.74L12 9.61L16.87 4.74C15.29 4.27 13.64 4 12 4Z" fill="#f44336"/>
                </svg>
                <span>您当前处于离线状态，部分功能可能无法使用</span>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .offline-warning {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background-color: #f8d7da;
                color: #721c24;
                padding: 10px;
                text-align: center;
                z-index: 9999;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            .offline-warning-content {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
            }
        `;
        
        document.head.appendChild(style);
        document.body.prepend(warning);
    }
    
    function hideOfflineWarning() {
        const warning = document.querySelector('.offline-warning');
        if (warning) {
            warning.remove();
        }
    }
})();