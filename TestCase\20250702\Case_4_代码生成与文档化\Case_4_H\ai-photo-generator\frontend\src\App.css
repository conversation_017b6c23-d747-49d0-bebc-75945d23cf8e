/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease;
}

/* 暗黑主题 */
body.dark-theme {
  background-color: #141414;
  color: rgba(255, 255, 255, 0.85);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

body.dark-theme ::-webkit-scrollbar-track {
  background: #1f1f1f;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 页面过渡动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 300ms, transform 300ms;
}

/* 照片卡片样式 */
.photo-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.photo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

body.dark-theme .photo-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* 上传区域样式 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #1890ff;
}

body.dark-theme .upload-area {
  border-color: #434343;
}

body.dark-theme .upload-area:hover {
  border-color: #1890ff;
}

/* 自定义按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

/* 结果展示区域 */
.result-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

body.dark-theme .result-container {
  background-color: #1f1f1f;
}

/* 照片编辑器样式 */
.photo-editor {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}

body.dark-theme .photo-editor {
  border-color: #434343;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
}

body.dark-theme .toolbar {
  background-color: #1f1f1f;
  border-color: #434343;
}

/* 标签样式 */
.custom-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 表单样式 */
.form-container {
  max-width: 600px;
  margin: 0 auto;
}

/* 页面容器 */
.page-container {
  padding: 24px;
  min-height: calc(100vh - 64px - 70px); /* 减去头部和底部的高度 */
}

/* 页面标题 */
.page-title {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 卡片网格布局 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 48px 0;
}

/* 头像上传样式 */
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
  border-radius: 50%;
  overflow: hidden;
}

/* 预览图片样式 */
.preview-image {
  max-width: 100%;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

body.dark-theme .preview-image {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}