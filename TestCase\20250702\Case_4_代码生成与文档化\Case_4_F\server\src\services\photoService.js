const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const Upload = require('../models/Upload');
const GenerationTask = require('../models/GenerationTask');
const GeneratedPhoto = require('../models/GeneratedPhoto');
const User = require('../models/User');

// 创建生成任务
const createGenerationTask = async (uploadId, spec, background, userId = null) => {
  try {
    // 查询上传记录
    const upload = await Upload.findByPk(uploadId);
    if (!upload) {
      throw new Error('未找到上传记录');
    }
    
    // 如果有用户ID，检查用户积分
    if (userId) {
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('未找到用户');
      }
      
      if (user.credits <= 0) {
        throw new Error('积分不足，无法生成照片');
      }
      
      // 扣除积分
      await user.update({
        credits: user.credits - 1
      });
    }
    
    // 创建任务
    const taskId = uuidv4();
    const task = await GenerationTask.create({
      id: taskId,
      uploadId: upload.id,
      spec,
      background,
      status: 'pending'
    });
    
    // 异步执行生成任务
    processGenerationTask(task);
    
    return {
      success: true,
      taskId: task.id
    };
  } catch (error) {
    console.error('创建生成任务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 处理生成任务
const processGenerationTask = async (task) => {
  try {
    // 更新任务状态
    await task.update({
      status: 'processing',
      progress: 10
    });
    
    // 获取上传记录
    const upload = await Upload.findByPk(task.uploadId);
    if (!upload) {
      throw new Error('未找到上传记录');
    }
    
    // 调用AI模型生成照片 - 这里是模拟过程
    // 实际应用中，这里应该调用真实的AI模型API
    await simulateAIProcessing(task);
    
    // 更新任务完成
    await task.update({
      status: 'completed',
      progress: 100,
      completedAt: new Date()
    });
  } catch (error) {
    console.error('生成任务处理失败:', error);
    await task.update({
      status: 'failed',
      progress: 0
    });
  }
};

// 模拟AI处理过程
const simulateAIProcessing = async (task) => {
  return new Promise(async (resolve) => {
    // 更新进度到30%
    await task.update({ progress: 30 });
    
    // 等待2秒
    await new Promise(r => setTimeout(r, 2000));
    
    // 更新进度到60%
    await task.update({ progress: 60 });
    
    // 等待2秒
    await new Promise(r => setTimeout(r, 2000));
    
    // 创建5张模拟照片
    const photos = [];
    for (let i = 0; i < 5; i++) {
      const photoId = uuidv4();
      const filePath = `generated/${task.id}/${photoId}.jpg`;
      const fileUrl = `/public/${filePath}`;
      const qualityScore = (Math.random() * 0.3 + 0.7).toFixed(2); // 0.7-1.0之间
      
      photos.push(await GeneratedPhoto.create({
        id: photoId,
        taskId: task.id,
        filePath,
        fileUrl,
        qualityScore
      }));
    }
    
    // 更新进度到90%
    await task.update({ progress: 90 });
    
    // 等待1秒
    await new Promise(r => setTimeout(r, 1000));
    
    resolve();
  });
};

// 查询任务状态
const getTaskStatus = async (taskId) => {
  try {
    const task = await GenerationTask.findByPk(taskId);
    if (!task) {
      throw new Error('未找到任务');
    }
    
    // 获取生成的照片
    let generatedPhotos = [];
    if (task.status === 'completed') {
      const photos = await GeneratedPhoto.findAll({
        where: { taskId: task.id }
      });
      
      generatedPhotos = photos.map(photo => ({
        id: photo.id,
        url: photo.fileUrl,
        quality_score: photo.qualityScore
      }));
    }
    
    return {
      success: true,
      task_id: task.id,
      status: task.status,
      progress: task.progress,
      generated_photos: generatedPhotos
    };
  } catch (error) {
    console.error('查询任务状态失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// 下载照片
const downloadPhotos = async (photoIds) => {
  try {
    // 查询照片记录
    const photos = await GeneratedPhoto.findAll({
      where: {
        id: photoIds
      }
    });
    
    if (photos.length === 0) {
      throw new Error('未找到照片');
    }
    
    // 如果只有一张照片，直接返回路径
    if (photos.length === 1) {
      return {
        success: true,
        filePath: photos[0].filePath
      };
    }
    
    // 如果有多张照片，创建ZIP文件
    const zipFileName = `photos_${Date.now()}.zip`;
    const zipFilePath = path.join(__dirname, '../../public/downloads', zipFileName);
    
    // 确保目录存在
    const downloadsDir = path.join(__dirname, '../../public/downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }
    
    // 创建ZIP文件
    const archive = archiver('zip', {
      zlib: { level: 9 } // 最高压缩等级
    });
    
    const output = fs.createWriteStream(zipFilePath);
    archive.pipe(output);
    
    // 添加文件到ZIP
    for (const photo of photos) {
      const filePath = path.join(__dirname, '../../public', photo.filePath);
      archive.file(filePath, { name: `photo_${photo.id}.jpg` });
    }
    
    await archive.finalize();
    
    return {
      success: true,
      filePath: `/public/downloads/${zipFileName}`
    };
  } catch (error) {
    console.error('下载照片失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  createGenerationTask,
  getTaskStatus,
  downloadPhotos
}; 