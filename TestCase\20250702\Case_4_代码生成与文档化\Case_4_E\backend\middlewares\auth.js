const jwt = require('jsonwebtoken');

/**
 * 认证中间件
 * 验证请求头中的JWT令牌
 */
module.exports = function(req, res, next) {
  // 获取token
  const token = req.header('x-auth-token');

  // 检查是否有token
  if (!token) {
    return res.status(401).json({ message: '无访问权限，请先登录' });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret_key');
    
    // 将用户ID添加到请求对象
    req.user = decoded.user;
    next();
  } catch (err) {
    res.status(401).json({ message: '无效的令牌' });
  }
};