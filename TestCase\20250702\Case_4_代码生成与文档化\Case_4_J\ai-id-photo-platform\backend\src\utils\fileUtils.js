const fs = require('fs').promises;
const path = require('path');
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');

/**
 * 确保目录存在
 * @param {string} dirPath 目录路径
 */
const ensureDirectoryExists = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch (error) {
    await fs.mkdir(dirPath, { recursive: true });
  }
};

/**
 * 生成唯一文件名
 * @param {string} originalName 原始文件名
 * @param {string} prefix 前缀
 * @returns {string} 新文件名
 */
const generateUniqueFileName = (originalName, prefix = '') => {
  const ext = path.extname(originalName);
  const uuid = uuidv4();
  return `${prefix}${uuid}${ext}`;
};

/**
 * 获取图片元数据
 * @param {string} filePath 文件路径
 * @returns {Object} 图片元数据
 */
const getImageMetadata = async (filePath) => {
  try {
    const metadata = await sharp(filePath).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      size: metadata.size,
      density: metadata.density,
      hasAlpha: metadata.hasAlpha,
      orientation: metadata.orientation
    };
  } catch (error) {
    throw new Error(`获取图片元数据失败: ${error.message}`);
  }
};

/**
 * 调整图片大小
 * @param {string} inputPath 输入文件路径
 * @param {string} outputPath 输出文件路径
 * @param {number} width 宽度
 * @param {number} height 高度
 * @param {Object} options 选项
 */
const resizeImage = async (inputPath, outputPath, width, height, options = {}) => {
  try {
    await sharp(inputPath)
      .resize(width, height, {
        fit: options.fit || 'cover',
        position: options.position || 'center',
        background: options.background || { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .jpeg({ quality: options.quality || 90 })
      .toFile(outputPath);
  } catch (error) {
    throw new Error(`调整图片大小失败: ${error.message}`);
  }
};

/**
 * 压缩图片
 * @param {string} inputPath 输入文件路径
 * @param {string} outputPath 输出文件路径
 * @param {Object} options 压缩选项
 */
const compressImage = async (inputPath, outputPath, options = {}) => {
  try {
    const { quality = 80, format = 'jpeg' } = options;
    
    let pipeline = sharp(inputPath);
    
    if (format === 'jpeg') {
      pipeline = pipeline.jpeg({ quality });
    } else if (format === 'png') {
      pipeline = pipeline.png({ quality });
    } else if (format === 'webp') {
      pipeline = pipeline.webp({ quality });
    }
    
    await pipeline.toFile(outputPath);
  } catch (error) {
    throw new Error(`压缩图片失败: ${error.message}`);
  }
};

/**
 * 删除文件
 * @param {string} filePath 文件路径
 */
const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
  } catch (error) {
    // 文件不存在时忽略错误
    if (error.code !== 'ENOENT') {
      throw error;
    }
  }
};

/**
 * 获取文件大小
 * @param {string} filePath 文件路径
 * @returns {number} 文件大小（字节）
 */
const getFileSize = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    throw new Error(`获取文件大小失败: ${error.message}`);
  }
};

module.exports = {
  ensureDirectoryExists,
  generateUniqueFileName,
  getImageMetadata,
  resizeImage,
  compressImage,
  deleteFile,
  getFileSize
};
