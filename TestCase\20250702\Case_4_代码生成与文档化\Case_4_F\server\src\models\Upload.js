const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const User = require('./User');

const Upload = sequelize.define('Upload', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    }
  },
  originalFilename: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  filePath: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  fileSize: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  fileType: {
    type: DataTypes.STRING(20),
    allowNull: false
  }
}, {
  timestamps: true,
  tableName: 'uploads',
  indexes: [
    {
      fields: ['userId']
    }
  ]
});

// 关联关系
Upload.belongsTo(User, { foreignKey: 'userId', as: 'user' });
User.hasMany(Upload, { foreignKey: 'userId', as: 'uploads' });

module.exports = Upload; 