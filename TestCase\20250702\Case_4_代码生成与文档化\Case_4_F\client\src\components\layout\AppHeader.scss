.app-header {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;
  
  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }
  
  .logo {
    h1 {
      font-size: 20px;
      margin: 0;
      color: #1890ff;
    }
  }
  
  .nav-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    
    .ant-menu {
      border-bottom: none;
      background: transparent;
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-dropdown {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      .username {
        margin: 0 8px;
      }
    }
    
    .auth-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
    
    .nav-menu {
      display: none;
    }
  }
} 