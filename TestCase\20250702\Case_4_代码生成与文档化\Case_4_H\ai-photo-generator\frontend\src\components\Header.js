import React from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space } from 'antd';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
} from '@ant-design/icons';
import { logout } from '../store/authSlice';
import { toggleSidebar } from '../store/uiSlice';
import { ROUTES } from '../utils/constants';

const { Header: AntHeader } = Layout;

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const { sidebarCollapsed } = useSelector((state) => state.ui);

  const handleLogout = () => {
    dispatch(logout());
    navigate(ROUTES.LOGIN);
  };

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to={ROUTES.USER}>个人中心</Link>
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        <Link to={`${ROUTES.USER}/settings`}>账号设置</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  const renderUserSection = () => {
    if (isAuthenticated) {
      return (
        <Dropdown overlay={userMenu} trigger={['click']}>
          <Space className="user-dropdown">
            <Avatar icon={<UserOutlined />} src={user?.avatar} />
            <span className="username">{user?.username || '用户'}</span>
          </Space>
        </Dropdown>
      );
    }

    return (
      <Space>
        <Button type="text" onClick={() => navigate(ROUTES.LOGIN)}>
          登录
        </Button>
        <Button type="primary" onClick={() => navigate(ROUTES.REGISTER)}>
          注册
        </Button>
      </Space>
    );
  };

  return (
    <AntHeader className="app-header">
      <div className="header-left">
        {isAuthenticated && (
          <Button
            type="text"
            icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={handleToggleSidebar}
            className="sidebar-toggle"
          />
        )}
        <div className="logo">
          <Link to={ROUTES.HOME}>AI证件照生成器</Link>
        </div>
        <Menu
          theme="dark"
          mode="horizontal"
          selectedKeys={[location.pathname]}
          className="header-menu"
        >
          <Menu.Item key={ROUTES.HOME}>
            <Link to={ROUTES.HOME}>首页</Link>
          </Menu.Item>
          <Menu.Item key={ROUTES.UPLOAD}>
            <Link to={ROUTES.UPLOAD}>生成证件照</Link>
          </Menu.Item>
        </Menu>
      </div>
      <div className="header-right">{renderUserSection()}</div>
    </AntHeader>
  );
};

export default Header;