import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { BrowserRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { getThemeConfig, createThemeAlgorithm } from './theme';
import GlobalStyles from './theme/globalStyles';
import routes from './routes';
import PrivateRoute from './routes/PrivateRoute';
import { fetchUserProfile } from './store/authSlice';
import { ROUTES, THEME_MODES } from './utils/constants';

/**
 * 路由配置组件
 * 
 * 递归渲染路由配置
 */
const RouteConfig = ({ routes }) => {
  return routes.map((route, index) => {
    // 如果路由有子路由，递归渲染
    if (route.children) {
      return (
        <Route key={index} path={route.path} element={route.element}>
          {route.children.map((childRoute, childIndex) => {
            // 检查是否需要认证
            const element = childRoute.requiresAuth ? (
              <PrivateRoute>{childRoute.element}</PrivateRoute>
            ) : (
              childRoute.element
            );
            
            return (
              <Route
                key={`${index}-${childIndex}`}
                path={childRoute.path}
                element={element}
              />
            );
          })}
        </Route>
      );
    }
    
    // 检查是否需要认证
    const element = route.requiresAuth ? (
      <PrivateRoute>{route.element}</PrivateRoute>
    ) : (
      route.element
    );
    
    return <Route key={index} path={route.path} element={element} />;
  });
};

/**
 * 认证检查组件
 * 
 * 在应用加载时检查用户认证状态
 */
const AuthCheck = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, loading } = useSelector(state => state.auth);
  const location = useLocation();
  const navigate = useNavigate();
  
  useEffect(() => {
    // 如果本地存储中有token，尝试获取用户信息
    const token = localStorage.getItem('token');
    if (token && !isAuthenticated && !loading) {
      dispatch(fetchUserProfile());
    }
    
    // 如果用户已登录，且访问的是登录/注册页面，重定向到仪表盘
    if (isAuthenticated && !loading) {
      const authPaths = [ROUTES.LOGIN, ROUTES.REGISTER, ROUTES.FORGOT_PASSWORD, ROUTES.RESET_PASSWORD];
      if (authPaths.includes(location.pathname)) {
        navigate(ROUTES.DASHBOARD);
      }
    }
  }, [dispatch, isAuthenticated, loading, location.pathname, navigate]);
  
  return null;
};

/**
 * 应用主组件
 */
const App = () => {
  const userTheme = useSelector(state => state.auth.user?.settings?.theme || THEME_MODES.LIGHT);
  
  // 获取系统主题偏好
  const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  
  // 确定当前主题模式
  const currentThemeMode = userTheme === THEME_MODES.SYSTEM 
    ? (prefersDarkMode ? THEME_MODES.DARK : THEME_MODES.LIGHT)
    : userTheme;
  
  // 获取主题配置
  const themeConfig = getThemeConfig(currentThemeMode);
  
  // 创建主题算法
  const themeAlgorithm = createThemeAlgorithm(currentThemeMode);
  
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        ...themeConfig,
        algorithm: themeAlgorithm,
      }}
    >
      <GlobalStyles theme={currentThemeMode} />
      <Router>
        <AuthCheck />
        <Routes>
          <RouteConfig routes={routes} />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;