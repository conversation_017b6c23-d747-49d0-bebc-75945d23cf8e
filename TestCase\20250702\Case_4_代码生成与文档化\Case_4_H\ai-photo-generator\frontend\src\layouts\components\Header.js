import React, { useState } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Input } from 'antd';
import { 
  MenuOutlined, 
  UserOutlined, 
  BellOutlined, 
  SearchOutlined, 
  LogoutOutlined, 
  SettingOutlined, 
  CameraOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import styled from 'styled-components';
import { logout } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';

const { Header: AntHeader } = Layout;
const { Search } = Input;

/**
 * 头部容器样式
 */
const StyledHeader = styled(AntHeader)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  position: sticky;
  top: 0;
  
  @media (max-width: 768px) {
    padding: 0 16px;
  }
`;

/**
 * 左侧区域样式
 */
const LeftSection = styled.div`
  display: flex;
  align-items: center;
`;

/**
 * 中间区域样式
 */
const MiddleSection = styled.div`
  flex: 1;
  display: flex;
  justify-content: center;
  
  @media (max-width: 992px) {
    display: none;
  }
`;

/**
 * 右侧区域样式
 */
const RightSection = styled.div`
  display: flex;
  align-items: center;
`;

/**
 * Logo容器样式
 */
const LogoContainer = styled.div`
  margin-right: 24px;
`;

/**
 * Logo样式
 */
const Logo = styled.img`
  height: 32px;
`;

/**
 * 搜索框样式
 */
const StyledSearch = styled(Search)`
  width: 300px;
  
  @media (max-width: 1200px) {
    width: 200px;
  }
`;

/**
 * 移动端菜单按钮样式
 */
const MobileMenuButton = styled(Button)`
  margin-right: 16px;
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
`;

/**
 * 创建按钮样式
 */
const CreateButton = styled(Button)`
  margin-right: 16px;
  
  @media (max-width: 576px) {
    .button-text {
      display: none;
    }
  }
`;

/**
 * 头部组件
 * 
 * @param {Object} props - 组件属性
 * @param {boolean} props.collapsed - 侧边栏是否折叠
 * @param {Function} props.toggleSidebar - 切换侧边栏折叠状态的函数
 */
const Header = ({ collapsed, toggleSidebar }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const user = useSelector(state => state.auth.user);
  const [searchVisible, setSearchVisible] = useState(false);
  
  // 处理搜索
  const handleSearch = (value) => {
    navigate(`${ROUTES.PHOTOS}?search=${encodeURIComponent(value)}`);
  };
  
  // 处理登出
  const handleLogout = () => {
    dispatch(logout());
    navigate(ROUTES.LOGIN);
  };
  
  // 用户下拉菜单项
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: <Link to={ROUTES.PROFILE}>个人资料</Link>,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: <Link to={ROUTES.SETTINGS}>设置</Link>,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: <span onClick={handleLogout}>退出登录</span>,
    },
  ];
  
  // 通知下拉菜单项
  const notificationMenuItems = [
    {
      key: 'notification-1',
      label: '您的照片已生成完成',
    },
    {
      key: 'notification-2',
      label: '您的订阅将在3天后到期',
    },
    {
      key: 'notification-3',
      label: '新功能上线：自定义背景颜色',
    },
    {
      type: 'divider',
    },
    {
      key: 'all-notifications',
      label: <Link to="/notifications">查看所有通知</Link>,
    },
  ];
  
  return (
    <StyledHeader>
      <LeftSection>
        <MobileMenuButton 
          type="text" 
          icon={<MenuOutlined />} 
          onClick={toggleSidebar}
        />
        <LogoContainer>
          <Link to={ROUTES.HOME}>
            <Logo src="/logo.png" alt="AI照片生成器" />
          </Link>
        </LogoContainer>
      </LeftSection>
      
      <MiddleSection>
        <StyledSearch
          placeholder="搜索照片..."
          onSearch={handleSearch}
          allowClear
        />
      </MiddleSection>
      
      <RightSection>
        {/* 移动端搜索按钮 */}
        <Button
          type="text"
          icon={<SearchOutlined />}
          onClick={() => setSearchVisible(!searchVisible)}
          style={{ display: 'none', '@media (max-width: 992px)': { display: 'block' } }}
        />
        
        {/* 创建照片按钮 */}
        <CreateButton type="primary" icon={<PlusOutlined />} onClick={() => navigate(ROUTES.CREATE_PHOTO)}>
          <span className="button-text">创建照片</span>
        </CreateButton>
        
        {/* 通知下拉菜单 */}
        <Dropdown
          menu={{ items: notificationMenuItems }}
          placement="bottomRight"
          arrow
          trigger={['click']}
        >
          <Badge count={3} size="small">
            <Button type="text" icon={<BellOutlined />} style={{ marginRight: 16 }} />
          </Badge>
        </Dropdown>
        
        {/* 用户下拉菜单 */}
        <Dropdown
          menu={{ items: userMenuItems }}
          placement="bottomRight"
          arrow
          trigger={['click']}
        >
          <Space>
            <Avatar 
              src={user?.avatar} 
              icon={!user?.avatar && <UserOutlined />}
              style={{ cursor: 'pointer' }}
            />
          </Space>
        </Dropdown>
      </RightSection>
    </StyledHeader>
  );
};

export default Header;