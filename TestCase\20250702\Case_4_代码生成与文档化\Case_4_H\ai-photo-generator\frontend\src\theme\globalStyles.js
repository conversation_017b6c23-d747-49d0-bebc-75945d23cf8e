import { createGlobalStyle } from 'styled-components';
import { THEME_MODES } from '../utils/constants';

/**
 * 全局样式组件
 * 
 * 定义应用的全局样式
 */
const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html, body {
    height: 100%;
    font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: ${props => props.theme === THEME_MODES.DARK ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)'};
    background-color: ${props => props.theme === THEME_MODES.DARK ? '#141414' : '#f0f2f5'};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  #root {
    height: 100%;
  }
  
  a {
    text-decoration: none;
    color: #1677ff;
    
    &:hover {
      color: #4096ff;
    }
  }
  
  ul, ol {
    list-style: none;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: ${props => props.theme === THEME_MODES.DARK ? '#1f1f1f' : '#f1f1f1'};
  }
  
  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme === THEME_MODES.DARK ? '#434343' : '#c1c1c1'};
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme === THEME_MODES.DARK ? '#555555' : '#a8a8a8'};
  }
  
  /* 表单元素样式 */
  input, textarea, select, button {
    font-family: inherit;
    font-size: inherit;
  }
  
  /* 禁用状态样式 */
  button:disabled,
  input:disabled,
  textarea:disabled,
  select:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  /* 文本选择样式 */
  ::selection {
    background-color: #1677ff;
    color: #ffffff;
  }
  
  /* 响应式断点 */
  @media (max-width: 576px) {
    html {
      font-size: 13px;
    }
  }
  
  /* 动画过渡 */
  .fade-enter {
    opacity: 0;
  }
  
  .fade-enter-active {
    opacity: 1;
    transition: opacity 300ms;
  }
  
  .fade-exit {
    opacity: 1;
  }
  
  .fade-exit-active {
    opacity: 0;
    transition: opacity 300ms;
  }
  
  /* 页面过渡动画 */
  .page-transition-enter {
    opacity: 0;
    transform: translateY(20px);
  }
  
  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms, transform 300ms;
  }
  
  .page-transition-exit {
    opacity: 1;
    transform: translateY(0);
  }
  
  .page-transition-exit-active {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 300ms, transform 300ms;
  }
`;

export default GlobalStyles;