{"name": "ai-id-photo-backend", "version": "1.0.0", "description": "AI证件照生成平台后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["ai", "photo", "id-photo", "backend"], "author": "AI Photo Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "redis": "^4.6.7", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "uuid": "^9.0.0", "sharp": "^0.32.4", "axios": "^1.4.0", "dotenv": "^16.3.1", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "@types/jest": "^29.5.3"}, "engines": {"node": ">=16.0.0"}}