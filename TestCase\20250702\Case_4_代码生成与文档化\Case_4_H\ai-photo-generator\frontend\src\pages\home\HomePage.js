import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Typography, Row, Col, Card, Button, Empty, Spin } from 'antd';
import { PlusOutlined, HistoryOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { fetchRecentPhotos, selectRecentPhotos, selectPhotoLoading } from '../../store/photoSlice';
import { selectUser } from '../../store/authSlice';
import { ROUTES } from '../../utils/constants';
import PhotoCard from '../../components/photo/PhotoCard';
import FeatureCard from '../../components/home/<USER>';

const { Title, Paragraph } = Typography;

/**
 * 主页组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const HomePage = () => {
  const dispatch = useDispatch();
  const user = useSelector(selectUser);
  const recentPhotos = useSelector(selectRecentPhotos);
  const loading = useSelector(selectPhotoLoading);
  
  // 获取最近生成的照片
  useEffect(() => {
    if (user) {
      dispatch(fetchRecentPhotos());
    }
  }, [dispatch, user]);
  
  // 特性列表
  const features = [
    {
      title: '智能证件照生成',
      description: '使用AI技术，一键生成符合各种规格的证件照',
      icon: 'camera',
      link: ROUTES.CREATE_PHOTO,
    },
    {
      title: '多种证件照规格',
      description: '支持身份证、护照、驾照等多种证件照规格',
      icon: 'layout',
      link: ROUTES.PHOTO_SPECS,
    },
    {
      title: '背景颜色更换',
      description: '一键更换照片背景颜色，满足不同场景需求',
      icon: 'bg-colors',
      link: ROUTES.EDIT_PHOTO,
    },
    {
      title: '照片美化增强',
      description: '智能美化功能，让您的证件照更加专业',
      icon: 'beautify',
      link: ROUTES.EDIT_PHOTO,
    },
  ];
  
  return (
    <div className="home-page page-container">
      {/* 欢迎区域 */}
      <div className="welcome-section" style={{ marginBottom: 48 }}>
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={16}>
            <Title level={2}>欢迎使用AI证件照生成器</Title>
            <Paragraph>
              使用我们的AI技术，轻松生成专业证件照。支持多种规格，一键更换背景，智能美化，让您的证件照更加完美。
            </Paragraph>
            <div style={{ marginTop: 24 }}>
              <Button
                type="primary"
                size="large"
                icon={<PlusOutlined />}
                className="btn-primary"
                style={{ marginRight: 16 }}
              >
                <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
              </Button>
              <Button
                size="large"
                icon={<HistoryOutlined />}
              >
                <Link to={ROUTES.MY_PHOTOS}>我的照片</Link>
              </Button>
            </div>
          </Col>
          <Col xs={24} md={8}>
            <img
              src="/images/home-illustration.svg"
              alt="AI证件照生成器"
              style={{ width: '100%', maxWidth: 300, margin: '0 auto', display: 'block' }}
            />
          </Col>
        </Row>
      </div>
      
      {/* 特性区域 */}
      <div className="features-section" style={{ marginBottom: 48 }}>
        <Title level={3} style={{ marginBottom: 24 }}>我们的功能</Title>
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <FeatureCard
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                link={feature.link}
              />
            </Col>
          ))}
        </Row>
      </div>
      
      {/* 最近照片区域 */}
      {user && (
        <div className="recent-photos-section">
          <div className="section-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
            <Title level={3}>最近生成的照片</Title>
            <Link to={ROUTES.MY_PHOTOS}>查看全部</Link>
          </div>
          
          {loading ? (
            <div className="loading-container">
              <Spin size="large" />
            </div>
          ) : recentPhotos.length > 0 ? (
            <Row gutter={[24, 24]}>
              {recentPhotos.map((photo) => (
                <Col xs={24} sm={12} md={8} lg={6} key={photo.id}>
                  <PhotoCard photo={photo} />
                </Col>
              ))}
            </Row>
          ) : (
            <Card>
              <Empty
                description="暂无照片记录"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button type="primary">
                  <Link to={ROUTES.CREATE_PHOTO}>立即创建</Link>
                </Button>
              </Empty>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default HomePage;