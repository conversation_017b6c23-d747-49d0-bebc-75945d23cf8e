import React from 'react';
import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import { StyleSheetManager } from 'styled-components';
import App from './App';
import store from './store';
import reportWebVitals from './reportWebVitals';

/**
 * 应用入口
 * 
 * 将应用渲染到DOM中
 */
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <StyleSheetManager shouldForwardProp={(prop) => prop !== 'theme'}>
        <App />
      </StyleSheetManager>
    </Provider>
  </React.StrictMode>
);

// 性能测量
reportWebVitals();