# AI证件照生成平台环境配置文件
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# 基础配置
# ===========================================

# 运行环境 (development, production, test)
NODE_ENV=production

# 前端URL
FRONTEND_URL=http://localhost

# 端口配置
BACKEND_PORT=3000
FRONTEND_PORT=80
FRONTEND_HTTPS_PORT=443

# ===========================================
# 数据库配置
# ===========================================

# MySQL配置
MYSQL_ROOT_PASSWORD=your-strong-root-password
MYSQL_DATABASE=ai_photo_platform
MYSQL_USER=ai_photo_user
MYSQL_PASSWORD=your-strong-db-password
MYSQL_PORT=3306

# ===========================================
# Redis配置
# ===========================================

REDIS_PASSWORD=your-strong-redis-password
REDIS_PORT=6379

# ===========================================
# JWT认证配置
# ===========================================

# JWT密钥 (生产环境请使用强密钥)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-environment
JWT_EXPIRES_IN=7d

# JWT刷新令牌配置
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production-environment
JWT_REFRESH_EXPIRES_IN=30d

# ===========================================
# AI模型配置
# ===========================================

# 默认AI模型配置
DEFAULT_AI_ENDPOINT=https://api.example.com/generate
DEFAULT_AI_API_KEY=your-default-ai-api-key

# 自定义AI模型配置
CUSTOM_AI_ENDPOINT=https://custom-api.example.com/process
CUSTOM_AI_TOKEN=your-custom-ai-token

# 开发环境是否使用模拟AI (true/false)
USE_MOCK_AI=false

# ===========================================
# 文件上传配置
# ===========================================

# 最大文件大小 (字节，默认10MB)
MAX_FILE_SIZE=10485760

# 最大文件数量
MAX_FILES_COUNT=5

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# ===========================================
# 前端构建配置
# ===========================================

# API基础URL
VITE_API_BASE_URL=/api

# ===========================================
# Nginx配置
# ===========================================

NGINX_HOST=localhost
NGINX_PORT=80

# ===========================================
# 监控配置 (可选)
# ===========================================

# Grafana管理员密码
GRAFANA_PASSWORD=admin

# ===========================================
# 邮件配置 (可选)
# ===========================================

# SMTP服务器配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
FROM_EMAIL=<EMAIL>

# ===========================================
# 第三方服务配置 (可选)
# ===========================================

# 云存储配置 (如阿里云OSS、AWS S3等)
CLOUD_STORAGE_PROVIDER=local
CLOUD_STORAGE_BUCKET=ai-photo-bucket
CLOUD_STORAGE_REGION=us-east-1
CLOUD_STORAGE_ACCESS_KEY=your-access-key
CLOUD_STORAGE_SECRET_KEY=your-secret-key

# CDN配置
CDN_DOMAIN=https://cdn.example.com

# ===========================================
# 安全配置
# ===========================================

# CORS允许的域名 (多个域名用逗号分隔)
CORS_ORIGINS=http://localhost:3001,https://yourdomain.com

# 是否启用HTTPS重定向
FORCE_HTTPS=false

# 会话密钥
SESSION_SECRET=your-session-secret-key

# ===========================================
# 性能配置
# ===========================================

# 数据库连接池大小
DB_POOL_MAX=10
DB_POOL_MIN=0

# Redis连接池大小
REDIS_POOL_MAX=10

# 文件清理任务间隔 (小时)
CLEANUP_INTERVAL=24

# 任务保留时间 (天)
TASK_RETENTION_DAYS=30

# ===========================================
# 开发配置
# ===========================================

# 是否启用API文档
ENABLE_API_DOCS=true

# 是否启用调试模式
DEBUG_MODE=false

# 是否启用性能监控
ENABLE_MONITORING=false

# ===========================================
# 备份配置
# ===========================================

# 自动备份间隔 (小时)
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_PATH=/backups

# ===========================================
# 限流配置
# ===========================================

# API请求限制 (每分钟)
RATE_LIMIT_REQUESTS=100

# 上传限制 (每分钟)
UPLOAD_RATE_LIMIT=10

# 下载限制 (每分钟)
DOWNLOAD_RATE_LIMIT=50

# ===========================================
# 缓存配置
# ===========================================

# 缓存过期时间 (秒)
CACHE_TTL=3600

# 静态资源缓存时间 (秒)
STATIC_CACHE_TTL=86400

# ===========================================
# 通知配置 (可选)
# ===========================================

# 是否启用邮件通知
ENABLE_EMAIL_NOTIFICATIONS=false

# 是否启用短信通知
ENABLE_SMS_NOTIFICATIONS=false

# 短信服务配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key

# ===========================================
# 支付配置 (可选)
# ===========================================

# 支付宝配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY=your-alipay-private-key
ALIPAY_PUBLIC_KEY=your-alipay-public-key

# 微信支付配置
WECHAT_PAY_APP_ID=your-wechat-app-id
WECHAT_PAY_MCH_ID=your-wechat-mch-id
WECHAT_PAY_KEY=your-wechat-pay-key

# ===========================================
# 其他配置
# ===========================================

# 时区设置
TZ=Asia/Shanghai

# 语言设置
LOCALE=zh-CN

# 是否启用维护模式
MAINTENANCE_MODE=false

# 维护模式提示信息
MAINTENANCE_MESSAGE=系统正在维护中，请稍后再试
