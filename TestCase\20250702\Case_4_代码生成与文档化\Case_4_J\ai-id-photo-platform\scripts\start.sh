#!/bin/bash

# AI证件照生成平台启动脚本
# 用于快速启动开发或生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AI证件照生成平台启动脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev         启动开发环境"
    echo "  prod        启动生产环境"
    echo "  docker      使用Docker启动"
    echo "  docker-dev  使用Docker启动开发环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -v, --verbose  详细输出"
    echo "  -c, --clean    清理并重新安装依赖"
    echo "  --no-frontend  只启动后端服务"
    echo "  --no-backend   只启动前端服务"
    echo "  --monitoring   启动监控服务"
    echo ""
    echo "示例:"
    echo "  $0 dev                    # 启动开发环境"
    echo "  $0 prod                   # 启动生产环境"
    echo "  $0 docker                 # 使用Docker启动"
    echo "  $0 dev --clean            # 清理并启动开发环境"
    echo "  $0 docker --monitoring    # 启动Docker环境和监控"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 16.0+"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 16 ]; then
        log_error "Node.js 版本过低，需要 16.0+，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_info "Node.js 版本: $(node -v)"
    log_info "npm 版本: $(npm -v)"
}

# 检查Docker依赖
check_docker_dependencies() {
    log_info "检查Docker依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "Docker 版本: $(docker --version)"
    log_info "Docker Compose 版本: $(docker-compose --version)"
}

# 安装依赖
install_dependencies() {
    if [ "$CLEAN_INSTALL" = true ]; then
        log_info "清理现有依赖..."
        rm -rf backend/node_modules frontend/node_modules
        rm -f backend/package-lock.json frontend/package-lock.json
    fi
    
    log_info "安装后端依赖..."
    cd backend
    npm install
    cd ..
    
    if [ "$NO_FRONTEND" != true ]; then
        log_info "安装前端依赖..."
        cd frontend
        npm install
        cd ..
    fi
}

# 检查环境配置
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        log_warn ".env 文件不存在，从 .env.example 复制..."
        cp .env.example .env
        log_warn "请编辑 .env 文件配置相关参数"
    fi
    
    if [ ! -f "backend/.env" ]; then
        log_warn "backend/.env 文件不存在，从 backend/.env.example 复制..."
        cp backend/.env.example backend/.env
    fi
    
    if [ "$NO_FRONTEND" != true ] && [ ! -f "frontend/.env.development" ] && [ "$ENVIRONMENT" = "dev" ]; then
        log_info "创建前端开发环境配置..."
        echo "VITE_API_BASE_URL=http://localhost:3000/api" > frontend/.env.development
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # 例如使用mysql命令测试连接
    
    log_info "数据库连接检查完成"
}

# 启动开发环境
start_development() {
    log_info "启动开发环境..."
    
    check_dependencies
    check_environment
    install_dependencies
    
    # 创建必要目录
    mkdir -p backend/uploads/temp backend/uploads/original backend/uploads/generated backend/logs
    
    log_info "启动服务..."
    
    if [ "$NO_BACKEND" != true ]; then
        log_info "启动后端服务 (端口: 3000)..."
        cd backend
        npm run dev &
        BACKEND_PID=$!
        cd ..
    fi
    
    if [ "$NO_FRONTEND" != true ]; then
        log_info "启动前端服务 (端口: 3001)..."
        cd frontend
        npm run dev &
        FRONTEND_PID=$!
        cd ..
    fi
    
    # 等待服务启动
    sleep 3
    
    log_info "服务启动完成!"
    log_info "前端地址: http://localhost:3001"
    log_info "后端地址: http://localhost:3000"
    log_info "API文档: http://localhost:3000/api-docs"
    
    # 等待用户中断
    wait
}

# 启动生产环境
start_production() {
    log_info "启动生产环境..."
    
    check_dependencies
    check_environment
    install_dependencies
    
    # 构建前端
    if [ "$NO_FRONTEND" != true ]; then
        log_info "构建前端应用..."
        cd frontend
        npm run build
        cd ..
    fi
    
    # 创建必要目录
    mkdir -p backend/uploads/temp backend/uploads/original backend/uploads/generated backend/logs
    
    # 启动后端服务
    if [ "$NO_BACKEND" != true ]; then
        log_info "启动后端服务..."
        cd backend
        
        # 使用PM2管理进程
        if command -v pm2 &> /dev/null; then
            pm2 start src/app.js --name "ai-photo-backend" --env production
            pm2 save
        else
            npm start &
        fi
        cd ..
    fi
    
    log_info "生产环境启动完成!"
}

# 启动Docker环境
start_docker() {
    log_info "启动Docker环境..."
    
    check_docker_dependencies
    check_environment
    
    # 构建并启动服务
    local compose_args=""
    if [ "$MONITORING" = true ]; then
        compose_args="--profile monitoring"
    fi
    
    log_info "构建Docker镜像..."
    docker-compose build
    
    log_info "启动Docker服务..."
    docker-compose up -d $compose_args
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    docker-compose ps
    
    log_info "Docker环境启动完成!"
    log_info "前端地址: http://localhost"
    log_info "后端地址: http://localhost:3000"
    
    if [ "$MONITORING" = true ]; then
        log_info "Prometheus: http://localhost:9090"
        log_info "Grafana: http://localhost:3001 (admin/admin)"
    fi
}

# 启动Docker开发环境
start_docker_dev() {
    log_info "启动Docker开发环境..."
    
    check_docker_dependencies
    
    # 使用开发配置
    export NODE_ENV=development
    export USE_MOCK_AI=true
    
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    
    log_info "Docker开发环境启动完成!"
}

# 清理函数
cleanup() {
    log_info "正在清理..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    log_info "清理完成"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 解析命令行参数
ENVIRONMENT=""
VERBOSE=false
CLEAN_INSTALL=false
NO_FRONTEND=false
NO_BACKEND=false
MONITORING=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clean)
            CLEAN_INSTALL=true
            shift
            ;;
        --no-frontend)
            NO_FRONTEND=true
            shift
            ;;
        --no-backend)
            NO_BACKEND=true
            shift
            ;;
        --monitoring)
            MONITORING=true
            shift
            ;;
        dev|prod|docker|docker-dev)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 如果没有指定环境，默认为开发环境
if [ -z "$ENVIRONMENT" ]; then
    ENVIRONMENT="dev"
fi

# 设置详细输出
if [ "$VERBOSE" = true ]; then
    set -x
fi

# 显示启动信息
log_info "AI证件照生成平台启动脚本"
log_info "环境: $ENVIRONMENT"
log_info "工作目录: $(pwd)"

# 根据环境启动相应服务
case $ENVIRONMENT in
    dev)
        start_development
        ;;
    prod)
        start_production
        ;;
    docker)
        start_docker
        ;;
    docker-dev)
        start_docker_dev
        ;;
    *)
        log_error "未知环境: $ENVIRONMENT"
        show_help
        exit 1
        ;;
esac
