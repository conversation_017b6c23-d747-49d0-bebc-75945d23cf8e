.results-page {
  .processing-container {
    text-align: center;
    padding: 60px 0;
    
    .processing-title {
      margin: 24px 0;
    }
    
    .ant-progress {
      width: 80%;
      max-width: 400px;
      margin: 0 auto 24px;
    }
    
    .processing-tip {
      color: rgba(0, 0, 0, 0.45);
    }
  }
  
  .loading-container {
    text-align: center;
    padding: 60px 0;
  }
  
  .error-container {
    text-align: center;
    padding: 60px 0;
    
    .error-actions {
      margin-top: 24px;
    }
  }
  
  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      
      .results-actions {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
    }
  }
  
  .photo-grid {
    margin-bottom: 24px;
    
    .photo-card {
      position: relative;
      overflow: hidden;
      
      &.selected {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
      
      .card-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        display: flex;
        gap: 8px;
        z-index: 10;
        
        .ant-checkbox-wrapper {
          background-color: rgba(255, 255, 255, 0.8);
          padding: 4px;
          border-radius: 4px;
        }
        
        .ant-btn-link {
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 4px;
          height: 32px;
          width: 32px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      
      .quality-score {
        margin-top: 8px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  
  .bottom-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
  }
} 