const express = require('express');
const router = express.Router();
const { getUser, updateUser, getUserHistory } = require('../controllers/userController');
const { auth } = require('../middleware/authMiddleware');

// 所有用户路由都需要身份验证
router.use(auth);

// 获取用户信息
router.get('/info', getUser);

// 更新用户信息
router.put('/update', updateUser);

// 获取使用历史
router.get('/history', getUserHistory);

module.exports = router; 