const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const GeneratedPhoto = sequelize.define('GeneratedPhoto', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  taskId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    field: 'task_id',
    references: {
      model: 'generation_tasks',
      key: 'id'
    }
  },
  filePath: {
    type: DataTypes.STRING(500),
    allowNull: false,
    field: 'file_path'
  },
  qualityScore: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    field: 'quality_score',
    validate: {
      min: 0,
      max: 1
    }
  },
  fileSize: {
    type: DataTypes.INTEGER,
    allowNull: false,
    field: 'file_size'
  },
  width: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  height: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  format: {
    type: DataTypes.STRING(10),
    allowNull: false,
    defaultValue: 'jpeg'
  },
  downloadCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'download_count'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    field: 'created_at'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '生成照片的元数据信息'
  }
}, {
  tableName: 'generated_photos',
  timestamps: false
});

module.exports = GeneratedPhoto;
