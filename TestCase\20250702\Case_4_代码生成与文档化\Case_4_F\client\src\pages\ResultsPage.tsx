import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Typography, 
  Card, 
  Button, 
  Row, 
  Col, 
  Spin, 
  Progress, 
  Checkbox, 
  message, 
  Modal,
  Empty
} from 'antd';
import { 
  DownloadOutlined, 
  ReloadOutlined, 
  EyeOutlined, 
  ArrowLeftOutlined 
} from '@ant-design/icons';
import { RootState } from '../store';
import { photoAPI } from '../api';
import { PhotoActionTypes } from '../store/reducers/photoReducer';
import { GeneratedPhoto } from '../store/reducers/photoReducer';
import './ResultsPage.scss';

const { Title, Paragraph } = Typography;

const ResultsPage: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const dispatch = useDispatch();
  
  // 从Redux获取状态
  const { status, progress, generatedPhotos, selectedPhotoIds, error } = 
    useSelector((state: RootState) => state.photo);
  
  // 本地状态
  const [isPolling, setIsPolling] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  // 轮询任务状态
  useEffect(() => {
    if (!taskId) return;
    
    // 如果当前没有任务ID或者与URL中的不匹配，则设置任务ID
    if (!status || status === 'idle') {
      dispatch({ type: PhotoActionTypes.GENERATE_START, payload: taskId });
    }
    
    // 如果任务还在处理中，开始轮询
    if (status === 'processing' && !isPolling) {
      setIsPolling(true);
      const pollInterval = setInterval(async () => {
        try {
          const response = await photoAPI.getTaskStatus(taskId);
          const { status: taskStatus, progress: taskProgress, generated_photos } = response.data.data;
          
          // 更新进度
          dispatch({ 
            type: PhotoActionTypes.GENERATE_PROGRESS, 
            payload: taskProgress 
          });
          
          // 如果任务完成，更新状态并停止轮询
          if (taskStatus === 'completed') {
            dispatch({
              type: PhotoActionTypes.GENERATE_SUCCESS,
              payload: {
                generatedPhotos: generated_photos.map((photo: any) => ({
                  id: photo.id,
                  url: photo.url,
                  qualityScore: photo.quality_score
                }))
              }
            });
            clearInterval(pollInterval);
            setIsPolling(false);
          } else if (taskStatus === 'failed') {
            dispatch({
              type: PhotoActionTypes.GENERATE_FAILURE,
              payload: '生成失败，请重试'
            });
            clearInterval(pollInterval);
            setIsPolling(false);
          }
        } catch (error) {
          console.error('Error polling task status:', error);
          dispatch({
            type: PhotoActionTypes.GENERATE_FAILURE,
            payload: '获取任务状态失败'
          });
          clearInterval(pollInterval);
          setIsPolling(false);
        }
      }, 2000); // 每2秒轮询一次
      
      return () => clearInterval(pollInterval);
    }
  }, [taskId, status, isPolling, dispatch]);
  
  // 处理全选
  const handleSelectAll = () => {
    if (selectedPhotoIds.length === generatedPhotos.length) {
      dispatch({ type: PhotoActionTypes.DESELECT_ALL_PHOTOS });
    } else {
      dispatch({ type: PhotoActionTypes.SELECT_ALL_PHOTOS });
    }
  };
  
  // 处理单选
  const handleSelectPhoto = (photoId: string) => {
    if (selectedPhotoIds.includes(photoId)) {
      dispatch({ type: PhotoActionTypes.DESELECT_PHOTO, payload: photoId });
    } else {
      dispatch({ type: PhotoActionTypes.SELECT_PHOTO, payload: photoId });
    }
  };
  
  // 处理预览
  const handlePreview = (photo: GeneratedPhoto) => {
    setPreviewImage(photo.url);
    setPreviewVisible(true);
  };
  
  // 处理下载
  const handleDownload = async () => {
    if (selectedPhotoIds.length === 0) {
      message.warning('请至少选择一张照片');
      return;
    }
    
    setDownloadLoading(true);
    
    try {
      const response = await photoAPI.downloadPhotos(selectedPhotoIds);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'photos.zip');
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      message.success('下载成功');
    } catch (error) {
      console.error('Download error:', error);
      message.error('下载失败，请重试');
    } finally {
      setDownloadLoading(false);
    }
  };
  
  // 渲染不同的状态
  const renderContent = () => {
    if (status === 'processing') {
      return (
        <div className="processing-container">
          <Spin size="large" />
          <Title level={3} className="processing-title">正在生成您的证件照</Title>
          <Progress percent={progress} status="active" />
          <Paragraph className="processing-tip">
            请耐心等待，生成过程需要一些时间...
          </Paragraph>
        </div>
      );
    } else if (status === 'completed' && generatedPhotos.length > 0) {
      return (
        <>
          <div className="results-header">
            <div className="results-title">
              <Title level={2}>生成结果</Title>
              <Paragraph>
                我们已为您生成 {generatedPhotos.length} 张证件照，请选择您喜欢的照片进行下载
              </Paragraph>
            </div>
            <div className="results-actions">
              <Checkbox
                checked={selectedPhotoIds.length === generatedPhotos.length && generatedPhotos.length > 0}
                indeterminate={selectedPhotoIds.length > 0 && selectedPhotoIds.length < generatedPhotos.length}
                onChange={handleSelectAll}
              >
                全选
              </Checkbox>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                loading={downloadLoading}
                onClick={handleDownload}
                disabled={selectedPhotoIds.length === 0}
              >
                下载选中照片
              </Button>
            </div>
          </div>
          
          <Row gutter={[16, 16]} className="photo-grid">
            {generatedPhotos.map((photo) => (
              <Col xs={12} sm={8} md={6} key={photo.id}>
                <Card
                  hoverable
                  className={`photo-card ${selectedPhotoIds.includes(photo.id) ? 'selected' : ''}`}
                  cover={<img alt="证件照" src={photo.url} />}
                  onClick={() => handleSelectPhoto(photo.id)}
                >
                  <div className="card-overlay">
                    <Checkbox checked={selectedPhotoIds.includes(photo.id)} />
                    <Button 
                      type="link" 
                      icon={<EyeOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePreview(photo);
                      }}
                    />
                  </div>
                  <div className="quality-score">
                    质量评分: {(photo.qualityScore * 100).toFixed(0)}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
          
          <div className="bottom-actions">
            <Link to="/upload">
              <Button icon={<ArrowLeftOutlined />}>返回上传</Button>
            </Link>
            <Button icon={<ReloadOutlined />}>重新生成</Button>
          </div>
          
          <Modal
            visible={previewVisible}
            title="照片预览"
            footer={null}
            onCancel={() => setPreviewVisible(false)}
            width={600}
          >
            <img alt="证件照预览" src={previewImage} style={{ width: '100%' }} />
          </Modal>
        </>
      );
    } else if (status === 'failed') {
      return (
        <div className="error-container">
          <Empty
            description={error || '生成失败，请重试'}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
          <div className="error-actions">
            <Link to="/upload">
              <Button type="primary">返回重试</Button>
            </Link>
          </div>
        </div>
      );
    } else {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <Paragraph>加载中...</Paragraph>
        </div>
      );
    }
  };
  
  return (
    <div className="results-page">
      {renderContent()}
    </div>
  );
};

export default ResultsPage; 