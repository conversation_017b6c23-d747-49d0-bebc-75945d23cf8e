import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Switch, Tooltip } from 'antd';
import { BulbOutlined, BulbFilled } from '@ant-design/icons';
import styled from 'styled-components';
import { toggleTheme } from '../../store/uiSlice';
import { THEME_MODES } from '../../utils/constants';

/**
 * 样式化组件
 */
const StyledSwitch = styled(Switch)`
  margin: 0 8px;
  background-color: ${({ checked, theme }) => 
    checked ? theme.colorPrimary : 'rgba(0, 0, 0, 0.25)'};
`;

/**
 * 主题切换开关组件
 * 
 * 用于切换应用的主题模式（亮色/暗色）
 * 
 * @returns {JSX.Element} 主题切换开关组件
 */
const ThemeSwitch = () => {
  const dispatch = useDispatch();
  
  // 从Redux获取当前主题
  const { currentTheme } = useSelector((state) => state.ui);
  
  // 判断是否为暗色主题
  const isDarkMode = currentTheme === THEME_MODES.DARK;
  
  // 切换主题
  const handleToggleTheme = () => {
    dispatch(toggleTheme());
  };
  
  return (
    <Tooltip title={isDarkMode ? '切换到亮色模式' : '切换到暗色模式'}>
      <StyledSwitch
        checked={isDarkMode}
        onChange={handleToggleTheme}
        checkedChildren={<BulbFilled />}
        unCheckedChildren={<BulbOutlined />}
      />
    </Tooltip>
  );
};

export default ThemeSwitch;