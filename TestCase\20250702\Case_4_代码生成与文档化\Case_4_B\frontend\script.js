document.getElementById('generate-btn').addEventListener('click', async () => {
    const fileInput = document.getElementById('photo-upload');
    const file = fileInput.files[0];
    if (!file) {
        alert('请先上传照片');
        return;
    }

    const formData = new FormData();
    formData.append('photo', file);

    try {
        const response = await fetch('/api/generate', {
            method: 'POST',
            body: formData
        });
        const result = await response.json();
        document.getElementById('result').innerHTML = `<img src="${result.url}" alt="证件照">`;
    } catch (error) {
        console.error('生成失败:', error);
        alert('生成失败，请重试');
    }
});