const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { getDefaultModel, getModelByName } = require('../../config/aiModels');
const logger = require('../../utils/logger');

class AIService {
  constructor() {
    this.defaultModel = getDefaultModel();
  }

  /**
   * 生成证件照
   * @param {Object} params 生成参数
   * @param {string} params.imagePath 原始图片路径
   * @param {string} params.spec 证件照规格
   * @param {string} params.background 背景颜色
   * @param {number} params.count 生成数量
   * @param {Object} params.modelConfig 模型配置
   * @returns {Promise<Array>} 生成的照片信息
   */
  async generateIDPhoto(params) {
    const {
      imagePath,
      spec,
      background,
      count = 5,
      modelConfig = {}
    } = params;

    try {
      // 选择使用的模型
      const model = modelConfig.modelName 
        ? getModelByName(modelConfig.modelName) 
        : this.defaultModel;

      if (!model) {
        throw new Error('指定的AI模型不存在');
      }

      logger.info(`使用模型 ${model.name} 生成证件照`, {
        spec,
        background,
        count,
        imagePath
      });

      // 根据模型类型调用不同的生成方法
      if (model.name === 'default_model') {
        return await this._generateWithDefaultModel(model, params);
      } else {
        return await this._generateWithCustomModel(model, params);
      }
    } catch (error) {
      logger.error('AI证件照生成失败', {
        error: error.message,
        params
      });
      throw error;
    }
  }

  /**
   * 使用默认模型生成证件照
   * @private
   */
  async _generateWithDefaultModel(model, params) {
    const { imagePath, spec, background, count, modelConfig } = params;

    // 创建表单数据
    const formData = new FormData();
    formData.append('image', fs.createReadStream(imagePath));
    formData.append('spec', spec);
    formData.append('background', background);
    formData.append('count', count.toString());
    
    if (modelConfig.parameters) {
      formData.append('parameters', JSON.stringify(modelConfig.parameters));
    }

    try {
      const response = await axios.post(model.endpoint, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${model.apiKey}`
        },
        timeout: model.timeout,
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });

      return this._processGenerationResponse(response.data);
    } catch (error) {
      if (error.response) {
        throw new Error(`AI服务响应错误: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
      } else if (error.request) {
        throw new Error('AI服务无响应，请检查网络连接');
      } else {
        throw new Error(`请求配置错误: ${error.message}`);
      }
    }
  }

  /**
   * 使用自定义模型生成证件照
   * @private
   */
  async _generateWithCustomModel(model, params) {
    const { imagePath, spec, background, count, modelConfig } = params;

    // 读取图片文件并转换为base64
    const imageBuffer = fs.readFileSync(imagePath);
    const imageBase64 = imageBuffer.toString('base64');

    const requestData = {
      image: imageBase64,
      spec: spec,
      background: background,
      count: count,
      ...modelConfig.parameters
    };

    try {
      const response = await axios.post(model.endpoint, requestData, {
        headers: model.headers,
        timeout: model.timeout
      });

      return this._processGenerationResponse(response.data);
    } catch (error) {
      if (error.response) {
        throw new Error(`自定义AI服务响应错误: ${error.response.status} - ${error.response.data?.message || '未知错误'}`);
      } else if (error.request) {
        throw new Error('自定义AI服务无响应，请检查网络连接');
      } else {
        throw new Error(`请求配置错误: ${error.message}`);
      }
    }
  }

  /**
   * 处理AI服务响应
   * @private
   */
  _processGenerationResponse(responseData) {
    // 这里需要根据实际AI服务的响应格式进行调整
    if (responseData.success && responseData.data) {
      return responseData.data.map((item, index) => ({
        id: item.id || `generated_${Date.now()}_${index}`,
        url: item.url,
        qualityScore: item.quality_score || Math.random() * 0.3 + 0.7, // 模拟质量分数
        metadata: item.metadata || {}
      }));
    } else {
      throw new Error(responseData.message || 'AI服务返回格式错误');
    }
  }

  /**
   * 模拟AI生成（用于开发测试）
   * @param {Object} params 生成参数
   * @returns {Promise<Array>} 模拟生成的照片信息
   */
  async mockGenerate(params) {
    const { count = 5 } = params;
    
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    // 模拟生成结果
    const results = [];
    for (let i = 0; i < count; i++) {
      results.push({
        id: `mock_${Date.now()}_${i}`,
        url: `https://picsum.photos/295/413?random=${Date.now()}_${i}`,
        qualityScore: Math.random() * 0.3 + 0.7,
        metadata: {
          generated_at: new Date().toISOString(),
          model: 'mock_model',
          version: '1.0.0'
        }
      });
    }

    return results;
  }

  /**
   * 健康检查
   * @param {string} modelName 模型名称
   * @returns {Promise<boolean>} 健康状态
   */
  async healthCheck(modelName = null) {
    const model = modelName ? getModelByName(modelName) : this.defaultModel;
    
    if (!model) {
      return false;
    }

    try {
      const response = await axios.get(`${model.endpoint}/health`, {
        headers: model.headers,
        timeout: 5000
      });
      
      return response.status === 200;
    } catch (error) {
      logger.warn(`模型 ${model.name} 健康检查失败`, { error: error.message });
      return false;
    }
  }
}

module.exports = new AIService();
