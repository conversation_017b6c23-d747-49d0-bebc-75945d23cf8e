const path = require('path');
const fs = require('fs');
const PhotoTask = require('../models/PhotoTask');

// @desc    上传单张照片
// @route   POST /api/upload/single
// @access  Private
exports.uploadSinglePhoto = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传照片'
      });
    }

    const { specification } = req.body;
    if (!specification) {
      return res.status(400).json({
        success: false,
        message: '请选择证件照规格'
      });
    }

    // 创建照片任务
    const photoTask = await PhotoTask.create({
      user: req.user._id,
      originalPhotos: [{
        path: req.file.path,
        filename: req.file.filename
      }],
      specification,
      status: 'pending',
      creditsUsed: 1 // 单张照片消耗1积分
    });

    // 扣除用户积分
    await req.user.decreaseCredits(1);

    res.status(201).json({
      success: true,
      taskId: photoTask._id,
      message: '照片上传成功，开始处理'
    });

    // 异步处理照片（实际项目中应该调用AI服务或队列）
    processPhotoTask(photoTask._id);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    批量上传照片
// @route   POST /api/upload/batch
// @access  Private
exports.uploadBatchPhotos = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请上传至少一张照片'
      });
    }

    const { specification } = req.body;
    if (!specification) {
      return res.status(400).json({
        success: false,
        message: '请选择证件照规格'
      });
    }

    // 限制最大上传数量
    const maxPhotos = 5;
    if (req.files.length > maxPhotos) {
      return res.status(400).json({
        success: false,
        message: `最多只能上传${maxPhotos}张照片`
      });
    }

    // 创建照片任务
    const originalPhotos = req.files.map(file => ({
      path: file.path,
      filename: file.filename
    }));

    const photoTask = await PhotoTask.create({
      user: req.user._id,
      originalPhotos,
      specification,
      status: 'pending',
      creditsUsed: req.files.length // 每张照片消耗1积分
    });

    // 扣除用户积分
    await req.user.decreaseCredits(req.files.length);

    res.status(201).json({
      success: true,
      taskId: photoTask._id,
      message: '照片上传成功，开始处理',
      uploadedCount: req.files.length
    });

    // 异步处理照片（实际项目中应该调用AI服务或队列）
    processPhotoTask(photoTask._id);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 模拟处理照片任务（实际项目中应该调用AI服务）
async function processPhotoTask(taskId) {
  try {
    // 获取任务
    const task = await PhotoTask.findById(taskId);
    if (!task) {
      console.error(`任务不存在: ${taskId}`);
      return;
    }

    // 更新状态为处理中
    await task.updateStatus('processing');

    // 模拟处理时间（实际项目中应该调用AI服务）
    const processingTime = Math.floor(Math.random() * 3000) + 2000; // 2-5秒
    
    setTimeout(async () => {
      try {
        // 为每张原始照片生成一张证件照
        for (let i = 0; i < task.originalPhotos.length; i++) {
          const originalPhoto = task.originalPhotos[i];
          
          // 模拟生成证件照（实际项目中应该调用AI服务）
          const generatedFilename = `generated_${Date.now()}_${i}.jpg`;
          const generatedPath = path.join(
            path.dirname(originalPhoto.path),
            generatedFilename
          );
          
          // 这里只是复制原图，实际项目中应该调用AI服务生成证件照
          fs.copyFileSync(originalPhoto.path, generatedPath);
          
          // 添加生成的照片到任务中
          await task.addGeneratedPhoto({
            path: generatedPath,
            filename: generatedFilename,
            originalPhotoIndex: i,
            qualityScore: Math.floor(Math.random() * 30) + 70 // 70-100的随机分数
          });
        }
        
        // 更新任务状态为完成
        await task.updateStatus('completed');
        
        console.log(`任务完成: ${taskId}`);
      } catch (error) {
        console.error(`处理任务失败: ${taskId}`, error);
        await task.updateStatus('failed', error.message);
      }
    }, processingTime);
    
  } catch (error) {
    console.error(`处理任务失败: ${taskId}`, error);
    const task = await PhotoTask.findById(taskId);
    if (task) {
      await task.updateStatus('failed', error.message);
    }
  }
}