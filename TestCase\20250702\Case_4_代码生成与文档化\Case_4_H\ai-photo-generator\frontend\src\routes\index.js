import React, { lazy, Suspense } from 'react';
import { Navigate, useRoutes } from 'react-router-dom';
import { Spin } from 'antd';
import { ROUTES } from '../utils/constants';

// 布局
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';

// 路由守卫
import PrivateRoute from './PrivateRoute';
import PublicRoute from './PublicRoute';

// 懒加载组件
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Login = lazy(() => import('../pages/auth/Login'));
const Register = lazy(() => import('../pages/auth/Register'));
const ForgotPassword = lazy(() => import('../pages/auth/ForgotPassword'));
const ResetPassword = lazy(() => import('../pages/auth/ResetPassword'));
const PhotoList = lazy(() => import('../pages/photos/PhotoList'));
const PhotoDetail = lazy(() => import('../pages/photos/PhotoDetail'));
const CreatePhoto = lazy(() => import('../pages/photos/CreatePhoto'));
const EditPhoto = lazy(() => import('../pages/photos/EditPhoto'));
const PhotoResult = lazy(() => import('../pages/photos/PhotoResult'));
const Favorites = lazy(() => import('../pages/photos/Favorites'));
const Subscription = lazy(() => import('../pages/subscription/Subscription'));
const Payment = lazy(() => import('../pages/subscription/Payment'));
const Profile = lazy(() => import('../pages/user/Profile'));
const Settings = lazy(() => import('../pages/user/Settings'));
const Help = lazy(() => import('../pages/Help'));
const Privacy = lazy(() => import('../pages/Privacy'));
const Terms = lazy(() => import('../pages/Terms'));
const NotFound = lazy(() => import('../pages/NotFound'));

/**
 * 加载中组件
 * 
 * 用于Suspense fallback
 */
const LoadingComponent = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh' 
  }}>
    <Spin size="large" tip="加载中..." />
  </div>
);

/**
 * 路由配置
 * 
 * @returns {JSX.Element} 路由组件
 */
const Router = () => {
  return useRoutes([
    {
      path: ROUTES.HOME,
      element: (
        <PublicRoute>
          <AuthLayout />
        </PublicRoute>
      ),
      children: [
        { path: '', element: <Navigate to={ROUTES.LOGIN} replace /> },
        { 
          path: ROUTES.LOGIN, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Login />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.REGISTER, 
          element: (
            <Suspense fallback={<LoadingComponent />}></Suspense>
              <Register />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.FORGOT_PASSWORD, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ForgotPassword />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.RESET_PASSWORD, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ResetPassword />
            </Suspense>
          ) 
        },
      ],
    },
    {
      path: '/',
      element: (
        <PrivateRoute>
          <MainLayout />
        </PrivateRoute>
      ),
      children: [
        { path: '', element: <Navigate to={ROUTES.DASHBOARD} replace /> },
        { 
          path: ROUTES.DASHBOARD, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Dashboard />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PHOTOS, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <PhotoList />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PHOTO_DETAIL, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <PhotoDetail />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.CREATE_PHOTO, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <CreatePhoto />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.EDIT_PHOTO, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <EditPhoto />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PHOTO_RESULT, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <PhotoResult />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.FAVORITES, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Favorites />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.SUBSCRIPTION, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Subscription />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PAYMENT, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Payment />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PROFILE, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Profile />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.SETTINGS, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Settings />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.HELP, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Help />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.PRIVACY, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Privacy />
            </Suspense>
          ) 
        },
        { 
          path: ROUTES.TERMS, 
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Terms />
            </Suspense>
          ) 
        },
      ],
    },
    { 
      path: ROUTES.NOT_FOUND, 
      element: (
        <Suspense fallback={<LoadingComponent />}>
          <NotFound />
        </Suspense>
      ) 
    },
    { path: '*', element: <Navigate to={ROUTES.NOT_FOUND} replace /> },
  ]);
};

export default Router;