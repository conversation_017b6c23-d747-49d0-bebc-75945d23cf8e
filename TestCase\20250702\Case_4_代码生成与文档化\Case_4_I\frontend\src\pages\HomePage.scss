.home-page {
  .hero-section {
    background-color: #f0f7ff;
    padding: 60px 0;
    
    .hero-content {
      h1 {
        font-size: 36px;
        margin-bottom: 16px;
      }
      
      .hero-description {
        font-size: 16px;
        margin-bottom: 24px;
      }
    }
    
    .hero-image {
      text-align: center;
      
      img {
        max-width: 100%;
        height: auto;
      }
    }
  }
  
  .section {
    padding: 60px 0;
    
    .section-title {
      margin-bottom: 40px;
    }
  }
  
  .steps-section {
    background-color: #fff;
  }
  
  .features-section {
    background-color: #f5f5f5;
    
    .feature-card {
      height: 100%;
      text-align: center;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .feature-icon {
        margin-bottom: 16px;
      }
    }
  }
  
  .cases-section {
    background-color: #fff;
    
    .case-card {
      padding: 24px;
      margin: 0 auto;
      max-width: 600px;
      
      .case-comment {
        font-size: 16px;
        font-style: italic;
        margin-bottom: 16px;
      }
      
      .case-info {
        display: flex;
        justify-content: space-between;
        
        .case-name {
          font-weight: bold;
        }
        
        .case-usage {
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }
  
  .cta-section {
    background-color: #e6f7ff;
    
    .cta-description {
      font-size: 16px;
      margin-bottom: 24px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .home-page {
    .hero-section {
      padding: 40px 0;
      
      .hero-content {
        h1 {
          font-size: 28px;
        }
      }
    }
    
    .section {
      padding: 40px 0;
    }
  }
}