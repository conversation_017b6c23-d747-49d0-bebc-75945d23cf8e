const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const Upload = require('./Upload');

const GenerationTask = sequelize.define('GenerationTask', {
  id: {
    type: DataTypes.STRING(50),
    primaryKey: true
  },
  uploadId: {
    type: DataTypes.STRING(50),
    allowNull: false,
    references: {
      model: Upload,
      key: 'id'
    }
  },
  spec: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  background: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    defaultValue: 'pending'
  },
  progress: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'generation_tasks',
  indexes: [
    {
      fields: ['uploadId']
    },
    {
      fields: ['status']
    }
  ]
});

// 关联关系
GenerationTask.belongsTo(Upload, { foreignKey: 'uploadId', as: 'upload' });
Upload.hasMany(GenerationTask, { foreignKey: 'uploadId', as: 'tasks' });

module.exports = GenerationTask; 