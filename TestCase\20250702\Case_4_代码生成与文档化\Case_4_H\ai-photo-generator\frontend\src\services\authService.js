import axios from 'axios';
import { API_ENDPOINTS, STORAGE_KEYS } from '../utils/constants';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // 如果是401错误且不是刷新token的请求，尝试刷新token
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      originalRequest.url !== API_ENDPOINTS.REFRESH_TOKEN
    ) {
      originalRequest._retry = true;
      
      try {
        // 尝试刷新token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (!refreshToken) {
          // 如果没有刷新token，直接登出
          authService.logout();
          return Promise.reject(error);
        }
        
        const response = await api.post(API_ENDPOINTS.REFRESH_TOKEN, {
          refreshToken,
        });
        
        // 保存新token
        const { token } = response.data;
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        
        // 使用新token重试原请求
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return api(originalRequest);
      } catch (refreshError) {
        // 刷新token失败，登出
        authService.logout();
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

/**
 * 认证服务
 * 处理用户登录、注册、登出等认证相关操作
 */
const authService = {
  /**
   * 用户登录
   * @param {string} email - 用户邮箱
   * @param {string} password - 用户密码
   * @returns {Promise} 包含用户信息和token的Promise
   */
  login: async (email, password) => {
    const response = await api.post(API_ENDPOINTS.LOGIN, { email, password });
    const { user, token, refreshToken } = response.data;
    
    // 保存token和用户信息到本地存储
    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    
    return { user, token };
  },
  
  /**
   * 用户注册
   * @param {Object} userData - 用户注册数据
   * @param {string} userData.name - 用户名
   * @param {string} userData.email - 用户邮箱
   * @param {string} userData.password - 用户密码
   * @returns {Promise} 包含用户信息和token的Promise
   */
  register: async (userData) => {
    const response = await api.post(API_ENDPOINTS.REGISTER, userData);
    const { user, token, refreshToken } = response.data;
    
    // 保存token和用户信息到本地存储
    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    
    return { user, token };
  },
  
  /**
   * 用户登出
   * @returns {Promise} 登出结果的Promise
   */
  logout: async () => {
    try {
      // 调用登出API
      await api.post(API_ENDPOINTS.LOGOUT);
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // 无论API调用是否成功，都清除本地存储
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
    }
    
    return { success: true };
  },
  
  /**
   * 获取当前登录用户信息
   * @returns {Promise} 包含用户信息的Promise
   */
  getCurrentUser: async () => {
    const response = await api.get(API_ENDPOINTS.CURRENT_USER);
    const { user } = response.data;
    
    // 更新本地存储中的用户信息
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    
    return { user };
  },
  
  /**
   * 更新用户资料
   * @param {Object} profileData - 用户资料数据
   * @returns {Promise} 包含更新后的用户信息的Promise
   */
  updateProfile: async (profileData) => {
    const response = await api.put(API_ENDPOINTS.UPDATE_PROFILE, profileData);
    const { user } = response.data;
    
    // 更新本地存储中的用户信息
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    
    return { user };
  },
  
  /**
   * 修改密码
   * @param {string} currentPassword - 当前密码
   * @param {string} newPassword - 新密码
   * @returns {Promise} 修改结果的Promise
   */
  changePassword: async (currentPassword, newPassword) => {
    const response = await api.put(API_ENDPOINTS.CHANGE_PASSWORD, {
      currentPassword,
      newPassword,
    });
    
    return response.data;
  },
  
  /**
   * 从本地存储获取当前用户
   * @returns {Object|null} 用户对象或null
   */
  getStoredUser: () => {
    const userStr = localStorage.getItem(STORAGE_KEYS.USER);
    return userStr ? JSON.parse(userStr) : null;
  },
  
  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn: () => {
    return !!localStorage.getItem(STORAGE_KEYS.TOKEN);
  },
};

export default authService;