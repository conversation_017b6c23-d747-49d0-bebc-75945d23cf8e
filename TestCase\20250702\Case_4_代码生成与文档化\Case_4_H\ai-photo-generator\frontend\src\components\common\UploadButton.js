import React, { useState } from 'react';
import { Upload, Button, message } from 'antd';
import { UploadOutlined, LoadingOutlined } from '@ant-design/icons';
import { validateFile } from '../../utils/utils';
import { UPLOAD_CONFIG } from '../../utils/constants';

const UploadButton = ({ onUploadSuccess, buttonText = '上传照片', disabled = false }) => {
  const [loading, setLoading] = useState(false);

  const beforeUpload = (file) => {
    return validateFile(file);
  };

  const customRequest = async ({ file, onSuccess, onError }) => {
    setLoading(true);
    try {
      // 这里不实际上传文件，而是将文件对象传递给父组件
      // 实际上传会在父组件中处理
      onUploadSuccess(file);
      onSuccess('ok');
      message.success('文件已选择，准备上传');
    } catch (error) {
      console.error('文件处理失败:', error);
      message.error('文件处理失败，请重试');
      onError(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Upload
      name="photo"
      showUploadList={false}
      beforeUpload={beforeUpload}
      customRequest={customRequest}
      accept={UPLOAD_CONFIG.acceptedFormats.join(',')}
      disabled={disabled || loading}
    >
      <Button 
        icon={loading ? <LoadingOutlined /> : <UploadOutlined />} 
        disabled={disabled || loading}
        type="primary"
        size="large"
      >
        {loading ? '处理中...' : buttonText}
      </Button>
    </Upload>
  );
};

export default UploadButton;