import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { 
  Typography, 
  Card, 
  Button, 
  Row, 
  Col, 
  Spin, 
  Empty, 
  Tabs, 
  Input, 
  Select, 
  Space, 
  Pagination,
  Modal,
  message
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  FilterOutlined, 
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { 
  fetchUserPhotos, 
  fetchFavoritePhotos,
  deletePhoto,
  batchDeletePhotos,
  selectUserPhotos, 
  selectFavoritePhotos,
  selectPhotoLoading, 
  selectPhotoError,
  selectPhotoTotal,
  selectPhotoPage
} from '../../store/photoSlice';
import { ROUTES, PHOTO_SPECS } from '../../utils/constants';
import PhotoCard from '../../components/photo/PhotoCard';
import PhotoListItem from '../../components/photo/PhotoListItem';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;
const { confirm } = Modal;

/**
 * 我的照片页面组件
 * 
 * @returns {React.ReactNode} 渲染的组件
 */
const MyPhotosPage = () => {
  const dispatch = useDispatch();
  const photos = useSelector(selectUserPhotos);
  const favoritePhotos = useSelector(selectFavoritePhotos);
  const loading = useSelector(selectPhotoLoading);
  const error = useSelector(selectPhotoError);
  const total = useSelector(selectPhotoTotal);
  const currentPage = useSelector(selectPhotoPage);
  
  const [activeTab, setActiveTab] = useState('all');
  const [viewMode, setViewMode] = useState('grid');
  const [searchText, setSearchText] = useState('');
  const [filterSpec, setFilterSpec] = useState('');
  const [selectedPhotos, setSelectedPhotos] = useState([]);
  const [pageSize, setPageSize] = useState(12);
  
  // 获取用户照片
  useEffect(() => {
    if (activeTab === 'all') {
      dispatch(fetchUserPhotos({ 
        page: currentPage, 
        limit: pageSize,
        search: searchText,
        spec: filterSpec
      }));
    } else if (activeTab === 'favorites') {
      dispatch(fetchFavoritePhotos({ 
        page: currentPage, 
        limit: pageSize,
        search: searchText,
        spec: filterSpec
      }));
    }
  }, [dispatch, activeTab, currentPage, pageSize, searchText, filterSpec]);
  
  /**
   * 处理标签切换
   * 
   * @param {string} key - 标签键
   */
  const handleTabChange = (key) => {
    setActiveTab(key);
    setSelectedPhotos([]);
  };
  
  /**
   * 处理搜索
   * 
   * @param {string} value - 搜索文本
   */
  const handleSearch = (value) => {
    setSearchText(value);
  };
  
  /**
   * 处理规格筛选
   * 
   * @param {string} value - 规格值
   */
  const handleFilterChange = (value) => {
    setFilterSpec(value);
  };
  
  /**
   * 处理页码变化
   * 
   * @param {number} page - 页码
   * @param {number} size - 每页数量
   */
  const handlePageChange = (page, size) => {
    if (size !== pageSize) {
      setPageSize(size);
    }
    
    if (activeTab === 'all') {
      dispatch(fetchUserPhotos({ 
        page, 
        limit: size,
        search: searchText,
        spec: filterSpec
      }));
    } else if (activeTab === 'favorites') {
      dispatch(fetchFavoritePhotos({ 
        page, 
        limit: size,
        search: searchText,
        spec: filterSpec
      }));
    }
  };
  
  /**
   * 处理照片选择
   * 
   * @param {string} id - 照片ID
   * @param {boolean} isSelected - 是否选中
   */
  const handlePhotoSelect = (id, isSelected) => {
    if (isSelected) {
      setSelectedPhotos([...selectedPhotos, id]);
    } else {
      setSelectedPhotos(selectedPhotos.filter(photoId => photoId !== id));
    }
  };
  
  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    if (selectedPhotos.length === 0) {
      message.warning('请先选择要删除的照片');
      return;
    }
    
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedPhotos.length} 张照片吗？此操作无法撤销。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(batchDeletePhotos(selectedPhotos)).unwrap();
          message.success(`成功删除 ${selectedPhotos.length} 张照片`);
          setSelectedPhotos([]);
        } catch (err) {
          message.error('删除失败: ' + err.message);
        }
      },
    });
  };
  
  /**
   * 处理单个照片删除
   * 
   * @param {string} id - 照片ID
   */
  const handleDeletePhoto = (id) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: '确定要删除这张照片吗？此操作无法撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deletePhoto(id)).unwrap();
          message.success('照片删除成功');
        } catch (err) {
          message.error('删除失败: ' + err.message);
        }
      },
    });
  };
  
  // 当前显示的照片列表
  const currentPhotos = activeTab === 'all' ? photos : favoritePhotos;
  
  // 渲染照片列表
  const renderPhotoList = () => {
    if (loading && currentPhotos.length === 0) {
      return (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      );
    }
    
    if (currentPhotos.length === 0) {
      return (
        <Card>
          <Empty
            description={
              <span>
                {activeTab === 'all' ? '暂无照片' : '暂无收藏的照片'}
              </span>
            }
          >
            <Button type="primary">
              <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
            </Button>
          </Empty>
        </Card>
      );
    }
    
    if (viewMode === 'grid') {
      return (
        <Row gutter={[16, 16]}>
          {currentPhotos.map(photo => (
            <Col xs={24} sm={12} md={8} lg={6} key={photo.id}>
              <PhotoCard 
                photo={photo} 
                selectable={true}
                selected={selectedPhotos.includes(photo.id)}
                onSelect={(isSelected) => handlePhotoSelect(photo.id, isSelected)}
                onDelete={() => handleDeletePhoto(photo.id)}
              />
            </Col>
          ))}
        </Row>
      );
    } else {
      return (
        <div className="photo-list">
          {currentPhotos.map(photo => (
            <PhotoListItem 
              key={photo.id} 
              photo={photo} 
              selectable={true}
              selected={selectedPhotos.includes(photo.id)}
              onSelect={(isSelected) => handlePhotoSelect(photo.id, isSelected)}
              onDelete={() => handleDeletePhoto(photo.id)}
            />
          ))}
        </div>
      );
    }
  };
  
  return (
    <div className="my-photos-page page-container">
      <div className="page-title">
        <Title level={2}>我的照片</Title>
        <Button type="primary" icon={<PlusOutlined />} className="btn-primary">
          <Link to={ROUTES.CREATE_PHOTO}>创建新照片</Link>
        </Button>
      </div>
      
      <Card bordered={false}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="全部照片" key="all" />
          <TabPane tab="收藏照片" key="favorites" />
        </Tabs>
        
        <div className="toolbar" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <div className="toolbar-left" style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
            <Search
              placeholder="搜索照片"
              allowClear
              onSearch={handleSearch}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="规格筛选"
              allowClear
              onChange={handleFilterChange}
              style={{ width: 150 }}
              suffixIcon={<FilterOutlined />}
            >
              {Object.entries(PHOTO_SPECS).map(([key, spec]) => (
                <Option key={key} value={key}>
                  {spec.name}
                </Option>
              ))}
            </Select>
          </div>
          
          <div className="toolbar-right" style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
            <Space>
              <Button.Group>
                <Button
                  type={viewMode === 'grid' ? 'primary' : 'default'}
                  onClick={() => setViewMode('grid')}
                >
                  网格
                </Button>
                <Button
                  type={viewMode === 'list' ? 'primary' : 'default'}
                  onClick={() => setViewMode('list')}
                >
                  列表
                </Button>
              </Button.Group>
              
              {selectedPhotos.length > 0 && (
                <Button 
                  icon={<DeleteOutlined />} 
                  danger 
                  onClick={handleBatchDelete}
                >
                  删除选中 ({selectedPhotos.length})
                </Button>
              )}
            </Space>
          </div>
        </div>
        
        {error && (
          <div style={{ marginBottom: 16 }}>
            <Text type="danger">{error}</Text>
          </div>
        )}
        
        <div className="photo-container">
          {renderPhotoList()}
        </div>
        
        <div className="pagination" style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={handlePageChange}
            showSizeChanger
            showTotal={(total) => `共 ${total} 张照片`}
          />
        </div>
      </Card>
    </div>
  );
};

export default MyPhotosPage;