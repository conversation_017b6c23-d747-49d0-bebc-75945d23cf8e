import api from './api'

class AuthService {
  /**
   * 用户登录
   * @param {Object} credentials 登录凭据
   * @param {string} credentials.email 邮箱
   * @param {string} credentials.password 密码
   * @returns {Promise} 登录响应
   */
  async login(credentials) {
    return await api.post('/user/login', credentials)
  }

  /**
   * 用户注册
   * @param {Object} userData 用户数据
   * @param {string} userData.username 用户名
   * @param {string} userData.email 邮箱
   * @param {string} userData.password 密码
   * @returns {Promise} 注册响应
   */
  async register(userData) {
    return await api.post('/user/register', userData)
  }

  /**
   * 获取用户信息
   * @returns {Promise} 用户信息响应
   */
  async getProfile() {
    return await api.get('/user/profile')
  }

  /**
   * 更新用户信息
   * @param {Object} userData 用户数据
   * @param {string} userData.username 用户名
   * @param {string} userData.avatar 头像URL
   * @returns {Promise} 更新响应
   */
  async updateProfile(userData) {
    return await api.put('/user/profile', userData)
  }

  /**
   * 修改密码
   * @param {Object} passwordData 密码数据
   * @param {string} passwordData.currentPassword 当前密码
   * @param {string} passwordData.newPassword 新密码
   * @returns {Promise} 修改响应
   */
  async changePassword(passwordData) {
    return await api.put('/user/password', passwordData)
  }

  /**
   * 获取用户统计信息
   * @returns {Promise} 统计信息响应
   */
  async getUserStats() {
    return await api.get('/user/stats')
  }

  /**
   * 用户注销
   * @returns {Promise} 注销响应
   */
  async logout() {
    return await api.post('/user/logout')
  }

  /**
   * 刷新token
   * @param {string} refreshToken 刷新token
   * @returns {Promise} 刷新响应
   */
  async refreshToken(refreshToken) {
    return await api.post('/user/refresh', { refreshToken })
  }

  /**
   * 验证token是否有效
   * @returns {Promise} 验证响应
   */
  async validateToken() {
    return await api.get('/user/validate')
  }
}

export default new AuthService()
