import axios from 'axios';
import { API_ENDPOINTS, STORAGE_KEYS } from '../utils/constants';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * 照片服务
 * 处理照片上传、生成、获取等操作
 */
const photoService = {
  /**
   * 上传原始照片
   * @param {File} file - 要上传的照片文件
   * @param {Object} metadata - 照片元数据
   * @returns {Promise} 包含上传结果的Promise
   */
  uploadPhoto: async (file, metadata = {}) => {
    const formData = new FormData();
    formData.append('photo', file);
    
    // 添加元数据
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }
    
    const response = await api.post(API_ENDPOINTS.UPLOAD_PHOTO, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  },
  
  /**
   * 生成证件照
   * @param {string} photoId - 原始照片ID
   * @param {Object} options - 生成选项
   * @param {string} options.type - 证件照类型
   * @param {Object} options.size - 尺寸配置
   * @param {string} options.backgroundColor - 背景颜色
   * @returns {Promise} 包含生成结果的Promise
   */
  generatePhoto: async (photoId, options) => {
    const response = await api.post(API_ENDPOINTS.GENERATE_PHOTO, {
      photoId,
      ...options,
    });
    
    return response.data;
  },
  
  /**
   * 获取用户的所有照片
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.sortBy - 排序字段
   * @param {string} params.sortOrder - 排序顺序
   * @returns {Promise} 包含照片列表和分页信息的Promise
   */
  getPhotos: async (params = {}) => {
    const response = await api.get(API_ENDPOINTS.GET_PHOTOS, { params });
    return response.data;
  },
  
  /**
   * 获取单张照片详情
   * @param {string} id - 照片ID
   * @returns {Promise} 包含照片详情的Promise
   */
  getPhoto: async (id) => {
    const url = API_ENDPOINTS.GET_PHOTO.replace(':id', id);
    const response = await api.get(url);
    return response.data;
  },
  
  /**
   * 更新照片信息
   * @param {string} id - 照片ID
   * @param {Object} data - 要更新的数据
   * @returns {Promise} 包含更新后的照片信息的Promise
   */
  updatePhoto: async (id, data) => {
    const url = API_ENDPOINTS.UPDATE_PHOTO.replace(':id', id);
    const response = await api.put(url, data);
    return response.data;
  },
  
  /**
   * 删除照片
   * @param {string} id - 照片ID
   * @returns {Promise} 包含删除结果的Promise
   */
  deletePhoto: async (id) => {
    const url = API_ENDPOINTS.DELETE_PHOTO.replace(':id', id);
    const response = await api.delete(url);
    return response.data;
  },
  
  /**
   * 批量删除照片
   * @param {Array<string>} ids - 照片ID数组
   * @returns {Promise} 包含删除结果的Promise
   */
  batchDeletePhotos: async (ids) => {
    const response = await api.post(`${API_ENDPOINTS.GET_PHOTOS}/batch-delete`, { ids });
    return response.data;
  },
  
  /**
   * 下载照片
   * @param {string} id - 照片ID
   * @param {string} type - 下载类型 (original, generated)
   * @returns {Promise} 包含下载URL的Promise
   */
  downloadPhoto: async (id, type = 'generated') => {
    const url = `${API_ENDPOINTS.GET_PHOTO.replace(':id', id)}/download`;
    const response = await api.get(url, { params: { type } });
    return response.data;
  },
  
  /**
   * 获取照片预览URL
   * @param {string} id - 照片ID
   * @param {string} type - 预览类型 (original, generated, thumbnail)
   * @returns {string} 预览URL
   */
  getPhotoPreviewUrl: (id, type = 'generated') => {
    const baseUrl = process.env.REACT_APP_API_URL || '';
    return `${baseUrl}/api/photos/${id}/preview?type=${type}&token=${localStorage.getItem(STORAGE_KEYS.TOKEN)}`;
  },
};

export default photoService;