const Joi = require('joi');

// 用户注册验证
const userRegistrationSchema = Joi.object({
  username: Jo<PERSON>.string().alphanum().min(3).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).max(128).required()
});

// 用户登录验证
const userLoginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

// 照片上传验证
const uploadSchema = Joi.object({
  spec: Joi.string().valid('1inch', '2inch', 'small2inch', 'big1inch', 'custom').optional()
});

// AI生成请求验证
const generateSchema = Joi.object({
  uploadId: Joi.string().required(),
  spec: Joi.string().valid('1inch', '2inch', 'small2inch', 'big1inch', 'custom').required(),
  background: Joi.string().valid('blue', 'red', 'white', 'lightblue', 'lightgray').required(),
  count: Joi.number().integer().min(1).max(10).default(5),
  modelConfig: Joi.object({
    modelName: Joi.string().optional(),
    parameters: Joi.object().optional()
  }).optional(),
  customSpec: Joi.when('spec', {
    is: 'custom',
    then: Joi.object({
      width: Joi.number().integer().min(100).max(2000).required(),
      height: Joi.number().integer().min(100).max(2000).required(),
      name: Joi.string().max(50).optional()
    }).required(),
    otherwise: Joi.optional()
  })
});

// 文件验证
const validateFile = (file) => {
  const errors = [];
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedTypes.includes(file.mimetype)) {
    errors.push('文件类型不支持，仅支持 JPG、JPEG、PNG 格式');
  }
  
  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('文件大小超过限制，最大支持 10MB');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// 批量文件验证
const validateFiles = (files) => {
  const errors = [];
  
  // 检查文件数量
  if (files.length > 5) {
    errors.push('文件数量超过限制，最多支持 5 张照片');
  }
  
  // 验证每个文件
  files.forEach((file, index) => {
    const fileValidation = validateFile(file);
    if (!fileValidation.isValid) {
      errors.push(`文件 ${index + 1}: ${fileValidation.errors.join(', ')}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// 通用验证中间件
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        code: 400,
        message: '请求参数验证失败',
        errors: error.details.map(detail => detail.message)
      });
    }
    next();
  };
};

module.exports = {
  userRegistrationSchema,
  userLoginSchema,
  uploadSchema,
  generateSchema,
  validateFile,
  validateFiles,
  validate
};
