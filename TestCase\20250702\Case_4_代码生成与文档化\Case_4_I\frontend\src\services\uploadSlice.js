import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// 上传照片
export const uploadPhoto = createAsyncThunk(
  'upload/uploadPhoto',
  async ({ file, spec }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (spec) formData.append('spec', spec);
      
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.post(`${API_URL}/upload`, formData, {
        headers: {
          ...headers,
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          // 可以在这里更新上传进度
        }
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '上传失败' });
    }
  }
);

// 批量上传照片
export const uploadMultiplePhotos = createAsyncThunk(
  'upload/uploadMultiplePhotos',
  async ({ files, spec }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });
      if (spec) formData.append('spec', spec);
      
      const token = localStorage.getItem('token');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.post(`${API_URL}/upload/multiple`, formData, {
        headers: {
          ...headers,
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          // 可以在这里更新上传进度
        }
      });
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: '批量上传失败' });
    }
  }
);

const uploadSlice = createSlice({
  name: 'upload',
  initialState: {
    uploadedFiles: [],
    currentUpload: null,
    uploadProgress: 0,
    loading: false,
    error: null,
  },
  reducers: {
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },
    clearUploadError: (state) => {
      state.error = null;
    },
    clearUploads: (state) => {
      state.uploadedFiles = [];
      state.currentUpload = null;
      state.uploadProgress = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // 单张照片上传
      .addCase(uploadPhoto.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.uploadProgress = 0;
      })
      .addCase(uploadPhoto.fulfilled, (state, action) => {
        state.loading = false;
        state.uploadedFiles.push(action.payload.data);
        state.currentUpload = action.payload.data;
        state.uploadProgress = 100;
      })
      .addCase(uploadPhoto.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || '上传失败';
        state.uploadProgress = 0;
      })
      // 批量照片上传
      .addCase(uploadMultiplePhotos.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.uploadProgress = 0;
      })
      .addCase(uploadMultiplePhotos.fulfilled, (state, action) => {
        state.loading = false;
        state.uploadedFiles = [...state.uploadedFiles, ...action.payload.data];
        state.uploadProgress = 100;
      })
      .addCase(uploadMultiplePhotos.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || '批量上传失败';
        state.uploadProgress = 0;
      });
  },
});

export const { setUploadProgress, clearUploadError, clearUploads } = uploadSlice.actions;
export default uploadSlice.reducer;