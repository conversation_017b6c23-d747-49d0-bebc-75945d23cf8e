const path = require('path');
const fs = require('fs');
const { 
  createGenerationTask, 
  getTaskStatus, 
  downloadPhotos 
} = require('../services/photoService');

// 生成证件照控制器
const generatePhotos = async (req, res) => {
  try {
    const { upload_id, spec, background, count } = req.body;
    
    // 验证必要参数
    if (!upload_id || !spec || !background) {
      return res.status(400).json({
        code: 400,
        message: '缺少必要参数'
      });
    }
    
    // 获取用户ID（如果已认证）
    const userId = req.user ? req.user.id : null;
    
    // 创建生成任务
    const result = await createGenerationTask(upload_id, spec, background, userId);
    
    if (!result.success) {
      return res.status(400).json({
        code: 400,
        message: '创建任务失败',
        error: result.error
      });
    }
    
    // 返回任务ID
    res.status(200).json({
      code: 200,
      message: '任务创建成功',
      data: {
        task_id: result.taskId,
        status: 'processing'
      }
    });
  } catch (error) {
    console.error('生成照片错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 查询任务状态控制器
const checkTaskStatus = async (req, res) => {
  try {
    const taskId = req.params.taskId;
    
    if (!taskId) {
      return res.status(400).json({
        code: 400,
        message: '缺少任务ID'
      });
    }
    
    // 查询任务状态
    const result = await getTaskStatus(taskId);
    
    if (!result.success) {
      return res.status(404).json({
        code: 404,
        message: '任务不存在',
        error: result.error
      });
    }
    
    // 返回任务状态
    res.status(200).json({
      code: 200,
      data: {
        task_id: result.task_id,
        status: result.status,
        progress: result.progress,
        generated_photos: result.generated_photos || []
      }
    });
  } catch (error) {
    console.error('查询任务状态错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

// 下载照片控制器
const downloadGeneratedPhotos = async (req, res) => {
  try {
    const { photo_ids } = req.body;
    
    if (!photo_ids || !Array.isArray(photo_ids) || photo_ids.length === 0) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的照片ID'
      });
    }
    
    // 下载照片
    const result = await downloadPhotos(photo_ids);
    
    if (!result.success) {
      return res.status(404).json({
        code: 404,
        message: '照片不存在',
        error: result.error
      });
    }
    
    // 获取文件路径
    const filePath = path.join(__dirname, '../../', result.filePath.replace('/public/', 'public/'));
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        code: 404,
        message: '文件不存在'
      });
    }
    
    // 返回文件
    res.download(filePath, path.basename(filePath));
  } catch (error) {
    console.error('下载照片错误:', error);
    res.status(500).json({
      code: 500,
      message: '服务器错误',
      error: error.message
    });
  }
};

module.exports = {
  generatePhotos,
  checkTaskStatus,
  downloadGeneratedPhotos
}; 