import React from 'react';
import { Layout, Menu, Button } from 'antd';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { 
  HomeOutlined, 
  UploadOutlined, 
  UserOutlined, 
  LoginOutlined 
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;

const MainLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  // 模拟用户登录状态，实际项目中应从状态管理或Context中获取
  const isLoggedIn = localStorage.getItem('token') ? true : false;

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/upload',
      icon: <UploadOutlined />,
      label: <Link to="/upload">上传照片</Link>,
    }
  ];

  const handleLogin = () => {
    navigate('/login');
  };

  const handleLogout = () => {
    // 清除用户登录信息
    localStorage.removeItem('token');
    navigate('/');
  };

  return (
    <Layout className="layout" style={{ minHeight: '100vh' }}>
      <Header style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div className="logo" style={{ color: 'white', fontSize: '20px', fontWeight: 'bold', marginRight: '40px' }}>
            AI证件照生成平台
          </div>
          <Menu
            theme="dark"
            mode="horizontal"
            selectedKeys={[location.pathname]}
            items={menuItems}
            style={{ flex: 1 }}
          />
        </div>
        <div>
          {isLoggedIn ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button 
                type="text" 
                icon={<UserOutlined />} 
                style={{ color: 'white' }}
                onClick={() => navigate('/profile')}
              >
                个人中心
              </Button>
              <Button 
                type="text" 
                icon={<LoginOutlined />} 
                style={{ color: 'white' }}
                onClick={handleLogout}
              >
                退出登录
              </Button>
            </div>
          ) : (
            <Button 
              type="primary" 
              onClick={handleLogin}
            >
              登录/注册
            </Button>
          )}
        </div>
      </Header>
      <Content style={{ padding: '0 50px', marginTop: 20 }}>
        <div className="site-layout-content" style={{ background: '#fff', padding: 24, minHeight: 280 }}>
          <Outlet />
        </div>
      </Content>
      <Footer style={{ textAlign: 'center' }}>
        AI证件照生成平台 ©{new Date().getFullYear()} 版权所有
      </Footer>
    </Layout>
  );
};

export default MainLayout;