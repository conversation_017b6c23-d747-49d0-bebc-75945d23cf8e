.upload-page {
  .steps-container {
    margin: 30px 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .error-alert {
    margin-bottom: 24px;
  }
  
  .upload-description {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 24px;
  }
  
  .upload-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .processing-container {
    padding: 40px 0;
    
    .processing-icon {
      font-size: 64px;
      color: #1890ff;
      margin-bottom: 24px;
      animation: pulse 1.5s infinite;
    }
    
    h3 {
      margin-bottom: 16px;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式样式
@media (max-width: 768px) {
  .upload-page {
    .steps-container {
      margin: 20px 0;
    }
    
    .processing-container {
      padding: 30px 0;
      
      .processing-icon {
        font-size: 48px;
      }
    }
  }
}