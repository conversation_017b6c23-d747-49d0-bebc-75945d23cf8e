const path = require('path');
const fs = require('fs').promises;
const archiver = require('archiver');
const { GenerationTask, GeneratedPhoto, Upload } = require('../models');
const logger = require('../utils/logger');

class TaskController {
  /**
   * 获取任务详情
   */
  async getTaskDetail(req, res, next) {
    try {
      const { taskId } = req.params;

      const task = await GenerationTask.findByPk(taskId, {
        include: [
          {
            model: GeneratedPhoto,
            as: 'generatedPhotos',
            attributes: ['id', 'filePath', 'qualityScore', 'width', 'height', 'fileSize', 'downloadCount', 'createdAt']
          },
          {
            model: Upload,
            as: 'upload',
            attributes: ['originalFilename', 'fileSize', 'uploadTime', 'userId']
          }
        ]
      });

      if (!task) {
        return res.status(404).json({
          code: 404,
          message: '任务不存在',
          errors: ['指定的任务ID无效']
        });
      }

      // 检查权限
      if (req.user && task.upload.userId && task.upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权访问此任务',
          errors: ['权限不足']
        });
      }

      const responseData = {
        taskId: task.id,
        status: task.status,
        progress: task.progress,
        spec: task.spec,
        background: task.background,
        modelName: task.modelName,
        createdAt: task.createdAt,
        completedAt: task.completedAt,
        errorMessage: task.errorMessage,
        upload: {
          originalFilename: task.upload.originalFilename,
          fileSize: task.upload.fileSize,
          uploadTime: task.upload.uploadTime
        }
      };

      if (task.generatedPhotos && task.generatedPhotos.length > 0) {
        responseData.generatedPhotos = task.generatedPhotos.map(photo => ({
          id: photo.id,
          url: `/uploads/generated/${path.basename(photo.filePath)}`,
          qualityScore: photo.qualityScore,
          width: photo.width,
          height: photo.height,
          fileSize: photo.fileSize,
          downloadCount: photo.downloadCount,
          createdAt: photo.createdAt
        }));
      }

      res.json({
        code: 200,
        message: '获取任务详情成功',
        data: responseData
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 下载单张生成的照片
   */
  async downloadPhoto(req, res, next) {
    try {
      const { photoId } = req.params;

      const photo = await GeneratedPhoto.findByPk(photoId, {
        include: [
          {
            model: GenerationTask,
            as: 'task',
            include: [
              {
                model: Upload,
                as: 'upload',
                attributes: ['userId']
              }
            ]
          }
        ]
      });

      if (!photo) {
        return res.status(404).json({
          code: 404,
          message: '照片不存在',
          errors: ['指定的照片ID无效']
        });
      }

      // 检查权限
      if (req.user && photo.task.upload.userId && photo.task.upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权下载此照片',
          errors: ['权限不足']
        });
      }

      // 检查文件是否存在
      try {
        await fs.access(photo.filePath);
      } catch (error) {
        return res.status(404).json({
          code: 404,
          message: '文件不存在',
          errors: ['照片文件已被删除或移动']
        });
      }

      // 更新下载次数
      await photo.increment('downloadCount');

      // 设置响应头
      const fileName = `id_photo_${photo.id}.jpg`;
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'image/jpeg');

      // 发送文件
      res.sendFile(path.resolve(photo.filePath));

      logger.info('照片下载成功', {
        photoId: photo.id,
        taskId: photo.taskId,
        userId: req.user?.id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量下载任务的所有照片（ZIP格式）
   */
  async downloadTaskPhotos(req, res, next) {
    try {
      const { taskId } = req.params;

      const task = await GenerationTask.findByPk(taskId, {
        include: [
          {
            model: GeneratedPhoto,
            as: 'generatedPhotos'
          },
          {
            model: Upload,
            as: 'upload',
            attributes: ['userId', 'originalFilename']
          }
        ]
      });

      if (!task) {
        return res.status(404).json({
          code: 404,
          message: '任务不存在',
          errors: ['指定的任务ID无效']
        });
      }

      // 检查权限
      if (req.user && task.upload.userId && task.upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权下载此任务的照片',
          errors: ['权限不足']
        });
      }

      if (!task.generatedPhotos || task.generatedPhotos.length === 0) {
        return res.status(404).json({
          code: 404,
          message: '没有可下载的照片',
          errors: ['该任务尚未生成照片']
        });
      }

      // 设置响应头
      const zipFileName = `id_photos_${taskId}.zip`;
      res.setHeader('Content-Disposition', `attachment; filename="${zipFileName}"`);
      res.setHeader('Content-Type', 'application/zip');

      // 创建ZIP压缩流
      const archive = archiver('zip', {
        zlib: { level: 9 } // 压缩级别
      });

      // 处理错误
      archive.on('error', (err) => {
        logger.error('ZIP压缩失败', {
          taskId,
          error: err.message
        });
        if (!res.headersSent) {
          res.status(500).json({
            code: 500,
            message: 'ZIP压缩失败',
            errors: [err.message]
          });
        }
      });

      // 将压缩流管道到响应
      archive.pipe(res);

      // 添加文件到压缩包
      let addedCount = 0;
      for (let i = 0; i < task.generatedPhotos.length; i++) {
        const photo = task.generatedPhotos[i];
        
        try {
          // 检查文件是否存在
          await fs.access(photo.filePath);
          
          // 生成文件名
          const fileName = `id_photo_${i + 1}_quality_${(photo.qualityScore * 100).toFixed(0)}.jpg`;
          
          // 添加文件到压缩包
          archive.file(photo.filePath, { name: fileName });
          addedCount++;
          
          // 更新下载次数
          await photo.increment('downloadCount');
        } catch (error) {
          logger.warn('跳过不存在的文件', {
            photoId: photo.id,
            filePath: photo.filePath
          });
        }
      }

      if (addedCount === 0) {
        return res.status(404).json({
          code: 404,
          message: '没有可用的照片文件',
          errors: ['所有照片文件都不存在']
        });
      }

      // 完成压缩
      await archive.finalize();

      logger.info('批量下载完成', {
        taskId,
        photoCount: addedCount,
        userId: req.user?.id
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除任务及其相关数据
   */
  async deleteTask(req, res, next) {
    try {
      const { taskId } = req.params;

      const task = await GenerationTask.findByPk(taskId, {
        include: [
          {
            model: GeneratedPhoto,
            as: 'generatedPhotos'
          },
          {
            model: Upload,
            as: 'upload',
            attributes: ['userId']
          }
        ]
      });

      if (!task) {
        return res.status(404).json({
          code: 404,
          message: '任务不存在',
          errors: ['指定的任务ID无效']
        });
      }

      // 检查权限
      if (req.user && task.upload.userId && task.upload.userId !== req.user.id) {
        return res.status(403).json({
          code: 403,
          message: '无权删除此任务',
          errors: ['权限不足']
        });
      }

      // 删除生成的照片文件
      if (task.generatedPhotos) {
        for (const photo of task.generatedPhotos) {
          try {
            await fs.unlink(photo.filePath);
          } catch (error) {
            logger.warn('删除照片文件失败', {
              photoId: photo.id,
              filePath: photo.filePath,
              error: error.message
            });
          }
        }

        // 删除照片记录
        await GeneratedPhoto.destroy({
          where: { taskId: task.id }
        });
      }

      // 删除任务记录
      await task.destroy();

      logger.info('任务删除成功', {
        taskId,
        userId: req.user?.id
      });

      res.json({
        code: 200,
        message: '任务删除成功',
        data: null
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(req, res, next) {
    try {
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          message: '请先登录',
          errors: ['需要用户认证']
        });
      }

      // 获取用户的任务统计
      const stats = await GenerationTask.findAll({
        include: [
          {
            model: Upload,
            as: 'upload',
            where: { userId: req.user.id },
            attributes: []
          }
        ],
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('GenerationTask.id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      // 获取总的生成照片数量
      const totalPhotos = await GeneratedPhoto.count({
        include: [
          {
            model: GenerationTask,
            as: 'task',
            include: [
              {
                model: Upload,
                as: 'upload',
                where: { userId: req.user.id },
                attributes: []
              }
            ]
          }
        ]
      });

      // 格式化统计数据
      const formattedStats = {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };

      stats.forEach(stat => {
        formattedStats[stat.status] = parseInt(stat.count);
      });

      res.json({
        code: 200,
        message: '获取统计信息成功',
        data: {
          taskStats: formattedStats,
          totalTasks: Object.values(formattedStats).reduce((sum, count) => sum + count, 0),
          totalGeneratedPhotos: totalPhotos
        }
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new TaskController();
