import React, { useState, useEffect, useRef } from 'react';
import {
  Row,
  Col,
  Card,
  Slider,
  Button,
  Tabs,
  Select,
  Radio,
  Space,
  Divider,
  Typography,
  Tooltip,
  message,
} from 'antd';
import {
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  DownloadOutlined,
  RotateLeftOutlined,
  <PERSON>otateRightOutlined,
  SwapOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  CompressOutlined,
  BorderOuterOutlined,
  BgColorsOutlined,
  ScissorOutlined,
  HighlightOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 默认编辑设置
const defaultSettings = {
  brightness: 100, // 亮度 (0-200)
  contrast: 100,   // 对比度 (0-200)
  saturation: 100, // 饱和度 (0-200)
  temperature: 100, // 色温 (0-200)
  blur: 0,         // 模糊 (0-10)
  rotate: 0,       // 旋转角度 (0, 90, 180, 270)
  flip: {
    horizontal: false, // 水平翻转
    vertical: false,   // 垂直翻转
  },
  crop: {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
    aspect: null,
  },
  background: {
    color: '#FFFFFF',
    type: 'color', // color, transparent, blur
  },
  size: {
    width: 413,
    height: 626,
    unit: 'px',
  },
  format: 'jpg',
  quality: 90,
};

// 背景颜色选项
const backgroundColors = [
  '#FFFFFF', // 白色
  '#FF0000', // 红色
  '#0000FF', // 蓝色
  '#00FF00', // 绿色
  '#FFFF00', // 黄色
  '#FF00FF', // 粉色
  '#00FFFF', // 青色
  '#000000', // 黑色
  '#808080', // 灰色
  '#FFA500', // 橙色
];

// 照片尺寸选项
const photoSizes = [
  { label: '1寸', width: 295, height: 413 },
  { label: '2寸', width: 413, height: 579 },
  { label: '小2寸', width: 413, height: 531 },
  { label: '5寸', width: 1500, height: 1050 },
  { label: '护照', width: 413, height: 531 },
  { label: '签证', width: 413, height: 531 },
  { label: '自定义', width: 0, height: 0 },
];

/**
 * 照片编辑器组件
 * @param {Object} props - 组件属性
 * @param {string} props.imageUrl - 图片URL
 * @param {Function} props.onSave - 保存回调函数
 * @param {Object} props.initialSettings - 初始编辑设置
 * @param {boolean} props.loading - 加载状态
 * @param {Function} props.onSettingsChange - 设置变更回调函数
 */
const PhotoEditor = ({
  imageUrl,
  onSave,
  initialSettings = {},
  loading = false,
  onSettingsChange,
}) => {
  // 编辑器状态
  const [settings, setSettings] = useState({ ...defaultSettings, ...initialSettings });
  const [history, setHistory] = useState([{ ...defaultSettings, ...initialSettings }]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('adjust');
  const [previewScale, setPreviewScale] = useState(1);
  const [selectedSize, setSelectedSize] = useState(photoSizes[0]);
  
  // 引用
  const canvasRef = useRef(null);
  const imageRef = useRef(null);
  
  // 加载图片
  useEffect(() => {
    if (imageUrl) {
      const image = new Image();
      image.crossOrigin = 'anonymous';
      image.src = imageUrl;
      image.onload = () => {
        imageRef.current = image;
        renderCanvas();
      };
    }
  }, [imageUrl]);
  
  // 当设置变更时重新渲染画布
  useEffect(() => {
    renderCanvas();
    
    // 通知父组件设置已变更
    if (onSettingsChange) {
      onSettingsChange(settings);
    }
  }, [settings, previewScale]);
  
  // 渲染画布
  const renderCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas || !imageRef.current) return;
    
    const ctx = canvas.getContext('2d');
    const image = imageRef.current;
    
    // 设置画布尺寸
    canvas.width = selectedSize.width;
    canvas.height = selectedSize.height;
    
    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制背景
    if (settings.background.type === 'color') {
      ctx.fillStyle = settings.background.color;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
    
    // 保存当前状态
    ctx.save();
    
    // 移动到画布中心
    ctx.translate(canvas.width / 2, canvas.height / 2);
    
    // 应用旋转
    ctx.rotate((settings.rotate * Math.PI) / 180);
    
    // 应用翻转
    const scaleX = settings.flip.horizontal ? -1 : 1;
    const scaleY = settings.flip.vertical ? -1 : 1;
    ctx.scale(scaleX, scaleY);
    
    // 计算图像绘制尺寸和位置
    const aspectRatio = image.width / image.height;
    let drawWidth, drawHeight;
    
    if (canvas.width / canvas.height > aspectRatio) {
      // 画布更宽，以高度为基准
      drawHeight = canvas.height;
      drawWidth = drawHeight * aspectRatio;
    } else {
      // 画布更高，以宽度为基准
      drawWidth = canvas.width;
      drawHeight = drawWidth / aspectRatio;
    }
    
    // 绘制图像
    ctx.drawImage(
      image,
      -drawWidth / 2,
      -drawHeight / 2,
      drawWidth,
      drawHeight
    );
    
    // 恢复状态
    ctx.restore();
    
    // 应用滤镜
    applyFilters(ctx, canvas);
  };
  
  // 应用滤镜
  const applyFilters = (ctx, canvas) => {
    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // 亮度、对比度、饱和度、色温调整
    const brightness = settings.brightness / 100;
    const contrast = settings.contrast / 100;
    const saturation = settings.saturation / 100;
    const temperature = (settings.temperature - 100) / 100 * 30; // -30 到 +30 的范围
    
    for (let i = 0; i < data.length; i += 4) {
      // 获取RGB值
      let r = data[i];
      let g = data[i + 1];
      let b = data[i + 2];
      
      // 应用亮度
      r *= brightness;
      g *= brightness;
      b *= brightness;
      
      // 应用对比度
      const factor = (259 * (contrast * 100 + 255)) / (255 * (259 - contrast * 100));
      r = factor * (r - 128) + 128;
      g = factor * (g - 128) + 128;
      b = factor * (b - 128) + 128;
      
      // 应用饱和度
      const gray = 0.2989 * r + 0.5870 * g + 0.1140 * b;
      r = gray * (1 - saturation) + r * saturation;
      g = gray * (1 - saturation) + g * saturation;
      b = gray * (1 - saturation) + b * saturation;
      
      // 应用色温
      if (temperature > 0) {
        // 偏暖色
        r += temperature;
        g += temperature * 0.7;
      } else {
        // 偏冷色
        b -= temperature;
      }
      
      // 限制在0-255范围内
      data[i] = Math.max(0, Math.min(255, r));
      data[i + 1] = Math.max(0, Math.min(255, g));
      data[i + 2] = Math.max(0, Math.min(255, b));
    }
    
    // 将处理后的图像数据放回画布
    ctx.putImageData(imageData, 0, 0);
  };
  
  // 更新设置并添加到历史记录
  const updateSettings = (newSettings) => {
    // 创建新的设置对象
    const updatedSettings = { ...settings, ...newSettings };
    
    // 更新设置
    setSettings(updatedSettings);
    
    // 添加到历史记录
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(updatedSettings);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };
  
  // 撤销
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setSettings(history[historyIndex - 1]);
    }
  };
  
  // 重做
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setSettings(history[historyIndex + 1]);
    }
  };
  
  // 重置
  const handleReset = () => {
    updateSettings(defaultSettings);
  };
  
  // 旋转
  const handleRotate = (direction) => {
    let newRotate = settings.rotate;
    if (direction === 'left') {
      newRotate = (newRotate - 90) % 360;
    } else {
      newRotate = (newRotate + 90) % 360;
    }
    updateSettings({ rotate: newRotate });
  };
  
  // 翻转
  const handleFlip = (direction) => {
    const newFlip = { ...settings.flip };
    if (direction === 'horizontal') {
      newFlip.horizontal = !newFlip.horizontal;
    } else {
      newFlip.vertical = !newFlip.vertical;
    }
    updateSettings({ flip: newFlip });
  };
  
  // 缩放预览
  const handleZoom = (direction) => {
    if (direction === 'in' && previewScale < 2) {
      setPreviewScale(previewScale + 0.1);
    } else if (direction === 'out' && previewScale > 0.5) {
      setPreviewScale(previewScale - 0.1);
    } else if (direction === 'reset') {
      setPreviewScale(1);
    }
  };
  
  // 选择照片尺寸
  const handleSizeChange = (value) => {
    const size = photoSizes.find(s => s.label === value);
    if (size) {
      setSelectedSize(size);
      updateSettings({
        size: {
          width: size.width,
          height: size.height,
          unit: 'px',
        },
      });
    }
  };
  
  // 保存照片
  const handleSave = () => {
    if (!canvasRef.current) return;
    
    canvasRef.current.toBlob(
      (blob) => {
        if (onSave) {
          onSave(blob, settings);
        }
      },
      `image/${settings.format}`,
      settings.quality / 100
    );
  };
  
  // 下载照片
  const handleDownload = () => {
    if (!canvasRef.current) return;
    
    const link = document.createElement('a');
    link.download = `edited-photo.${settings.format}`;
    link.href = canvasRef.current.toDataURL(`image/${settings.format}`, settings.quality / 100);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <div className="photo-editor">
      <Row gutter={[24, 24]}>
        <Col xs={24} md={16}>
          <Card className="preview-card">
            <div className="canvas-container" style={{ transform: `scale(${previewScale})` }}>
              <canvas ref={canvasRef} className="editor-canvas" />
            </div>
            
            <div className="preview-toolbar">
              <Space></Space>
                <Tooltip title="缩小">
                  <Button
                    icon={<ZoomOutOutlined />}
                    onClick={() => handleZoom('out')}
                    disabled={previewScale <= 0.5}
                  />
                </Tooltip>
                <Button onClick={() => handleZoom('reset')}>
                  {Math.round(previewScale * 100)}%
                </Button>
                <Tooltip title="放大"></Tooltip>
                  <Button
                    icon={<ZoomInOutlined />}
                    onClick={() => handleZoom('in')}
                    disabled={previewScale >= 2}
                  />
                </Tooltip>
              </Space>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} md={8}>
          <Card className="controls-card">
            <div className="editor-actions">
              <Space>
                <Tooltip title="撤销">
                  <Button
                    icon={<UndoOutlined />}
                    onClick={handleUndo}
                    disabled={historyIndex <= 0}
                  />
                </Tooltip>
                <Tooltip title="重做">
                  <Button
                    icon={<RedoOutlined />}
                    onClick={handleRedo}
                    disabled={historyIndex >= history.length - 1}
                  />
                </Tooltip>
                <Button onClick={handleReset}>重置</Button>
              </Space>
              
              <Space>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                  loading={loading}
                >
                  保存
                </Button>
                <Button icon={<DownloadOutlined />} onClick={handleDownload}>
                  下载
                </Button>
              </Space>
            </div>
            
            <Divider />
            
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="调整" key="adjust">
                <div className="control-group">
                  <div className="control-item">
                    <Text>亮度</Text>
                    <Slider
                      min={0}
                      max={200}
                      value={settings.brightness}
                      onChange={(value) => updateSettings({ brightness: value })}
                    />
                  </div>
                  
                  <div className="control-item">
                    <Text>对比度</Text>
                    <Slider
                      min={0}
                      max={200}
                      value={settings.contrast}
                      onChange={(value) => updateSettings({ contrast: value })}
                    />
                  </div>
                  
                  <div className="control-item">
                    <Text>饱和度</Text>
                    <Slider
                      min={0}
                      max={200}
                      value={settings.saturation}
                      onChange={(value) => updateSettings({ saturation: value })}
                    />
                  </div>
                  
                  <div className="control-item">
                    <Text>色温</Text>
                    <Slider
                      min={0}
                      max={200}
                      value={settings.temperature}
                      onChange={(value) => updateSettings({ temperature: value })}
                    />
                  </div>
                </div>
              </TabPane>
              
              <TabPane tab="变换" key="transform">
                <div className="control-group">
                  <div className="control-item">
                    <Text>旋转</Text>
                    <div className="button-group">
                      <Tooltip title="向左旋转90°">
                        <Button
                          icon={<RotateLeftOutlined />}
                          onClick={() => handleRotate('left')}
                        />
                      </Tooltip>
                      <Tooltip title="向右旋转90°">
                        <Button
                          icon={<RotateRightOutlined />}
                          onClick={() => handleRotate('right')}
                        />
                      </Tooltip>
                    </div>
                  </div>
                  
                  <div className="control-item">
                    <Text>翻转</Text>
                    <div className="button-group">
                      <Tooltip title="水平翻转">
                        <Button
                          icon={<SwapOutlined />}
                          onClick={() => handleFlip('horizontal')}
                        />
                      </Tooltip>
                      <Tooltip title="垂直翻转">
                        <Button
                          icon={<SwapOutlined style={{ transform: 'rotate(90deg)' }} />}
                          onClick={() => handleFlip('vertical')}
                        />
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </TabPane>
              
              <TabPane tab="背景" key="background">
                <div className="control-group">
                  <div className="control-item">
                    <Text>背景类型</Text>
                    <Radio.Group
                      value={settings.background.type}
                      onChange={(e) =>
                        updateSettings({
                          background: { ...settings.background, type: e.target.value },
                        })
                      }
                    >
                      <Radio.Button value="color">纯色</Radio.Button>
                      <Radio.Button value="transparent">透明</Radio.Button>
                    </Radio.Group>
                  </div>
                  
                  {settings.background.type === 'color' && (
                    <div className="control-item">
                      <Text>背景颜色</Text>
                      <div className="color-picker">
                        {backgroundColors.map((color) => (
                          <div
                            key={color}
                            className={`color-swatch ${
                              settings.background.color === color ? 'active' : ''
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() =>
                              updateSettings({
                                background: { ...settings.background, color },
                              })
                            }
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabPane>
              
              <TabPane tab="尺寸" key="size">
                <div className="control-group">
                  <div className="control-item">
                    <Text>照片尺寸</Text>
                    <Select
                      style={{ width: '100%' }}
                      value={selectedSize.label}
                      onChange={handleSizeChange}
                    >
                      {photoSizes.map((size) => (
                        <Option key={size.label} value={size.label}>
                          {size.label} ({size.width}x{size.height}px)
                        </Option>
                      ))}
                    </Select>
                  </div>
                  
                  {selectedSize.label === '自定义' && (
                    <>
                      <div className="control-item">
                        <Text>宽度 (px)</Text>
                        <Input
                          type="number"
                          value={settings.size.width}
                          onChange={(e) =>
                            updateSettings({
                              size: { ...settings.size, width: parseInt(e.target.value) },
                            })
                          }
                        />
                      </div>
                      
                      <div className="control-item">
                        <Text>高度 (px)</Text>
                        <Input
                          type="number"
                          value={settings.size.height}
                          onChange={(e) =>
                            updateSettings({
                              size: { ...settings.size, height: parseInt(e.target.value) },
                            })
                          }
                        />
                      </div>
                    </>
                  )}
                </div>
              </TabPane>
              
              <TabPane tab="导出" key="export">
                <div className="control-group">
                  <div className="control-item">
                    <Text>文件格式</Text>
                    <Radio.Group
                      value={settings.format}
                      onChange={(e) => updateSettings({ format: e.target.value })}
                    >
                      <Radio.Button value="jpg">JPG</Radio.Button>
                      <Radio.Button value="png">PNG</Radio.Button>
                    </Radio.Group>
                  </div>
                  
                  {settings.format === 'jpg' && (
                    <div className="control-item">
                      <Text>质量</Text>
                      <Slider
                        min={10}
                        max={100}
                        value={settings.quality}
                        onChange={(value) => updateSettings({ quality: value })}
                      />
                    </div>
                  )}
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PhotoEditor;