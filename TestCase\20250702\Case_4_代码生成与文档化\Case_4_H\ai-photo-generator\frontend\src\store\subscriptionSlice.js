import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { message } from 'antd';
import api from '../api';
import { API_ENDPOINTS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

/**
 * 初始状态
 */
const initialState = {
  plans: [],
  userSubscription: null,
  paymentIntent: null,
  loading: false,
  error: null,
};

/**
 * 获取订阅计划列表异步action
 */
export const fetchSubscriptionPlans = createAsyncThunk(
  'subscription/fetchSubscriptionPlans',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get(API_ENDPOINTS.SUBSCRIPTION.PLANS);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 获取用户订阅信息异步action
 */
export const fetchUserSubscription = createAsyncThunk(
  'subscription/fetchUserSubscription',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get(API_ENDPOINTS.SUBSCRIPTION.USER_SUBSCRIPTION);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 创建支付意图异步action
 */
export const createPaymentIntent = createAsyncThunk(
  'subscription/createPaymentIntent',
  async (planId, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.SUBSCRIPTION.PAYMENT_INTENT, { planId });
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 订阅计划异步action
 */
export const subscribeToplan = createAsyncThunk(
  'subscription/subscribeToplan',
  async (subscriptionData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.SUBSCRIPTION.SUBSCRIBE, subscriptionData);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 取消订阅异步action
 */
export const cancelSubscription = createAsyncThunk(
  'subscription/cancelSubscription',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.SUBSCRIPTION.CANCEL);
      return response.data;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || ERROR_MESSAGES.NETWORK_ERROR
      );
    }
  }
);

/**
 * 订阅slice
 */
const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearPaymentIntent: (state) => {
      state.paymentIntent = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取订阅计划列表
      .addCase(fetchSubscriptionPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSubscriptionPlans.fulfilled, (state, action) => {
        state.loading = false;
        state.plans = action.payload.plans;
      })
      .addCase(fetchSubscriptionPlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 获取用户订阅信息
      .addCase(fetchUserSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.userSubscription = action.payload.subscription;
      })
      .addCase(fetchUserSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // 创建支付意图
      .addCase(createPaymentIntent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPaymentIntent.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentIntent = action.payload.paymentIntent;
      })
      .addCase(createPaymentIntent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 订阅计划
      .addCase(subscribeToplan.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(subscribeToplan.fulfilled, (state, action) => {
        state.loading = false;
        state.userSubscription = action.payload.subscription;
        message.success(SUCCESS_MESSAGES.SUBSCRIPTION_SUCCESS);
      })
      .addCase(subscribeToplan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      })
      
      // 取消订阅
      .addCase(cancelSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(cancelSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.userSubscription = action.payload.subscription;
        message.success(SUCCESS_MESSAGES.SUBSCRIPTION_CANCEL_SUCCESS);
      })
      .addCase(cancelSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        message.error(action.payload);
      });
  },
});

export const { clearError, clearPaymentIntent } = subscriptionSlice.actions;

export default subscriptionSlice.reducer;